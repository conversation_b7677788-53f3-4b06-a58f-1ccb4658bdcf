import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';

class ContentUnlockingService {
  final StorageService _storageService;
  final Uuid _uuid = const Uuid();
  final Random _random = Random();
  
  static const String _defaultUserId = 'default_user';

  ContentUnlockingService(this._storageService);

  // 检查并处理内容解锁
  Future<List<UnlockableContent>> checkAndProcessUnlocks({
    required String characterId,
    Map<String, dynamic>? context,
  }) async {
    final unlockedContent = <UnlockableContent>[];
    
    // 获取所有可解锁内容
    final allContent = await _storageService.getUnlockableContent(characterId);
    
    // 获取已解锁的内容ID
    final unlockedIds = await _getUnlockedContentIds(characterId);
    
    // 筛选未解锁的内容
    final lockedContent = allContent.where((content) => 
        !unlockedIds.contains(content.id) && content.isActive).toList();

    for (final content in lockedContent) {
      final progress = await _evaluateUnlockConditions(content, characterId, context);
      
      if (progress.overallProgress >= 1.0) {
        // 解锁内容
        await _unlockContent(content, characterId, context ?? {});
        unlockedContent.add(content);
        
        // 记录解锁事件
        await _recordUnlockEvent(content, characterId, EventType.contentUnlocked);
        
        // 发送解锁通知
        await _sendUnlockNotification(content, characterId);
      } else {
        // 更新进度
        await _storageService.saveUnlockProgress(progress);
        
        // 检查是否接近解锁
        if (progress.overallProgress >= 0.8) {
          await _sendNearUnlockNotification(content, characterId, progress);
        }
      }
    }

    return unlockedContent;
  }

  // 评估解锁条件
  Future<UnlockProgress> _evaluateUnlockConditions(
    UnlockableContent content,
    String characterId,
    Map<String, dynamic>? context,
  ) async {
    final conditionProgress = <String, double>{};
    final completedConditions = <String>[];
    final pendingConditions = <String>[];
    
    double totalWeight = 0.0;
    double achievedWeight = 0.0;

    for (final condition in content.unlockConditions) {
      final progress = await _evaluateCondition(condition, characterId, context);
      conditionProgress[condition.id] = progress;
      
      totalWeight += condition.weight;
      achievedWeight += progress * condition.weight;
      
      if (progress >= 1.0) {
        completedConditions.add(condition.id);
      } else {
        pendingConditions.add(condition.id);
      }
    }

    final overallProgress = totalWeight > 0 ? achievedWeight / totalWeight : 0.0;

    return UnlockProgress(
      userId: _defaultUserId,
      characterId: characterId,
      contentId: content.id,
      conditionProgress: conditionProgress,
      overallProgress: overallProgress,
      lastUpdated: DateTime.now(),
      completedConditions: completedConditions,
      pendingConditions: pendingConditions,
    );
  }

  // 评估单个条件
  Future<double> _evaluateCondition(
    UnlockCondition condition,
    String characterId,
    Map<String, dynamic>? context,
  ) async {
    switch (condition.type) {
      case ConditionType.intimacyLevel:
        return await _evaluateIntimacyCondition(condition, characterId);
      case ConditionType.conversationCount:
        return await _evaluateConversationCountCondition(condition, characterId);
      case ConditionType.timeSpent:
        return await _evaluateTimeSpentCondition(condition, characterId);
      case ConditionType.emotionState:
        return await _evaluateEmotionCondition(condition, characterId);
      case ConditionType.specificDialogue:
        return await _evaluateDialogueCondition(condition, characterId, context);
      case ConditionType.dateTime:
        return _evaluateDateTimeCondition(condition);
      case ConditionType.userBehavior:
        return await _evaluateUserBehaviorCondition(condition, characterId, context);
      case ConditionType.memoryCount:
        return await _evaluateMemoryCountCondition(condition, characterId);
      case ConditionType.achievementUnlock:
        return await _evaluateAchievementCondition(condition, characterId);
      case ConditionType.seasonalEvent:
        return _evaluateSeasonalCondition(condition);
      case ConditionType.randomChance:
        return _evaluateRandomCondition(condition);
      case ConditionType.combinationLock:
        return await _evaluateCombinationCondition(condition, characterId, context);
    }
  }

  Future<double> _evaluateIntimacyCondition(UnlockCondition condition, String characterId) async {
    final character = await _storageService.getCharacter(characterId);
    if (character == null) return 0.0;

    final requiredLevel = condition.parameters['level'] as int? ?? 0;
    return character.intimacyLevel >= requiredLevel ? 1.0 : character.intimacyLevel / requiredLevel;
  }

  Future<double> _evaluateConversationCountCondition(UnlockCondition condition, String characterId) async {
    final conversations = await _storageService.getConversationsByCharacter(characterId);
    final requiredCount = condition.parameters['count'] as int? ?? 0;
    
    return conversations.length >= requiredCount ? 1.0 : conversations.length / requiredCount;
  }

  Future<double> _evaluateTimeSpentCondition(UnlockCondition condition, String characterId) async {
    final conversations = await _storageService.getConversationsByCharacter(characterId);
    final totalMinutes = conversations.fold<int>(0, (sum, conv) => 
        sum + DateTime.now().difference(conv.createdAt).inMinutes);
    
    final requiredMinutes = condition.parameters['minutes'] as int? ?? 0;
    return totalMinutes >= requiredMinutes ? 1.0 : totalMinutes / requiredMinutes;
  }

  Future<double> _evaluateEmotionCondition(UnlockCondition condition, String characterId) async {
    final emotionStates = await _storageService.getEmotionalStates(_defaultUserId, characterId);
    if (emotionStates.isEmpty) return 0.0;

    final requiredEmotion = condition.parameters['emotion'] as String?;
    final requiredIntensity = condition.parameters['intensity'] as double? ?? 0.5;
    
    final recentState = emotionStates.first;
    final emotionValue = recentState.emotions[EmotionType.values.firstWhere(
      (e) => e.name == requiredEmotion,
      orElse: () => EmotionType.joy,
    )] ?? 0.0;

    return emotionValue >= requiredIntensity ? 1.0 : emotionValue / requiredIntensity;
  }

  Future<double> _evaluateDialogueCondition(
    UnlockCondition condition,
    String characterId,
    Map<String, dynamic>? context,
  ) async {
    final requiredKeywords = condition.parameters['keywords'] as List<String>? ?? [];
    final currentMessage = context?['message'] as String? ?? '';
    
    if (requiredKeywords.isEmpty) return 0.0;
    
    final foundKeywords = requiredKeywords.where((keyword) => 
        currentMessage.toLowerCase().contains(keyword.toLowerCase())).length;
    
    return foundKeywords / requiredKeywords.length;
  }

  double _evaluateDateTimeCondition(UnlockCondition condition) {
    final now = DateTime.now();
    final targetDate = DateTime.tryParse(condition.parameters['date'] as String? ?? '');
    
    if (targetDate == null) return 0.0;
    
    final targetHour = condition.parameters['hour'] as int?;
    final targetMinute = condition.parameters['minute'] as int?;
    
    bool dateMatch = now.year == targetDate.year && 
                    now.month == targetDate.month && 
                    now.day == targetDate.day;
    
    bool timeMatch = true;
    if (targetHour != null) {
      timeMatch = timeMatch && now.hour == targetHour;
    }
    if (targetMinute != null) {
      timeMatch = timeMatch && now.minute == targetMinute;
    }
    
    return (dateMatch && timeMatch) ? 1.0 : 0.0;
  }

  Future<double> _evaluateUserBehaviorCondition(
    UnlockCondition condition,
    String characterId,
    Map<String, dynamic>? context,
  ) async {
    final behaviorType = condition.parameters['behavior'] as String?;
    final requiredCount = condition.parameters['count'] as int? ?? 1;
    
    // 这里可以根据具体的行为类型来评估
    // 例如：点击次数、停留时间、特定操作等
    final currentCount = context?['${behaviorType}_count'] as int? ?? 0;
    
    return currentCount >= requiredCount ? 1.0 : currentCount / requiredCount;
  }

  Future<double> _evaluateMemoryCountCondition(UnlockCondition condition, String characterId) async {
    final memories = await _storageService.getCharacterMemories(characterId, _defaultUserId);
    final requiredCount = condition.parameters['count'] as int? ?? 0;
    final memoryType = condition.parameters['type'] as String?;
    
    int relevantMemories = memories.length;
    if (memoryType != null) {
      final targetType = MemoryType.values.firstWhere(
        (t) => t.name == memoryType,
        orElse: () => MemoryType.conversation,
      );
      relevantMemories = memories.where((m) => m.type == targetType).length;
    }
    
    return relevantMemories >= requiredCount ? 1.0 : relevantMemories / requiredCount;
  }

  Future<double> _evaluateAchievementCondition(UnlockCondition condition, String characterId) async {
    final achievementId = condition.parameters['achievementId'] as String?;
    if (achievementId == null) return 0.0;
    
    // 检查成就是否已解锁
    final unlocks = await _storageService.getContentUnlocks(_defaultUserId, characterId);
    final achievementUnlocked = unlocks.any((unlock) => unlock.contentId == achievementId);
    
    return achievementUnlocked ? 1.0 : 0.0;
  }

  double _evaluateSeasonalCondition(UnlockCondition condition) {
    final now = DateTime.now();
    final targetSeason = condition.parameters['season'] as String?;
    
    String currentSeason;
    if (now.month >= 3 && now.month <= 5) {
      currentSeason = 'spring';
    } else if (now.month >= 6 && now.month <= 8) {
      currentSeason = 'summer';
    } else if (now.month >= 9 && now.month <= 11) {
      currentSeason = 'autumn';
    } else {
      currentSeason = 'winter';
    }
    
    return currentSeason == targetSeason ? 1.0 : 0.0;
  }

  double _evaluateRandomCondition(UnlockCondition condition) {
    final chance = condition.parameters['chance'] as double? ?? 0.1;
    return _random.nextDouble() < chance ? 1.0 : 0.0;
  }

  Future<double> _evaluateCombinationCondition(
    UnlockCondition condition,
    String characterId,
    Map<String, dynamic>? context,
  ) async {
    final requiredConditions = condition.parameters['conditions'] as List<String>? ?? [];
    final operator = condition.parameters['operator'] as String? ?? 'and';
    
    final results = <double>[];
    for (final conditionId in requiredConditions) {
      // 这里需要递归评估子条件
      // 简化实现，假设子条件已经满足
      results.add(1.0);
    }
    
    if (operator == 'and') {
      return results.every((r) => r >= 1.0) ? 1.0 : results.reduce((a, b) => a * b);
    } else if (operator == 'or') {
      return results.any((r) => r >= 1.0) ? 1.0 : results.reduce((a, b) => a > b ? a : b);
    }
    
    return 0.0;
  }

  // 解锁内容
  Future<void> _unlockContent(
    UnlockableContent content,
    String characterId,
    Map<String, dynamic> context,
  ) async {
    final unlock = ContentUnlock(
      id: _uuid.v4(),
      userId: _defaultUserId,
      contentId: content.id,
      unlockedAt: DateTime.now(),
      trigger: UnlockTrigger(
        type: TriggerType.automatic,
        description: '满足解锁条件',
        context: context,
      ),
      unlockContext: context,
    );

    await _storageService.saveContentUnlock(unlock);
  }

  // 获取已解锁的内容ID
  Future<Set<String>> _getUnlockedContentIds(String characterId) async {
    final unlocks = await _storageService.getContentUnlocks(_defaultUserId, characterId);
    return unlocks.map((unlock) => unlock.contentId).toSet();
  }

  // 记录解锁事件
  Future<void> _recordUnlockEvent(
    UnlockableContent content,
    String characterId,
    EventType eventType,
  ) async {
    final event = UnlockEvent(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      contentId: content.id,
      eventType: eventType,
      timestamp: DateTime.now(),
      description: '${content.title} - ${eventType.name}',
    );

    await _storageService.saveUnlockEvent(event);
  }

  // 发送解锁通知
  Future<void> _sendUnlockNotification(UnlockableContent content, String characterId) async {
    final notification = UnlockNotification(
      id: _uuid.v4(),
      userId: _defaultUserId,
      contentId: content.id,
      notificationType: NotificationType.newUnlock,
      title: '🎉 新内容解锁！',
      message: '您解锁了新的${_getContentTypeLabel(content.type)}：${content.title}',
      createdAt: DateTime.now(),
      actionUrl: '/content/${content.id}',
    );

    await _storageService.saveUnlockNotification(notification);
  }

  // 发送即将解锁通知
  Future<void> _sendNearUnlockNotification(
    UnlockableContent content,
    String characterId,
    UnlockProgress progress,
  ) async {
    final notification = UnlockNotification(
      id: _uuid.v4(),
      userId: _defaultUserId,
      contentId: content.id,
      notificationType: NotificationType.nearUnlock,
      title: '🔓 即将解锁',
      message: '${content.title} 解锁进度已达到 ${(progress.overallProgress * 100).toInt()}%',
      createdAt: DateTime.now(),
      actionUrl: '/content/${content.id}',
    );

    await _storageService.saveUnlockNotification(notification);
  }

  // 获取内容列表
  Future<List<UnlockableContent>> getContent({
    required String characterId,
    ContentFilter? filter,
  }) async {
    var content = await _storageService.getUnlockableContent(characterId);
    
    if (filter != null) {
      content = await _applyContentFilter(content, characterId, filter);
    }
    
    return content;
  }

  // 应用内容过滤器
  Future<List<UnlockableContent>> _applyContentFilter(
    List<UnlockableContent> content,
    String characterId,
    ContentFilter filter,
  ) async {
    var filtered = content.where((item) => item.isActive);

    // 类型过滤
    if (filter.types.isNotEmpty) {
      filtered = filtered.where((item) => filter.types.contains(item.type));
    }

    // 稀有度过滤
    if (filter.rarities.isNotEmpty) {
      filtered = filtered.where((item) => filter.rarities.contains(item.rarity));
    }

    // 标签过滤
    if (filter.tags.isNotEmpty) {
      filtered = filtered.where((item) => 
          filter.tags.any((tag) => item.tags.contains(tag)));
    }

    // 搜索查询
    if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
      final query = filter.searchQuery!.toLowerCase();
      filtered = filtered.where((item) => 
          item.title.toLowerCase().contains(query) ||
          item.description.toLowerCase().contains(query) ||
          item.tags.any((tag) => tag.toLowerCase().contains(query)));
    }

    // 解锁状态过滤
    if (filter.unlockStatus != UnlockStatus.all) {
      final unlockedIds = await _getUnlockedContentIds(characterId);
      
      switch (filter.unlockStatus) {
        case UnlockStatus.unlocked:
          filtered = filtered.where((item) => unlockedIds.contains(item.id));
          break;
        case UnlockStatus.locked:
          filtered = filtered.where((item) => !unlockedIds.contains(item.id));
          break;
        case UnlockStatus.nearUnlock:
          // 需要检查进度
          final nearUnlockIds = <String>{};
          for (final item in filtered) {
            if (!unlockedIds.contains(item.id)) {
              final progress = await _storageService.getUnlockProgress(_defaultUserId, characterId, item.id);
              if (progress != null && progress.overallProgress >= 0.8) {
                nearUnlockIds.add(item.id);
              }
            }
          }
          filtered = filtered.where((item) => nearUnlockIds.contains(item.id));
          break;
        case UnlockStatus.all:
          break;
      }
    }

    // 查看状态过滤
    if (filter.viewStatus != ViewStatus.all) {
      final unlocks = await _storageService.getContentUnlocks(_defaultUserId, characterId);
      final viewedIds = unlocks.where((unlock) => unlock.isViewed)
          .map((unlock) => unlock.contentId).toSet();
      
      switch (filter.viewStatus) {
        case ViewStatus.viewed:
          filtered = filtered.where((item) => viewedIds.contains(item.id));
          break;
        case ViewStatus.unviewed:
          filtered = filtered.where((item) => !viewedIds.contains(item.id));
          break;
        case ViewStatus.all:
          break;
      }
    }

    // 排序
    var result = filtered.toList();
    switch (filter.sortBy) {
      case SortBy.unlockOrder:
        result.sort((a, b) => a.unlockOrder.compareTo(b.unlockOrder));
        break;
      case SortBy.unlockDate:
        // 需要获取解锁日期
        break;
      case SortBy.rarity:
        result.sort((a, b) => a.rarity.index.compareTo(b.rarity.index));
        break;
      case SortBy.rating:
        result.sort((a, b) => a.userRating.compareTo(b.userRating));
        break;
      case SortBy.viewCount:
        result.sort((a, b) => a.viewCount.compareTo(b.viewCount));
        break;
      case SortBy.name:
        result.sort((a, b) => a.title.compareTo(b.title));
        break;
    }

    if (filter.sortOrder == SortOrder.descending) {
      result = result.reversed.toList();
    }

    return result;
  }

  // 查看内容
  Future<void> viewContent(String contentId, String characterId) async {
    final unlock = await _storageService.getContentUnlock(_defaultUserId, contentId);
    if (unlock == null) return;

    final updatedUnlock = unlock.copyWith(
      isViewed: true,
      viewedAt: DateTime.now(),
      viewCount: unlock.viewCount + 1,
    );

    await _storageService.saveContentUnlock(updatedUnlock);
    
    // 记录查看事件
    final event = UnlockEvent(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      contentId: contentId,
      eventType: EventType.contentViewed,
      timestamp: DateTime.now(),
    );

    await _storageService.saveUnlockEvent(event);
  }

  // 生成内容推荐
  Future<List<ContentRecommendation>> generateRecommendations(String characterId) async {
    final recommendations = <ContentRecommendation>[];
    
    // 即将解锁的内容
    final nearUnlockRecs = await _generateNearUnlockRecommendations(characterId);
    recommendations.addAll(nearUnlockRecs);
    
    // 基于用户偏好的推荐
    final preferenceRecs = await _generatePreferenceRecommendations(characterId);
    recommendations.addAll(preferenceRecs);
    
    // 热门内容推荐
    final trendingRecs = await _generateTrendingRecommendations(characterId);
    recommendations.addAll(trendingRecs);

    return recommendations;
  }

  Future<List<ContentRecommendation>> _generateNearUnlockRecommendations(String characterId) async {
    final recommendations = <ContentRecommendation>[];
    final content = await _storageService.getUnlockableContent(characterId);
    final unlockedIds = await _getUnlockedContentIds(characterId);
    
    for (final item in content) {
      if (!unlockedIds.contains(item.id)) {
        final progress = await _storageService.getUnlockProgress(_defaultUserId, characterId, item.id);
        if (progress != null && progress.overallProgress >= 0.7) {
          recommendations.add(ContentRecommendation(
            id: _uuid.v4(),
            userId: _defaultUserId,
            contentId: item.id,
            reason: RecommendationReason.nearUnlock,
            confidence: progress.overallProgress,
            description: '您已完成 ${(progress.overallProgress * 100).toInt()}% 的解锁条件',
            generatedAt: DateTime.now(),
          ));
        }
      }
    }
    
    return recommendations;
  }

  Future<List<ContentRecommendation>> _generatePreferenceRecommendations(String characterId) async {
    // 基于用户查看历史和评分生成推荐
    final recommendations = <ContentRecommendation>[];
    // 实现用户偏好分析逻辑
    return recommendations;
  }

  Future<List<ContentRecommendation>> _generateTrendingRecommendations(String characterId) async {
    // 基于热门内容生成推荐
    final recommendations = <ContentRecommendation>[];
    // 实现热门内容分析逻辑
    return recommendations;
  }

  String _getContentTypeLabel(ContentType type) {
    switch (type) {
      case ContentType.story:
        return '故事';
      case ContentType.memory:
        return '记忆';
      case ContentType.dialogue:
        return '对话';
      case ContentType.image:
        return '图片';
      case ContentType.audio:
        return '音频';
      case ContentType.video:
        return '视频';
      case ContentType.background:
        return '背景';
      case ContentType.achievement:
        return '成就';
      case ContentType.outfit:
        return '服装';
      case ContentType.accessory:
        return '配饰';
      case ContentType.scene:
        return '场景';
      case ContentType.emotion:
        return '情感';
    }
  }
}

// Provider
final contentUnlockingServiceProvider = Provider<ContentUnlockingService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return ContentUnlockingService(storageService);
});
