import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../models/models.dart';
import '../../../modules/multi_character/group_chat_service.dart';
import '../../../services/storage_service.dart';

class CreateGroupScreen extends ConsumerStatefulWidget {
  const CreateGroupScreen({super.key});

  @override
  ConsumerState<CreateGroupScreen> createState() => _CreateGroupScreenState();
}

class _CreateGroupScreenState extends ConsumerState<CreateGroupScreen> {
  final TextEditingController _titleController = TextEditingController();
  List<Character> _availableCharacters = [];
  Set<String> _selectedCharacterIds = {};
  GroupConversationType _selectedType = GroupConversationType.casual;
  bool _isLoading = true;
  bool _isCreating = false;

  @override
  void initState() {
    super.initState();
    _loadCharacters();
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  Future<void> _loadCharacters() async {
    final storageService = ref.read(storageServiceProvider);
    final characters = await storageService.getAllCharacters();
    
    if (mounted) {
      setState(() {
        _availableCharacters = characters;
        _isLoading = false;
      });
    }
  }

  Future<void> _createGroup() async {
    if (_selectedCharacterIds.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请至少选择2个角色')),
      );
      return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      final groupChatService = ref.read(groupChatServiceProvider);
      final conversation = await groupChatService.createGroupConversation(
        characterIds: _selectedCharacterIds.toList(),
        title: _titleController.text.trim().isEmpty ? null : _titleController.text.trim(),
        type: _selectedType,
      );

      if (mounted) {
        context.go('/group-chat/${conversation.id}');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('创建失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isCreating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('创建群聊'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          TextButton(
            onPressed: _isCreating ? null : _createGroup,
            child: _isCreating
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('创建'),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 群聊名称
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: '群聊名称（可选）',
                hintText: '不填写将自动生成',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            
            // 群聊类型
            const Text(
              '群聊类型',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            _buildTypeSelector(),
            const SizedBox(height: 24),
            
            // 选择角色
            Row(
              children: [
                const Text(
                  '选择角色',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '(${_selectedCharacterIds.length}/${_availableCharacters.length})',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // 角色列表
            Expanded(
              child: _buildCharacterList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeSelector() {
    return Wrap(
      spacing: 8,
      children: GroupConversationType.values.map((type) {
        return ChoiceChip(
          label: Text(_getTypeLabel(type)),
          selected: _selectedType == type,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedType = type;
              });
            }
          },
        );
      }).toList(),
    );
  }

  Widget _buildCharacterList() {
    if (_availableCharacters.isEmpty) {
      return const Center(
        child: Text(
          '没有可用的角色',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: _availableCharacters.length,
      itemBuilder: (context, index) {
        final character = _availableCharacters[index];
        final isSelected = _selectedCharacterIds.contains(character.id);
        
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: CheckboxListTile(
            value: isSelected,
            onChanged: (selected) {
              setState(() {
                if (selected == true) {
                  _selectedCharacterIds.add(character.id);
                } else {
                  _selectedCharacterIds.remove(character.id);
                }
              });
            },
            title: Text(
              character.name,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(character.description),
            secondary: CircleAvatar(
              backgroundColor: _getCharacterColor(character.id),
              child: Text(
                character.name[0],
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            controlAffinity: ListTileControlAffinity.trailing,
          ),
        );
      },
    );
  }

  String _getTypeLabel(GroupConversationType type) {
    switch (type) {
      case GroupConversationType.casual:
        return '随意聊天';
      case GroupConversationType.debate:
        return '辩论讨论';
      case GroupConversationType.roleplay:
        return '角色扮演';
      case GroupConversationType.support:
        return '情感支持';
      case GroupConversationType.learning:
        return '学习交流';
    }
  }

  Color _getCharacterColor(String characterId) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    
    final index = characterId.hashCode % colors.length;
    return colors[index];
  }
}
