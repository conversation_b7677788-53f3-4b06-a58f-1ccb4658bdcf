import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'user_preference.freezed.dart';
part 'user_preference.g.dart';

@freezed
@HiveType(typeId: 13)
class UserPreference with _$UserPreference {
  const factory UserPreference({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required PreferenceType type,
    @HiveField(4) required String category,
    @HiveField(5) required String content,
    @HiveField(6) required double confidence,
    @HiveField(7) required DateTime discoveredAt,
    @HiveField(8) required DateTime lastUpdated,
    @HiveField(9) @Default(1) int frequency,
    @HiveField(10) @Default([]) List<String> evidenceMessageIds,
    @HiveField(11) @Default(true) bool isActive,
    @HiveField(12) Map<String, dynamic>? metadata,
  }) = _UserPreference;

  factory UserPreference.fromJson(Map<String, dynamic> json) => _$UserPreferenceFromJson(json);
}

@HiveType(typeId: 14)
enum PreferenceType {
  @HiveField(0)
  likes,        // 喜欢
  @HiveField(1)
  dislikes,     // 不喜欢
  @HiveField(2)
  interests,    // 兴趣
  @HiveField(3)
  habits,       // 习惯
  @HiveField(4)
  values,       // 价值观
  @HiveField(5)
  communication, // 沟通偏好
  @HiveField(6)
  topics,       // 话题偏好
  @HiveField(7)
  activities,   // 活动偏好
}

@freezed
@HiveType(typeId: 15)
class PreferencePattern with _$PreferencePattern {
  const factory PreferencePattern({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required String pattern,
    @HiveField(4) required String description,
    @HiveField(5) required double strength,
    @HiveField(6) required DateTime detectedAt,
    @HiveField(7) @Default([]) List<String> relatedPreferenceIds,
    @HiveField(8) @Default(0) int occurrenceCount,
    @HiveField(9) Map<String, dynamic>? context,
  }) = _PreferencePattern;

  factory PreferencePattern.fromJson(Map<String, dynamic> json) => _$PreferencePatternFromJson(json);
}

@freezed
@HiveType(typeId: 16)
class UserBehaviorAnalysis with _$UserBehaviorAnalysis {
  const factory UserBehaviorAnalysis({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required DateTime analysisDate,
    @HiveField(4) required Map<String, double> topicInterests,
    @HiveField(5) required Map<String, double> communicationPatterns,
    @HiveField(6) required Map<String, double> emotionalResponses,
    @HiveField(7) required Map<String, int> activityFrequency,
    @HiveField(8) required double engagementScore,
    @HiveField(9) required List<String> preferredTopics,
    @HiveField(10) required List<String> avoidedTopics,
    @HiveField(11) Map<String, dynamic>? insights,
  }) = _UserBehaviorAnalysis;

  factory UserBehaviorAnalysis.fromJson(Map<String, dynamic> json) => _$UserBehaviorAnalysisFromJson(json);
}

@freezed
@HiveType(typeId: 17)
class ConversationInsight with _$ConversationInsight {
  const factory ConversationInsight({
    @HiveField(0) required String id,
    @HiveField(1) required String conversationId,
    @HiveField(2) required String userId,
    @HiveField(3) required String characterId,
    @HiveField(4) required DateTime timestamp,
    @HiveField(5) required InsightType type,
    @HiveField(6) required String insight,
    @HiveField(7) required double confidence,
    @HiveField(8) @Default([]) List<String> supportingMessageIds,
    @HiveField(9) @Default(false) bool isApplied,
    @HiveField(10) Map<String, dynamic>? actionData,
  }) = _ConversationInsight;

  factory ConversationInsight.fromJson(Map<String, dynamic> json) => _$ConversationInsightFromJson(json);
}

@HiveType(typeId: 18)
enum InsightType {
  @HiveField(0)
  preferenceDiscovered,  // 发现新偏好
  @HiveField(1)
  behaviorPattern,       // 行为模式
  @HiveField(2)
  emotionalState,        // 情感状态
  @HiveField(3)
  topicInterest,         // 话题兴趣
  @HiveField(4)
  communicationStyle,    // 沟通风格
  @HiveField(5)
  relationshipChange,    // 关系变化
}
