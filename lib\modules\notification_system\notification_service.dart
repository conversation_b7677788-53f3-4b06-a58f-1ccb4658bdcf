import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';

class NotificationService {
  final StorageService _storageService;
  final Uuid _uuid = const Uuid();
  
  static const String _defaultUserId = 'default_user';

  NotificationService(this._storageService);

  // 发送通知
  Future<AppNotification> sendNotification({
    required NotificationType type,
    required String title,
    required String body,
    String? characterId,
    String? imageUrl,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic> data = const {},
    String? actionUrl,
    List<NotificationAction> actions = const [],
    DateTime? expiresAt,
    List<String> tags = const [],
  }) async {
    // 检查通知设置
    final settings = await getNotificationSettings();
    if (!settings.enabled) {
      throw Exception('通知已被禁用');
    }

    if (!(settings.typeSettings[type] ?? true)) {
      throw Exception('此类型通知已被禁用');
    }

    // 检查优先级过滤
    if (priority.index < settings.minPriority.index) {
      throw Exception('通知优先级过低');
    }

    // 检查免打扰时间
    if (_isInQuietHours(settings)) {
      if (priority != NotificationPriority.urgent) {
        throw Exception('当前为免打扰时间');
      }
    }

    // 检查每日通知限制
    final todayCount = await _getTodayNotificationCount();
    if (todayCount >= settings.maxNotificationsPerDay) {
      if (priority != NotificationPriority.urgent) {
        throw Exception('今日通知数量已达上限');
      }
    }

    final notification = AppNotification(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      type: type,
      title: title,
      body: body,
      imageUrl: imageUrl,
      createdAt: DateTime.now(),
      priority: priority,
      data: data,
      actionUrl: actionUrl,
      actions: actions,
      expiresAt: expiresAt,
      tags: tags,
    );

    await _storageService.saveNotification(notification);
    
    // 触发系统通知
    await _triggerSystemNotification(notification);
    
    // 更新分析数据
    await _updateAnalytics(notification);

    return notification;
  }

  // 计划通知
  Future<AppNotification> scheduleNotification({
    required NotificationType type,
    required String title,
    required String body,
    required DateTime scheduledAt,
    String? characterId,
    String? imageUrl,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic> data = const {},
    String? actionUrl,
    List<NotificationAction> actions = const [],
    DateTime? expiresAt,
    List<String> tags = const [],
  }) async {
    final notification = AppNotification(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      type: type,
      title: title,
      body: body,
      imageUrl: imageUrl,
      createdAt: DateTime.now(),
      scheduledAt: scheduledAt,
      priority: priority,
      data: data,
      actionUrl: actionUrl,
      actions: actions,
      expiresAt: expiresAt,
      tags: tags,
    );

    await _storageService.saveNotification(notification);
    
    // 注册系统计划通知
    await _scheduleSystemNotification(notification);

    return notification;
  }

  // 标记通知为已读
  Future<void> markAsRead(String notificationId) async {
    final notification = await _storageService.getNotification(notificationId);
    if (notification == null) return;

    final updatedNotification = notification.copyWith(isRead: true);
    await _storageService.saveNotification(updatedNotification);
    
    // 更新分析数据
    await _updateReadAnalytics(notification);
  }

  // 批量标记为已读
  Future<void> markAllAsRead({NotificationType? type, String? characterId}) async {
    final notifications = await getNotifications(
      type: type,
      characterId: characterId,
      isRead: false,
    );

    for (final notification in notifications) {
      await markAsRead(notification.id);
    }
  }

  // 删除通知
  Future<void> deleteNotification(String notificationId) async {
    await _storageService.deleteNotification(notificationId);
  }

  // 获取通知列表
  Future<List<AppNotification>> getNotifications({
    NotificationType? type,
    String? characterId,
    bool? isRead,
    NotificationPriority? priority,
    int limit = 50,
  }) async {
    return await _storageService.getNotifications(
      userId: _defaultUserId,
      type: type,
      characterId: characterId,
      isRead: isRead,
      priority: priority,
      limit: limit,
    );
  }

  // 获取未读通知数量
  Future<int> getUnreadCount({NotificationType? type, String? characterId}) async {
    final notifications = await getNotifications(
      type: type,
      characterId: characterId,
      isRead: false,
    );
    return notifications.length;
  }

  // 获取通知设置
  Future<NotificationSettings> getNotificationSettings() async {
    var settings = await _storageService.getNotificationSettings(_defaultUserId);
    
    if (settings == null) {
      // 创建默认设置
      settings = NotificationSettings(
        userId: _defaultUserId,
        typeSettings: {
          for (final type in NotificationType.values) type: true,
        },
      );
      await _storageService.saveNotificationSettings(settings);
    }
    
    return settings;
  }

  // 更新通知设置
  Future<void> updateNotificationSettings(NotificationSettings settings) async {
    final updatedSettings = settings.copyWith(updatedAt: DateTime.now());
    await _storageService.saveNotificationSettings(updatedSettings);
  }

  // 创建通知模板
  Future<NotificationTemplate> createTemplate({
    required NotificationType type,
    required String titleTemplate,
    required String bodyTemplate,
    Map<String, String> variables = const {},
    NotificationPriority priority = NotificationPriority.normal,
    List<NotificationAction> defaultActions = const [],
    String? imageUrl,
    List<String> tags = const [],
  }) async {
    final template = NotificationTemplate(
      id: _uuid.v4(),
      type: type,
      titleTemplate: titleTemplate,
      bodyTemplate: bodyTemplate,
      variables: variables,
      priority: priority,
      defaultActions: defaultActions,
      imageUrl: imageUrl,
      tags: tags,
    );

    await _storageService.saveNotificationTemplate(template);
    return template;
  }

  // 从模板发送通知
  Future<AppNotification> sendFromTemplate({
    required String templateId,
    required Map<String, String> variables,
    String? characterId,
    Map<String, dynamic> additionalData = const {},
  }) async {
    final template = await _storageService.getNotificationTemplate(templateId);
    if (template == null) {
      throw Exception('通知模板不存在');
    }

    if (!template.enabled) {
      throw Exception('通知模板已禁用');
    }

    // 替换模板变量
    String title = template.titleTemplate;
    String body = template.bodyTemplate;
    
    for (final entry in variables.entries) {
      title = title.replaceAll('{{${entry.key}}}', entry.value);
      body = body.replaceAll('{{${entry.key}}}', entry.value);
    }

    return await sendNotification(
      type: template.type,
      title: title,
      body: body,
      characterId: characterId,
      imageUrl: template.imageUrl,
      priority: template.priority,
      data: additionalData,
      actions: template.defaultActions,
      tags: template.tags,
    );
  }

  // 创建通知规则
  Future<NotificationRule> createRule({
    required String name,
    required RuleTrigger trigger,
    required List<RuleCondition> conditions,
    required RuleAction action,
    int priority = 0,
  }) async {
    final rule = NotificationRule(
      id: _uuid.v4(),
      userId: _defaultUserId,
      name: name,
      trigger: trigger,
      conditions: conditions,
      action: action,
      priority: priority,
      createdAt: DateTime.now(),
    );

    await _storageService.saveNotificationRule(rule);
    return rule;
  }

  // 处理规则触发
  Future<void> processRuleTrigger(TriggerType triggerType, Map<String, dynamic> context) async {
    final rules = await _storageService.getNotificationRules(_defaultUserId);
    
    final applicableRules = rules.where((rule) => 
        rule.enabled && rule.trigger.type == triggerType).toList()
      ..sort((a, b) => b.priority.compareTo(a.priority));

    for (final rule in applicableRules) {
      if (await _evaluateConditions(rule.conditions, context)) {
        await _executeRuleAction(rule, context);
        
        // 更新规则统计
        final updatedRule = rule.copyWith(
          lastTriggered: DateTime.now(),
          triggerCount: rule.triggerCount + 1,
        );
        await _storageService.saveNotificationRule(updatedRule);
      }
    }
  }

  // 智能通知建议
  Future<List<SmartNotificationSuggestion>> generateSmartSuggestions() async {
    final suggestions = <SmartNotificationSuggestion>[];
    
    // 分析用户行为模式
    final analytics = await _analyzeUserBehavior();
    
    // 免打扰时间建议
    final quietHoursSuggestion = await _suggestQuietHours(analytics);
    if (quietHoursSuggestion != null) {
      suggestions.add(quietHoursSuggestion);
    }

    // 通知类型优化建议
    final typeOptimization = await _suggestTypeOptimization(analytics);
    suggestions.addAll(typeOptimization);

    // 批量通知建议
    final batchSuggestion = await _suggestBatching(analytics);
    if (batchSuggestion != null) {
      suggestions.add(batchSuggestion);
    }

    // 保存建议
    for (final suggestion in suggestions) {
      await _storageService.saveSmartSuggestion(suggestion);
    }

    return suggestions;
  }

  // 应用智能建议
  Future<void> applySmartSuggestion(String suggestionId) async {
    final suggestion = await _storageService.getSmartSuggestion(suggestionId);
    if (suggestion == null || suggestion.isApplied) return;

    switch (suggestion.type) {
      case SuggestionType.quietHours:
        await _applyQuietHoursSuggestion(suggestion);
        break;
      case SuggestionType.typeDisable:
        await _applyTypeDisableSuggestion(suggestion);
        break;
      case SuggestionType.priorityAdjust:
        await _applyPriorityAdjustSuggestion(suggestion);
        break;
      case SuggestionType.batchSimilar:
        await _applyBatchSuggestion(suggestion);
        break;
      case SuggestionType.scheduleOptimize:
        await _applyScheduleOptimizeSuggestion(suggestion);
        break;
    }

    // 标记为已应用
    final updatedSuggestion = suggestion.copyWith(isApplied: true);
    await _storageService.saveSmartSuggestion(updatedSuggestion);
  }

  // 获取通知分析数据
  Future<NotificationAnalytics> getAnalytics({DateTime? date}) async {
    final targetDate = date ?? DateTime.now();
    var analytics = await _storageService.getNotificationAnalytics(_defaultUserId, targetDate);
    
    if (analytics == null) {
      analytics = NotificationAnalytics(
        userId: _defaultUserId,
        date: targetDate,
      );
    }
    
    return analytics;
  }

  // 清理过期通知
  Future<void> cleanupExpiredNotifications() async {
    final settings = await getNotificationSettings();
    final cutoffTime = DateTime.now().subtract(Duration(hours: settings.autoDeleteAfterHours));
    
    await _storageService.deleteExpiredNotifications(_defaultUserId, cutoffTime);
  }

  // 私有方法

  bool _isInQuietHours(NotificationSettings settings) {
    final now = DateTime.now();
    final currentTime = TimeOfDay(hour: now.hour, minute: now.minute);
    final currentWeekday = now.weekday;

    for (final quietHours in settings.quietHours) {
      if (!quietHours.enabled) continue;
      
      if (quietHours.weekdays.isNotEmpty && !quietHours.weekdays.contains(currentWeekday)) {
        continue;
      }

      if (_isTimeInRange(currentTime, quietHours.startTime, quietHours.endTime)) {
        return true;
      }
    }

    return false;
  }

  bool _isTimeInRange(TimeOfDay current, TimeOfDay start, TimeOfDay end) {
    final currentMinutes = current.hour * 60 + current.minute;
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;

    if (startMinutes <= endMinutes) {
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    } else {
      // 跨越午夜的情况
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }
  }

  Future<int> _getTodayNotificationCount() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    final notifications = await _storageService.getNotificationsInRange(
      _defaultUserId,
      startOfDay,
      endOfDay,
    );
    
    return notifications.length;
  }

  Future<void> _triggerSystemNotification(AppNotification notification) async {
    // 这里会调用系统通知API
    // 在实际实现中，会使用 flutter_local_notifications 等插件
    print('系统通知: ${notification.title} - ${notification.body}');
  }

  Future<void> _scheduleSystemNotification(AppNotification notification) async {
    // 这里会调用系统计划通知API
    print('计划通知: ${notification.title} 在 ${notification.scheduledAt}');
  }

  Future<void> _updateAnalytics(AppNotification notification) async {
    final today = DateTime.now();
    final dateKey = DateTime(today.year, today.month, today.day);
    
    var analytics = await _storageService.getNotificationAnalytics(_defaultUserId, dateKey);
    
    if (analytics == null) {
      analytics = NotificationAnalytics(
        userId: _defaultUserId,
        date: dateKey,
      );
    }

    final updatedAnalytics = analytics.copyWith(
      totalSent: analytics.totalSent + 1,
      totalDelivered: analytics.totalDelivered + 1,
      typeBreakdown: {
        ...analytics.typeBreakdown,
        notification.type: (analytics.typeBreakdown[notification.type] ?? 0) + 1,
      },
      priorityBreakdown: {
        ...analytics.priorityBreakdown,
        notification.priority: (analytics.priorityBreakdown[notification.priority] ?? 0) + 1,
      },
    );

    await _storageService.saveNotificationAnalytics(updatedAnalytics);
  }

  Future<void> _updateReadAnalytics(AppNotification notification) async {
    final today = DateTime.now();
    final dateKey = DateTime(today.year, today.month, today.day);
    
    var analytics = await _storageService.getNotificationAnalytics(_defaultUserId, dateKey);
    if (analytics == null) return;

    final responseTime = DateTime.now().difference(notification.createdAt).inMinutes.toDouble();
    final newAverageResponseTime = analytics.totalRead > 0
        ? (analytics.averageResponseTime * analytics.totalRead + responseTime) / (analytics.totalRead + 1)
        : responseTime;

    final updatedAnalytics = analytics.copyWith(
      totalRead: analytics.totalRead + 1,
      averageResponseTime: newAverageResponseTime,
    );

    await _storageService.saveNotificationAnalytics(updatedAnalytics);
  }

  Future<bool> _evaluateConditions(List<RuleCondition> conditions, Map<String, dynamic> context) async {
    if (conditions.isEmpty) return true;

    bool result = true;
    LogicalOperator? lastOperator;

    for (final condition in conditions) {
      final conditionResult = _evaluateCondition(condition, context);
      
      if (lastOperator == null) {
        result = conditionResult;
      } else if (lastOperator == LogicalOperator.and) {
        result = result && conditionResult;
      } else if (lastOperator == LogicalOperator.or) {
        result = result || conditionResult;
      }
      
      lastOperator = condition.logicalOperator;
    }

    return result;
  }

  bool _evaluateCondition(RuleCondition condition, Map<String, dynamic> context) {
    final fieldValue = context[condition.field];
    final conditionValue = condition.value;

    switch (condition.operator) {
      case ConditionOperator.equals:
        return fieldValue == conditionValue;
      case ConditionOperator.notEquals:
        return fieldValue != conditionValue;
      case ConditionOperator.greaterThan:
        return (fieldValue as num) > (conditionValue as num);
      case ConditionOperator.lessThan:
        return (fieldValue as num) < (conditionValue as num);
      case ConditionOperator.contains:
        return fieldValue.toString().contains(conditionValue.toString());
      case ConditionOperator.notContains:
        return !fieldValue.toString().contains(conditionValue.toString());
      case ConditionOperator.startsWith:
        return fieldValue.toString().startsWith(conditionValue.toString());
      case ConditionOperator.endsWith:
        return fieldValue.toString().endsWith(conditionValue.toString());
    }
  }

  Future<void> _executeRuleAction(NotificationRule rule, Map<String, dynamic> context) async {
    switch (rule.action.type) {
      case RuleActionType.sendNotification:
        await _executeSendNotificationAction(rule.action.parameters, context);
        break;
      case RuleActionType.scheduleNotification:
        await _executeScheduleNotificationAction(rule.action.parameters, context);
        break;
      case RuleActionType.cancelNotification:
        await _executeCancelNotificationAction(rule.action.parameters, context);
        break;
      case RuleActionType.updateSettings:
        await _executeUpdateSettingsAction(rule.action.parameters, context);
        break;
      case RuleActionType.triggerAction:
        await _executeTriggerAction(rule.action.parameters, context);
        break;
    }
  }

  Future<void> _executeSendNotificationAction(Map<String, dynamic> parameters, Map<String, dynamic> context) async {
    await sendNotification(
      type: NotificationType.values.firstWhere((t) => t.name == parameters['type']),
      title: parameters['title'] ?? '通知',
      body: parameters['body'] ?? '',
      characterId: context['characterId'],
      priority: NotificationPriority.values.firstWhere(
        (p) => p.name == parameters['priority'],
        orElse: () => NotificationPriority.normal,
      ),
    );
  }

  Future<void> _executeScheduleNotificationAction(Map<String, dynamic> parameters, Map<String, dynamic> context) async {
    final delayMinutes = parameters['delayMinutes'] as int? ?? 60;
    final scheduledAt = DateTime.now().add(Duration(minutes: delayMinutes));

    await scheduleNotification(
      type: NotificationType.values.firstWhere((t) => t.name == parameters['type']),
      title: parameters['title'] ?? '计划通知',
      body: parameters['body'] ?? '',
      scheduledAt: scheduledAt,
      characterId: context['characterId'],
    );
  }

  Future<void> _executeCancelNotificationAction(Map<String, dynamic> parameters, Map<String, dynamic> context) async {
    final notificationId = parameters['notificationId'] as String?;
    if (notificationId != null) {
      await deleteNotification(notificationId);
    }
  }

  Future<void> _executeUpdateSettingsAction(Map<String, dynamic> parameters, Map<String, dynamic> context) async {
    final settings = await getNotificationSettings();
    // 根据参数更新设置
    await updateNotificationSettings(settings);
  }

  Future<void> _executeTriggerAction(Map<String, dynamic> parameters, Map<String, dynamic> context) async {
    // 执行自定义动作
    final actionType = parameters['actionType'] as String?;
    print('执行自定义动作: $actionType');
  }

  Future<Map<String, dynamic>> _analyzeUserBehavior() async {
    // 分析用户通知行为模式
    final notifications = await getNotifications(limit: 100);
    
    final readTimes = <int>[];
    final dismissTimes = <int>[];
    
    for (final notification in notifications) {
      if (notification.isRead) {
        readTimes.add(notification.createdAt.hour);
      }
    }

    return {
      'readTimes': readTimes,
      'dismissTimes': dismissTimes,
      'totalNotifications': notifications.length,
      'readRate': notifications.where((n) => n.isRead).length / notifications.length,
    };
  }

  Future<SmartNotificationSuggestion?> _suggestQuietHours(Map<String, dynamic> analytics) async {
    final readTimes = analytics['readTimes'] as List<int>;
    if (readTimes.length < 10) return null;

    // 分析用户很少查看通知的时间段
    final hourCounts = <int, int>{};
    for (final hour in readTimes) {
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
    }

    final lowActivityHours = hourCounts.entries
        .where((e) => e.value < readTimes.length * 0.1)
        .map((e) => e.key)
        .toList();

    if (lowActivityHours.length >= 4) {
      return SmartNotificationSuggestion(
        id: _uuid.v4(),
        userId: _defaultUserId,
        type: SuggestionType.quietHours,
        title: '建议设置免打扰时间',
        description: '根据您的使用习惯，建议在${lowActivityHours.first}:00-${lowActivityHours.last}:00设置免打扰',
        confidence: 0.8,
        data: {
          'startHour': lowActivityHours.first,
          'endHour': lowActivityHours.last,
        },
        generatedAt: DateTime.now(),
      );
    }

    return null;
  }

  Future<List<SmartNotificationSuggestion>> _suggestTypeOptimization(Map<String, dynamic> analytics) async {
    final suggestions = <SmartNotificationSuggestion>[];
    
    // 基于阅读率建议禁用某些类型的通知
    final readRate = analytics['readRate'] as double;
    if (readRate < 0.3) {
      suggestions.add(SmartNotificationSuggestion(
        id: _uuid.v4(),
        userId: _defaultUserId,
        type: SuggestionType.typeDisable,
        title: '建议减少通知类型',
        description: '您的通知阅读率较低，建议禁用一些不重要的通知类型',
        confidence: 0.7,
        data: {'suggestedTypes': ['system', 'update']},
        generatedAt: DateTime.now(),
      ));
    }

    return suggestions;
  }

  Future<SmartNotificationSuggestion?> _suggestBatching(Map<String, dynamic> analytics) async {
    final totalNotifications = analytics['totalNotifications'] as int;
    
    if (totalNotifications > 20) {
      return SmartNotificationSuggestion(
        id: _uuid.v4(),
        userId: _defaultUserId,
        type: SuggestionType.batchSimilar,
        title: '建议启用通知批量',
        description: '您收到的通知较多，建议启用相似通知批量功能',
        confidence: 0.9,
        data: {'enableBatching': true},
        generatedAt: DateTime.now(),
      );
    }

    return null;
  }

  Future<void> _applyQuietHoursSuggestion(SmartNotificationSuggestion suggestion) async {
    final settings = await getNotificationSettings();
    final data = suggestion.data;
    
    final quietHours = QuietHours(
      id: _uuid.v4(),
      startTime: TimeOfDay(hour: data['startHour'], minute: 0),
      endTime: TimeOfDay(hour: data['endHour'], minute: 0),
      name: '智能推荐免打扰',
    );

    final updatedSettings = settings.copyWith(
      quietHours: [...settings.quietHours, quietHours],
    );

    await updateNotificationSettings(updatedSettings);
  }

  Future<void> _applyTypeDisableSuggestion(SmartNotificationSuggestion suggestion) async {
    final settings = await getNotificationSettings();
    final suggestedTypes = suggestion.data['suggestedTypes'] as List<String>;
    
    final updatedTypeSettings = Map<NotificationType, bool>.from(settings.typeSettings);
    for (final typeString in suggestedTypes) {
      final type = NotificationType.values.firstWhere((t) => t.name == typeString);
      updatedTypeSettings[type] = false;
    }

    final updatedSettings = settings.copyWith(typeSettings: updatedTypeSettings);
    await updateNotificationSettings(updatedSettings);
  }

  Future<void> _applyPriorityAdjustSuggestion(SmartNotificationSuggestion suggestion) async {
    final settings = await getNotificationSettings();
    final newMinPriority = NotificationPriority.values.firstWhere(
      (p) => p.name == suggestion.data['minPriority'],
      orElse: () => NotificationPriority.normal,
    );

    final updatedSettings = settings.copyWith(minPriority: newMinPriority);
    await updateNotificationSettings(updatedSettings);
  }

  Future<void> _applyBatchSuggestion(SmartNotificationSuggestion suggestion) async {
    final settings = await getNotificationSettings();
    final updatedSettings = settings.copyWith(groupSimilar: true);
    await updateNotificationSettings(updatedSettings);
  }

  Future<void> _applyScheduleOptimizeSuggestion(SmartNotificationSuggestion suggestion) async {
    // 实现发送时间优化逻辑
    print('应用发送时间优化建议');
  }
}

// Provider
final notificationServiceProvider = Provider<NotificationService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return NotificationService(storageService);
});
