import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../models/models.dart';
import '../../../modules/multi_character/group_chat_service.dart';
import '../../../services/storage_service.dart';

class GroupListScreen extends ConsumerStatefulWidget {
  const GroupListScreen({super.key});

  @override
  ConsumerState<GroupListScreen> createState() => _GroupListScreenState();
}

class _GroupListScreenState extends ConsumerState<GroupListScreen> {
  List<GroupConversation> _groupConversations = [];
  Map<String, List<Character>> _groupCharacters = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadGroupConversations();
  }

  Future<void> _loadGroupConversations() async {
    final groupChatService = ref.read(groupChatServiceProvider);
    final storageService = ref.read(storageServiceProvider);
    
    final conversations = await groupChatService.getGroupConversations();
    final groupCharacters = <String, List<Character>>{};
    
    for (final conversation in conversations) {
      final characters = <Character>[];
      for (final characterId in conversation.characterIds) {
        final character = await storageService.getCharacter(characterId);
        if (character != null) {
          characters.add(character);
        }
      }
      groupCharacters[conversation.id] = characters;
    }

    if (mounted) {
      setState(() {
        _groupConversations = conversations;
        _groupCharacters = groupCharacters;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('群聊'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.go('/create-group'),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildGroupList(),
    );
  }

  Widget _buildGroupList() {
    if (_groupConversations.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadGroupConversations,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _groupConversations.length,
        itemBuilder: (context, index) {
          final conversation = _groupConversations[index];
          final characters = _groupCharacters[conversation.id] ?? [];
          return _buildGroupCard(conversation, characters);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.group_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '还没有群聊',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '创建一个群聊，让多个AI角色一起聊天！',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => context.go('/create-group'),
            icon: const Icon(Icons.add),
            label: const Text('创建群聊'),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupCard(GroupConversation conversation, List<Character> characters) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => context.go('/group-chat/${conversation.id}'),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 群聊标题和类型
              Row(
                children: [
                  Expanded(
                    child: Text(
                      conversation.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (conversation.type != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getTypeColor(conversation.type!).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getTypeLabel(conversation.type!),
                        style: TextStyle(
                          fontSize: 10,
                          color: _getTypeColor(conversation.type!),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              
              // 参与者头像
              Row(
                children: [
                  Expanded(
                    child: SizedBox(
                      height: 32,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: characters.length,
                        itemBuilder: (context, index) {
                          final character = characters[index];
                          return Container(
                            margin: const EdgeInsets.only(right: 4),
                            child: CircleAvatar(
                              radius: 16,
                              backgroundColor: _getCharacterColor(character.id),
                              child: Text(
                                character.name[0],
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  Text(
                    '${characters.length}人',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              
              // 最后消息预览
              if (conversation.lastMessagePreview != null) ...[
                Text(
                  conversation.lastMessagePreview!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
              
              // 时间和消息数
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 14,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('MM/dd HH:mm').format(conversation.updatedAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                  const Spacer(),
                  if (conversation.messageCount > 0) ...[
                    Icon(
                      Icons.chat_bubble_outline,
                      size: 14,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${conversation.messageCount}条消息',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTypeLabel(GroupConversationType type) {
    switch (type) {
      case GroupConversationType.casual:
        return '随意聊天';
      case GroupConversationType.debate:
        return '辩论讨论';
      case GroupConversationType.roleplay:
        return '角色扮演';
      case GroupConversationType.support:
        return '情感支持';
      case GroupConversationType.learning:
        return '学习交流';
    }
  }

  Color _getTypeColor(GroupConversationType type) {
    switch (type) {
      case GroupConversationType.casual:
        return Colors.blue;
      case GroupConversationType.debate:
        return Colors.red;
      case GroupConversationType.roleplay:
        return Colors.purple;
      case GroupConversationType.support:
        return Colors.green;
      case GroupConversationType.learning:
        return Colors.orange;
    }
  }

  Color _getCharacterColor(String characterId) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    
    final index = characterId.hashCode % colors.length;
    return colors[index];
  }
}
