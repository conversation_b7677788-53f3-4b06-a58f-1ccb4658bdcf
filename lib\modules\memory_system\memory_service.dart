import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';
import 'memory_clustering_service.dart';

class MemoryService {
  final StorageService _storageService;
  final MemoryClusteringService _clusteringService;
  final Uuid _uuid = const Uuid();

  MemoryService(this._storageService, this._clusteringService);

  // 创建记忆
  Future<Memory> createMemory({
    required String characterId,
    required String userId,
    required MemoryType type,
    required String content,
    required String summary,
    double importance = 1.0,
    List<String> tags = const [],
    String? conversationId,
    String? messageId,
    Map<String, dynamic>? metadata,
  }) async {
    final memory = Memory(
      id: _uuid.v4(),
      characterId: characterId,
      userId: userId,
      type: type,
      content: content,
      summary: summary,
      timestamp: DateTime.now(),
      importance: importance,
      tags: tags,
      conversationId: conversationId,
      messageId: messageId,
      metadata: metadata,
    );

    await _storageService.saveMemory(memory);

    // 更新聚类
    await _clusteringService.updateClustersWithNewMemory(
      characterId: characterId,
      newMemory: memory,
    );

    return memory;
  }

  // 从对话中提取记忆
  Future<List<Memory>> extractMemoriesFromConversation({
    required String characterId,
    required String userId,
    required String conversationId,
    required List<Message> messages,
  }) async {
    final memories = <Memory>[];

    for (final message in messages) {
      if (message.sender == MessageSender.user) {
        // 分析用户消息，提取重要信息
        final extractedMemories = await _analyzeUserMessage(
          characterId: characterId,
          userId: userId,
          conversationId: conversationId,
          message: message,
        );
        memories.addAll(extractedMemories);
      }
    }

    return memories;
  }

  // 分析用户消息提取记忆
  Future<List<Memory>> _analyzeUserMessage({
    required String characterId,
    required String userId,
    required String conversationId,
    required Message message,
  }) async {
    final memories = <Memory>[];
    final content = message.content.toLowerCase();

    // 检测个人信息
    if (_containsPersonalInfo(content)) {
      final personalMemory = await createMemory(
        characterId: characterId,
        userId: userId,
        type: MemoryType.personal,
        content: message.content,
        summary: _extractPersonalInfoSummary(message.content),
        importance: 0.8,
        tags: ['个人信息'],
        conversationId: conversationId,
        messageId: message.id,
      );
      memories.add(personalMemory);
    }

    // 检测偏好信息
    if (_containsPreference(content)) {
      final preferenceMemory = await createMemory(
        characterId: characterId,
        userId: userId,
        type: MemoryType.preference,
        content: message.content,
        summary: _extractPreferenceSummary(message.content),
        importance: 0.7,
        tags: ['偏好'],
        conversationId: conversationId,
        messageId: message.id,
      );
      memories.add(preferenceMemory);
    }

    // 检测情感信息
    if (_containsEmotion(content)) {
      final emotionMemory = await createMemory(
        characterId: characterId,
        userId: userId,
        type: MemoryType.emotion,
        content: message.content,
        summary: _extractEmotionSummary(message.content),
        importance: 0.6,
        tags: ['情感'],
        conversationId: conversationId,
        messageId: message.id,
      );
      memories.add(emotionMemory);
    }

    // 检测重要事件
    if (_containsImportantEvent(content)) {
      final eventMemory = await createMemory(
        characterId: characterId,
        userId: userId,
        type: MemoryType.event,
        content: message.content,
        summary: _extractEventSummary(message.content),
        importance: 0.9,
        tags: ['事件'],
        conversationId: conversationId,
        messageId: message.id,
      );
      memories.add(eventMemory);
    }

    return memories;
  }

  // 检测是否包含个人信息
  bool _containsPersonalInfo(String content) {
    final personalKeywords = [
      '我叫', '我的名字', '我是', '我今年', '岁', '我在', '我住在',
      '我的工作', '我的职业', '我学', '我毕业', '我家', '我的家人'
    ];
    return personalKeywords.any((keyword) => content.contains(keyword));
  }

  // 检测是否包含偏好信息
  bool _containsPreference(String content) {
    final preferenceKeywords = [
      '我喜欢', '我不喜欢', '我爱', '我讨厌', '我偏好', '我倾向于',
      '我更喜欢', '我比较喜欢', '我的爱好', '我的兴趣'
    ];
    return preferenceKeywords.any((keyword) => content.contains(keyword));
  }

  // 检测是否包含情感信息
  bool _containsEmotion(String content) {
    final emotionKeywords = [
      '开心', '高兴', '快乐', '兴奋', '激动', '满足',
      '难过', '伤心', '沮丧', '失望', '郁闷', '痛苦',
      '生气', '愤怒', '烦躁', '焦虑', '担心', '害怕',
      '感动', '温暖', '幸福', '感谢', '感激'
    ];
    return emotionKeywords.any((keyword) => content.contains(keyword));
  }

  // 检测是否包含重要事件
  bool _containsImportantEvent(String content) {
    final eventKeywords = [
      '今天', '昨天', '明天', '这周', '上周', '下周',
      '发生了', '遇到了', '经历了', '参加了', '去了',
      '生日', '节日', '假期', '旅行', '工作', '考试'
    ];
    return eventKeywords.any((keyword) => content.contains(keyword));
  }

  // 提取个人信息摘要
  String _extractPersonalInfoSummary(String content) {
    if (content.contains('我叫') || content.contains('我的名字')) {
      return '用户姓名信息';
    }
    if (content.contains('岁') || content.contains('今年')) {
      return '用户年龄信息';
    }
    if (content.contains('工作') || content.contains('职业')) {
      return '用户职业信息';
    }
    if (content.contains('住在') || content.contains('我在')) {
      return '用户居住地信息';
    }
    return '用户个人基本信息';
  }

  // 提取偏好摘要
  String _extractPreferenceSummary(String content) {
    if (content.contains('喜欢')) {
      return '用户喜好偏好';
    }
    if (content.contains('不喜欢') || content.contains('讨厌')) {
      return '用户厌恶偏好';
    }
    return '用户偏好信息';
  }

  // 提取情感摘要
  String _extractEmotionSummary(String content) {
    final positiveEmotions = ['开心', '高兴', '快乐', '兴奋', '满足', '幸福'];
    final negativeEmotions = ['难过', '伤心', '沮丧', '失望', '生气', '焦虑'];
    
    if (positiveEmotions.any((emotion) => content.contains(emotion))) {
      return '用户积极情感状态';
    }
    if (negativeEmotions.any((emotion) => content.contains(emotion))) {
      return '用户消极情感状态';
    }
    return '用户情感状态';
  }

  // 提取事件摘要
  String _extractEventSummary(String content) {
    if (content.contains('今天')) {
      return '用户今日事件';
    }
    if (content.contains('昨天')) {
      return '用户昨日事件';
    }
    if (content.contains('明天')) {
      return '用户明日计划';
    }
    return '用户重要事件';
  }

  // 获取相关记忆
  Future<List<Memory>> getRelevantMemories({
    required String characterId,
    required String userId,
    required String query,
    int limit = 10,
  }) async {
    final allMemories = await _storageService.getCharacterMemories(characterId, userId);
    
    // 简单的相关性计算（基于关键词匹配）
    final scoredMemories = allMemories.map((memory) {
      final score = _calculateRelevanceScore(memory, query);
      return MapEntry(memory, score);
    }).where((entry) => entry.value > 0).toList();

    // 按相关性排序
    scoredMemories.sort((a, b) => b.value.compareTo(a.value));
    
    return scoredMemories.take(limit).map((entry) => entry.key).toList();
  }

  // 计算相关性分数
  double _calculateRelevanceScore(Memory memory, String query) {
    final queryWords = query.toLowerCase().split(' ');
    final memoryText = '${memory.content} ${memory.summary} ${memory.tags.join(' ')}'.toLowerCase();
    
    double score = 0.0;
    for (final word in queryWords) {
      if (memoryText.contains(word)) {
        score += 1.0;
      }
    }
    
    // 考虑记忆的重要性和访问频率
    score *= memory.importance;
    score += memory.accessCount * 0.1;
    
    return score;
  }

  // 更新记忆访问
  Future<void> updateMemoryAccess(String memoryId) async {
    final memory = await _storageService.getMemory(memoryId);
    if (memory != null) {
      final updatedMemory = memory.copyWith(
        accessCount: memory.accessCount + 1,
        lastAccessed: DateTime.now(),
      );
      await _storageService.saveMemory(updatedMemory);
    }
  }

  // 获取用户档案
  Future<UserProfile?> getUserProfile(String userId) async {
    return await _storageService.getUserProfile(userId);
  }

  // 更新用户档案
  Future<void> updateUserProfile(UserProfile profile) async {
    await _storageService.saveUserProfile(profile);
  }

  // 从记忆中构建用户档案
  Future<UserProfile> buildUserProfileFromMemories(String userId) async {
    final memories = await _storageService.getUserMemories(userId);
    
    final personalInfo = <String, String>{};
    final interests = <String>[];
    final preferences = <String>[];
    
    for (final memory in memories) {
      switch (memory.type) {
        case MemoryType.personal:
          _extractPersonalInfoFromMemory(memory, personalInfo);
          break;
        case MemoryType.preference:
          _extractPreferencesFromMemory(memory, preferences);
          break;
        default:
          break;
      }
    }
    
    return UserProfile(
      userId: userId,
      name: personalInfo['name'] ?? '用户',
      age: personalInfo['age'],
      occupation: personalInfo['occupation'],
      location: personalInfo['location'],
      interests: interests,
      preferences: preferences,
      personalInfo: personalInfo,
      lastUpdated: DateTime.now(),
      interactionCount: memories.length,
    );
  }

  void _extractPersonalInfoFromMemory(Memory memory, Map<String, String> personalInfo) {
    final content = memory.content.toLowerCase();
    
    // 提取姓名
    if (content.contains('我叫') || content.contains('我的名字')) {
      // 简单的姓名提取逻辑
      final nameMatch = RegExp(r'我叫(.+?)(?:\s|$|，|。)').firstMatch(content);
      if (nameMatch != null) {
        personalInfo['name'] = nameMatch.group(1)?.trim() ?? '';
      }
    }
    
    // 提取年龄
    if (content.contains('岁') || content.contains('今年')) {
      final ageMatch = RegExp(r'(\d+)岁').firstMatch(content);
      if (ageMatch != null) {
        personalInfo['age'] = ageMatch.group(1) ?? '';
      }
    }
  }

  void _extractPreferencesFromMemory(Memory memory, List<String> preferences) {
    final content = memory.content;
    if (content.contains('喜欢')) {
      preferences.add(memory.summary);
    }
  }

  // 获取记忆聚类
  Future<List<MemoryCluster>> getMemoryClusters(String characterId) async {
    return await _clusteringService.getMemoryClusters(characterId);
  }

  // 执行记忆聚类
  Future<List<MemoryCluster>> performMemoryClustering(String characterId) async {
    return await _clusteringService.performClustering(characterId: characterId);
  }

  // 搜索记忆聚类
  Future<List<MemoryCluster>> searchMemoryClusters({
    required String characterId,
    required String query,
  }) async {
    return await _clusteringService.searchClusters(
      characterId: characterId,
      query: query,
    );
  }

  // 分析聚类趋势
  Future<Map<String, dynamic>> analyzeClusterTrends(String characterId) async {
    return await _clusteringService.analyzeClusterTrends(characterId);
  }
}

// Provider
final memoryServiceProvider = Provider<MemoryService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  final clusteringService = ref.watch(memoryClusteringServiceProvider);
  return MemoryService(storageService, clusteringService);
});
