import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';
import 'message.dart';

part 'conversation.freezed.dart';
part 'conversation.g.dart';

@freezed
@HiveType(typeId: 7)
class Conversation with _$Conversation {
  const factory Conversation({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required String title,
    @HiveField(3) required DateTime createdAt,
    @HiveField(4) required DateTime updatedAt,
    @HiveField(5) @Default([]) List<String> messageIds,
    @HiveField(6) @Default(0) int messageCount,
    @HiveField(7) String? lastMessageId,
    @HiveField(8) String? lastMessagePreview,
    @HiveField(9) @Default(false) bool isPinned,
    @HiveField(10) @Default(false) bool isMuted,
    @HiveField(11) @Default(0) int unreadCount,
    @HiveField(12) Map<String, dynamic>? metadata,
  }) = _Conversation;

  factory Conversation.fromJson(Map<String, dynamic> json) => _$ConversationFromJson(json);
}

@freezed
@HiveType(typeId: 8)
class ConversationSummary with _$ConversationSummary {
  const factory ConversationSummary({
    @HiveField(0) required String conversationId,
    @HiveField(1) required String characterId,
    @HiveField(2) required String characterName,
    @HiveField(3) required String lastMessage,
    @HiveField(4) required DateTime lastMessageTime,
    @HiveField(5) @Default(0) int unreadCount,
    @HiveField(6) @Default(false) bool isPinned,
    @HiveField(7) String? characterAvatar,
  }) = _ConversationSummary;

  factory ConversationSummary.fromJson(Map<String, dynamic> json) => _$ConversationSummaryFromJson(json);
}
