import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../modules/relationship/intimacy_service.dart';

class IntimacyIndicator extends ConsumerWidget {
  final int intimacyLevel;
  final bool showLabel;
  final bool showProgress;
  final double size;

  const IntimacyIndicator({
    super.key,
    required this.intimacyLevel,
    this.showLabel = true,
    this.showProgress = true,
    this.size = 24.0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final intimacyService = ref.read(intimacyServiceProvider);
    final description = intimacyService.getIntimacyLevelDescription(intimacyLevel);
    final colorHex = intimacyService.getIntimacyLevelColor(intimacyLevel);
    final color = Color(int.parse(colorHex.substring(1), radix: 16) + 0xFF000000);
    final pointsToNext = intimacyService.getPointsToNextLevel(intimacyLevel);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 心形图标
        Icon(
          Icons.favorite,
          size: size,
          color: color,
        ),
        if (showLabel || showProgress) const SizedBox(width: 6),
        
        // 等级描述和进度
        if (showLabel || showProgress)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showLabel)
                Text(
                  description,
                  style: TextStyle(
                    fontSize: size * 0.6,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
              if (showProgress && intimacyLevel < 100) ...[
                if (showLabel) const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      '$intimacyLevel',
                      style: TextStyle(
                        fontSize: size * 0.5,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 4),
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: _getProgressFactor(intimacyLevel),
                        child: Container(
                          decoration: BoxDecoration(
                            color: color,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '+$pointsToNext',
                      style: TextStyle(
                        fontSize: size * 0.4,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
      ],
    );
  }

  double _getProgressFactor(int level) {
    if (level < 20) {
      return level / 20.0;
    } else if (level < 40) {
      return (level - 20) / 20.0;
    } else if (level < 60) {
      return (level - 40) / 20.0;
    } else if (level < 80) {
      return (level - 60) / 20.0;
    } else if (level < 100) {
      return (level - 80) / 20.0;
    } else {
      return 1.0;
    }
  }
}

class IntimacyLevelCard extends ConsumerWidget {
  final int intimacyLevel;
  final String characterName;

  const IntimacyLevelCard({
    super.key,
    required this.intimacyLevel,
    required this.characterName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final intimacyService = ref.read(intimacyServiceProvider);
    final description = intimacyService.getIntimacyLevelDescription(intimacyLevel);
    final colorHex = intimacyService.getIntimacyLevelColor(intimacyLevel);
    final color = Color(int.parse(colorHex.substring(1), radix: 16) + 0xFF000000);
    final pointsToNext = intimacyService.getPointsToNextLevel(intimacyLevel);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.favorite,
                  color: color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  '与$characterName的关系',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 亲密度等级
            Row(
              children: [
                Text(
                  '等级: ',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                const Spacer(),
                Text(
                  '$intimacyLevel/100',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // 进度条
            LinearProgressIndicator(
              value: intimacyLevel / 100.0,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
            const SizedBox(height: 8),
            
            // 下一等级信息
            if (intimacyLevel < 100)
              Text(
                '距离下一等级还需 $pointsToNext 点亲密度',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              )
            else
              Text(
                '已达到最高等级！',
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class IntimacyMilestoneDialog extends StatelessWidget {
  final int newLevel;
  final String characterName;

  const IntimacyMilestoneDialog({
    super.key,
    required this.newLevel,
    required this.characterName,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 庆祝图标
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.pink[100],
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.celebration,
              size: 40,
              color: Colors.pink[600],
            ),
          ),
          const SizedBox(height: 16),
          
          // 标题
          Text(
            '关系升级！',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          
          // 内容
          Text(
            '恭喜！你和$characterName的关系达到了新的高度！',
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          
          // 新等级显示
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.pink[50],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.favorite,
                  color: Colors.pink[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '亲密度: $newLevel',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.pink[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('太棒了！'),
        ),
      ],
    );
  }
}
