import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../models/models.dart';
import '../../../modules/analytics/interaction_analysis_service.dart';
import '../../widgets/enhanced_loading.dart';
import '../../widgets/enhanced_card.dart';
import '../../widgets/enhanced_button.dart';

class InteractionAnalysisScreen extends ConsumerStatefulWidget {
  final String characterId;

  const InteractionAnalysisScreen({
    super.key,
    required this.characterId,
  });

  @override
  ConsumerState<InteractionAnalysisScreen> createState() => _InteractionAnalysisScreenState();
}

class _InteractionAnalysisScreenState extends ConsumerState<InteractionAnalysisScreen> {
  InteractionAnalysis? _latestAnalysis;
  List<InteractionAnalysis> _analysisHistory = [];
  bool _isLoading = true;
  bool _isGenerating = false;
  Character? _character;
  AnalysisPeriod _selectedPeriod = AnalysisPeriod.weekly;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final analysisService = ref.read(interactionAnalysisServiceProvider);
    final storageService = ref.read(storageServiceProvider);
    
    final character = await storageService.getCharacter(widget.characterId);
    final history = await analysisService.getAnalysisHistory(characterId: widget.characterId);
    final latest = history.isNotEmpty ? history.first : null;

    if (mounted) {
      setState(() {
        _character = character;
        _latestAnalysis = latest;
        _analysisHistory = history;
        _isLoading = false;
      });
    }
  }

  Future<void> _generateNewAnalysis() async {
    setState(() {
      _isGenerating = true;
    });

    try {
      final analysisService = ref.read(interactionAnalysisServiceProvider);
      final analysis = await analysisService.generateAnalysis(
        characterId: widget.characterId,
        period: _selectedPeriod,
      );

      setState(() {
        _latestAnalysis = analysis;
        _analysisHistory = [analysis, ..._analysisHistory];
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('分析报告生成完成')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('生成失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _character == null) {
      return const Scaffold(
        body: Center(
          child: EnhancedLoading(
            message: '正在加载分析数据...',
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${_character!.name} 的互动分析'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 生成新分析按钮
            _buildGenerateAnalysisCard(),
            
            const SizedBox(height: 16),
            
            // 最新分析概览
            if (_latestAnalysis != null) ...[
              _buildLatestAnalysisCard(),
              const SizedBox(height: 16),
            ],
            
            // 分析历史
            _buildAnalysisHistorySection(),
          ],
        ),
      ),
    );
  }

  Widget _buildGenerateAnalysisCard() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                '生成新的分析报告',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // 分析周期选择
          const Text(
            '分析周期:',
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: AnalysisPeriod.values.map((period) {
              return ChoiceChip(
                label: Text(_getPeriodLabel(period)),
                selected: _selectedPeriod == period,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedPeriod = period;
                    });
                  }
                },
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
          
          // 生成按钮
          SizedBox(
            width: double.infinity,
            child: EnhancedButton(
              text: _isGenerating ? '正在生成...' : '生成分析报告',
              onPressed: _isGenerating ? null : _generateNewAnalysis,
              isLoading: _isGenerating,
              icon: Icons.analytics,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLatestAnalysisCard() {
    final analysis = _latestAnalysis!;
    
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.insights,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                '最新分析报告',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const Spacer(),
              Text(
                DateFormat('MM/dd HH:mm').format(analysis.analysisDate),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // 关键指标
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  '总消息数',
                  analysis.metrics.totalMessages.toString(),
                  Icons.chat_bubble_outline,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  '参与度',
                  '${analysis.relationshipInsights.engagementScore.toInt()}%',
                  Icons.trending_up,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  '连续天数',
                  '${analysis.metrics.currentStreak}天',
                  Icons.local_fire_department,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  '关系阶段',
                  _getStageLabel(analysis.relationshipInsights.currentStage),
                  Icons.favorite,
                  Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 亮点和建议
          if (analysis.highlights.isNotEmpty) ...[
            const Text(
              '亮点:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 4),
            ...analysis.highlights.take(3).map((highlight) => Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 2),
              child: Row(
                children: [
                  const Icon(Icons.star, size: 12, color: Colors.amber),
                  const SizedBox(width: 8),
                  Expanded(child: Text(highlight, style: const TextStyle(fontSize: 12))),
                ],
              ),
            )),
            const SizedBox(height: 8),
          ],
          
          if (analysis.recommendations.isNotEmpty) ...[
            const Text(
              '建议:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 4),
            ...analysis.recommendations.take(2).map((recommendation) => Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 2),
              child: Row(
                children: [
                  const Icon(Icons.lightbulb, size: 12, color: Colors.blue),
                  const SizedBox(width: 8),
                  Expanded(child: Text(recommendation, style: const TextStyle(fontSize: 12))),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisHistorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '分析历史',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        
        if (_analysisHistory.isEmpty)
          const EnhancedCard(
            child: Center(
              child: Text(
                '还没有分析记录',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...(_analysisHistory.map((analysis) => _buildAnalysisHistoryCard(analysis))),
      ],
    );
  }

  Widget _buildAnalysisHistoryCard(InteractionAnalysis analysis) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getPeriodColor(analysis.period).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getPeriodLabel(analysis.period),
                  style: TextStyle(
                    fontSize: 10,
                    color: _getPeriodColor(analysis.period),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  DateFormat('yyyy/MM/dd HH:mm').format(analysis.analysisDate),
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
              Text(
                '${analysis.relationshipInsights.engagementScore.toInt()}%',
                style: TextStyle(
                  fontSize: 12,
                  color: _getEngagementColor(analysis.relationshipInsights.engagementScore),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Text(
            '${analysis.metrics.totalMessages}条消息 • '
            '${analysis.metrics.sessionCount}次会话 • '
            '连续${analysis.metrics.currentStreak}天',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  String _getPeriodLabel(AnalysisPeriod period) {
    switch (period) {
      case AnalysisPeriod.daily:
        return '日分析';
      case AnalysisPeriod.weekly:
        return '周分析';
      case AnalysisPeriod.monthly:
        return '月分析';
      case AnalysisPeriod.quarterly:
        return '季度分析';
      case AnalysisPeriod.yearly:
        return '年分析';
    }
  }

  Color _getPeriodColor(AnalysisPeriod period) {
    switch (period) {
      case AnalysisPeriod.daily:
        return Colors.green;
      case AnalysisPeriod.weekly:
        return Colors.blue;
      case AnalysisPeriod.monthly:
        return Colors.orange;
      case AnalysisPeriod.quarterly:
        return Colors.purple;
      case AnalysisPeriod.yearly:
        return Colors.red;
    }
  }

  String _getStageLabel(RelationshipStage stage) {
    switch (stage) {
      case RelationshipStage.stranger:
        return '陌生人';
      case RelationshipStage.acquaintance:
        return '熟人';
      case RelationshipStage.friend:
        return '朋友';
      case RelationshipStage.closeFriend:
        return '好朋友';
      case RelationshipStage.bestFriend:
        return '最好的朋友';
      case RelationshipStage.companion:
        return '伴侣';
    }
  }

  Color _getEngagementColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    if (score >= 40) return Colors.yellow[700]!;
    return Colors.red;
  }
}
