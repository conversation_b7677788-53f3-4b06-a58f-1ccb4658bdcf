import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../models/models.dart';

class AIService {
  late GenerativeModel _model;
  bool _isInitialized = false;

  // 初始化AI服务
  Future<void> initialize(String apiKey) async {
    try {
      _model = GenerativeModel(
        model: 'gemini-2.5-flash-preview-05-20',
        apiKey: apiKey,
        generationConfig: GenerationConfig(
          temperature: 0.8,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1000,
        ),
      );
      _isInitialized = true;
    } catch (e) {
      throw Exception('AI服务初始化失败: $e');
    }
  }

  // 生成AI回复
  Future<String> generateResponse({
    required String userMessage,
    required Character character,
    required List<Message> conversationHistory,
  }) async {
    if (!_isInitialized) {
      return _getFallbackResponse(userMessage, character);
    }

    try {
      final prompt = _buildPrompt(userMessage, character, conversationHistory);
      final content = [Content.text(prompt)];
      final response = await _model.generateContent(content);
      
      return response.text?.trim() ?? _getFallbackResponse(userMessage, character);
    } catch (e) {
      print('AI生成回复失败: $e');
      return _getFallbackResponse(userMessage, character);
    }
  }

  // 构建提示词
  String _buildPrompt(String userMessage, Character character, List<Message> history) {
    final personalityDescription = _buildPersonalityDescription(character.personality);
    final recentHistory = history.take(10).map((msg) {
      final sender = msg.sender == MessageSender.user ? '用户' : character.name;
      return '$sender: ${msg.content}';
    }).join('\n');

    return '''
你是${character.name}，一个${character.age}岁的${character.occupation}。

角色设定：
- 性格描述：${character.description}
- 性格特点：${character.traits.join('、')}
- 兴趣爱好：${character.interests.join('、')}
- 说话风格：${_getSpeakingStyleDescription(character.speakingStyle)}

性格特征详细：
$personalityDescription

对话历史：
$recentHistory

用户刚刚说：$userMessage

请以${character.name}的身份回复，保持角色的性格特点和说话风格。回复要自然、有趣，体现出你的个性。回复长度控制在50-150字之间。
''';
  }

  // 构建性格描述
  String _buildPersonalityDescription(PersonalityTraits traits) {
    final descriptions = <String>[];
    
    if (traits.extroversion > 0.6) {
      descriptions.add('外向开朗，喜欢与人交流');
    } else if (traits.extroversion < 0.4) {
      descriptions.add('内向安静，更喜欢深度交流');
    }
    
    if (traits.agreeableness > 0.6) {
      descriptions.add('友善合作，容易相处');
    } else if (traits.agreeableness < 0.4) {
      descriptions.add('独立自主，有自己的主见');
    }
    
    if (traits.conscientiousness > 0.6) {
      descriptions.add('认真负责，做事有条理');
    } else if (traits.conscientiousness < 0.4) {
      descriptions.add('随性自由，不拘小节');
    }
    
    if (traits.neuroticism > 0.6) {
      descriptions.add('情感丰富，敏感细腻');
    } else if (traits.neuroticism < 0.4) {
      descriptions.add('情绪稳定，心态平和');
    }
    
    if (traits.openness > 0.6) {
      descriptions.add('思维开放，喜欢新事物');
    } else if (traits.openness < 0.4) {
      descriptions.add('传统稳重，喜欢熟悉的事物');
    }
    
    if (traits.humor > 0.6) {
      descriptions.add('幽默风趣，喜欢开玩笑');
    }
    
    if (traits.empathy > 0.6) {
      descriptions.add('善解人意，富有同情心');
    }
    
    if (traits.creativity > 0.6) {
      descriptions.add('富有创意，想象力丰富');
    }
    
    return descriptions.join('，');
  }

  // 获取说话风格描述
  String _getSpeakingStyleDescription(String style) {
    switch (style) {
      case 'gentle':
        return '温柔体贴，语气柔和，经常使用"呢"、"哦"等语气词';
      case 'energetic':
        return '活力四射，语气热情，经常使用感叹号和积极的词汇';
      case 'professional':
        return '专业理性，用词准确，逻辑清晰';
      case 'technical':
        return '技术范儿，喜欢用技术术语，逻辑性强';
      case 'casual':
      default:
        return '随意自然，语气轻松，像朋友一样聊天';
    }
  }

  // 备用回复（当AI服务不可用时）
  String _getFallbackResponse(String userMessage, Character character) {
    final responses = _getFallbackResponsesByCharacter(character.id);
    final randomIndex = DateTime.now().millisecond % responses.length;
    return responses[randomIndex];
  }

  // 根据角色获取备用回复
  List<String> _getFallbackResponsesByCharacter(String characterId) {
    switch (characterId) {
      case '1': // 小雨
        return [
          '我理解你的想法呢，这确实很有趣～',
          '谢谢你和我分享这些，我很开心能听到你的想法。',
          '这听起来很棒！你能告诉我更多吗？',
          '我觉得你说得很有道理，我也是这么想的呢。',
          '哇，这真的很有意思！我从来没有这样想过。',
        ];
      case '2': // 阿凯
        return [
          '哈哈，你说得对！我也觉得是这样。',
          '这个想法不错！我们可以一起讨论讨论。',
          '真的吗？那太酷了！',
          '我完全同意你的观点，兄弟！',
          '这让我想到了一个有趣的事情...',
        ];
      case '3': // 小雪
        return [
          '您的观点很有见地，我认为这值得深入思考。',
          '从理性的角度来看，这确实是一个不错的想法。',
          '我理解您的立场，这是一个很好的分析。',
          '根据我的理解，您说的很有道理。',
          '这是一个很专业的观点，我很赞同。',
        ];
      case '4': // 小明
        return [
          '从技术角度来看，这个想法很有创新性！',
          '这让我想到了最新的技术趋势...',
          '有意思！这个问题可以用编程的思维来解决。',
          '我觉得可以用算法来优化这个过程。',
          '这个概念在计算机科学中也有类似的应用。',
        ];
      default:
        return [
          '这是一个很有趣的想法！',
          '我很喜欢和你聊这些话题。',
          '你说得很有道理。',
          '这让我学到了新东西。',
          '谢谢你的分享！',
        ];
    }
  }

  // 检查服务是否可用
  bool get isAvailable => _isInitialized;
}

// Provider
final aiServiceProvider = Provider<AIService>((ref) {
  return AIService();
});
