# 生成Freezed和Hive代码的脚本

Write-Host "开始生成模型代码..." -ForegroundColor Green

# 检查Flutter是否可用
try {
    flutter --version | Out-Null
    Write-Host "Flutter已安装" -ForegroundColor Green
} catch {
    Write-Host "Flutter未安装或未添加到PATH，请先安装Flutter" -ForegroundColor Red
    exit 1
}

# 获取依赖
Write-Host "获取依赖..." -ForegroundColor Yellow
flutter pub get

# 生成代码
Write-Host "生成Freezed和Hive代码..." -ForegroundColor Yellow
flutter packages pub run build_runner build --delete-conflicting-outputs

Write-Host "代码生成完成！" -ForegroundColor Green
