import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'content_unlocking.freezed.dart';
part 'content_unlocking.g.dart';

@freezed
@HiveType(typeId: 95)
class UnlockableContent with _$UnlockableContent {
  const factory UnlockableContent({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required ContentType type,
    @HiveField(3) required String title,
    @HiveField(4) required String description,
    @HiveField(5) required String content,
    @HiveField(6) required List<UnlockCondition> unlockConditions,
    @HiveField(7) @Default(ContentRarity.common) ContentRarity rarity,
    @HiveField(8) @Default(0) int unlockOrder,
    @HiveField(9) @Default([]) List<String> tags,
    @HiveField(10) String? imageUrl,
    @HiveField(11) String? audioUrl,
    @HiveField(12) @Default({}) Map<String, dynamic> metadata,
    @HiveField(13) required DateTime createdAt,
    @HiveField(14) @Default(true) bool isActive,
    @HiveField(15) @Default(0) int viewCount,
    @HiveField(16) @Default(0.0) double userRating,
  }) = _UnlockableContent;

  factory UnlockableContent.fromJson(Map<String, dynamic> json) => _$UnlockableContentFromJson(json);
}

@HiveType(typeId: 96)
enum ContentType {
  @HiveField(0)
  story,        // 故事
  @HiveField(1)
  memory,       // 记忆片段
  @HiveField(2)
  dialogue,     // 特殊对话
  @HiveField(3)
  image,        // 图片
  @HiveField(4)
  audio,        // 音频
  @HiveField(5)
  video,        // 视频
  @HiveField(6)
  background,   // 背景故事
  @HiveField(7)
  achievement,  // 成就
  @HiveField(8)
  outfit,       // 服装
  @HiveField(9)
  accessory,    // 配饰
  @HiveField(10)
  scene,        // 场景
  @HiveField(11)
  emotion,      // 情感表达
}

@HiveType(typeId: 97)
enum ContentRarity {
  @HiveField(0)
  common,       // 普通
  @HiveField(1)
  uncommon,     // 不常见
  @HiveField(2)
  rare,         // 稀有
  @HiveField(3)
  epic,         // 史诗
  @HiveField(4)
  legendary,    // 传说
}

@freezed
@HiveType(typeId: 98)
class UnlockCondition with _$UnlockCondition {
  const factory UnlockCondition({
    @HiveField(0) required String id,
    @HiveField(1) required ConditionType type,
    @HiveField(2) required Map<String, dynamic> parameters,
    @HiveField(3) @Default(1.0) double weight,
    @HiveField(4) String? description,
  }) = _UnlockCondition;

  factory UnlockCondition.fromJson(Map<String, dynamic> json) => _$UnlockConditionFromJson(json);
}

@HiveType(typeId: 99)
enum ConditionType {
  @HiveField(0)
  intimacyLevel,        // 亲密度等级
  @HiveField(1)
  conversationCount,    // 对话次数
  @HiveField(2)
  timeSpent,           // 相处时间
  @HiveField(3)
  emotionState,        // 情感状态
  @HiveField(4)
  specificDialogue,    // 特定对话
  @HiveField(5)
  dateTime,            // 特定时间
  @HiveField(6)
  userBehavior,        // 用户行为
  @HiveField(7)
  memoryCount,         // 记忆数量
  @HiveField(8)
  achievementUnlock,   // 成就解锁
  @HiveField(9)
  seasonalEvent,       // 季节性事件
  @HiveField(10)
  randomChance,        // 随机概率
  @HiveField(11)
  combinationLock,     // 组合解锁
}

@freezed
@HiveType(typeId: 100)
class ContentUnlock with _$ContentUnlock {
  const factory ContentUnlock({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String contentId,
    @HiveField(3) required DateTime unlockedAt,
    @HiveField(4) required UnlockTrigger trigger,
    @HiveField(5) @Default(false) bool isViewed,
    @HiveField(6) DateTime? viewedAt,
    @HiveField(7) @Default(0) int viewCount,
    @HiveField(8) double? userRating,
    @HiveField(9) String? userComment,
    @HiveField(10) @Default({}) Map<String, dynamic> unlockContext,
  }) = _ContentUnlock;

  factory ContentUnlock.fromJson(Map<String, dynamic> json) => _$ContentUnlockFromJson(json);
}

@freezed
@HiveType(typeId: 101)
class UnlockTrigger with _$UnlockTrigger {
  const factory UnlockTrigger({
    @HiveField(0) required TriggerType type,
    @HiveField(1) required String description,
    @HiveField(2) @Default({}) Map<String, dynamic> context,
  }) = _UnlockTrigger;

  factory UnlockTrigger.fromJson(Map<String, dynamic> json) => _$UnlockTriggerFromJson(json);
}

@HiveType(typeId: 102)
enum TriggerType {
  @HiveField(0)
  automatic,    // 自动触发
  @HiveField(1)
  manual,       // 手动触发
  @HiveField(2)
  scheduled,    // 计划触发
  @HiveField(3)
  event,        // 事件触发
}

@freezed
@HiveType(typeId: 103)
class ContentCollection with _$ContentCollection {
  const factory ContentCollection({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required String name,
    @HiveField(3) required String description,
    @HiveField(4) required List<String> contentIds,
    @HiveField(5) @Default(CollectionType.series) CollectionType type,
    @HiveField(6) @Default([]) List<String> tags,
    @HiveField(7) String? imageUrl,
    @HiveField(8) @Default(0) int unlockOrder,
    @HiveField(9) @Default(true) bool isActive,
    @HiveField(10) required DateTime createdAt,
  }) = _ContentCollection;

  factory ContentCollection.fromJson(Map<String, dynamic> json) => _$ContentCollectionFromJson(json);
}

@HiveType(typeId: 104)
enum CollectionType {
  @HiveField(0)
  series,       // 系列
  @HiveField(1)
  theme,        // 主题
  @HiveField(2)
  seasonal,     // 季节性
  @HiveField(3)
  special,      // 特殊
}

@freezed
@HiveType(typeId: 105)
class UnlockProgress with _$UnlockProgress {
  const factory UnlockProgress({
    @HiveField(0) required String userId,
    @HiveField(1) required String characterId,
    @HiveField(2) required String contentId,
    @HiveField(3) required Map<String, double> conditionProgress,
    @HiveField(4) required double overallProgress,
    @HiveField(5) required DateTime lastUpdated,
    @HiveField(6) @Default([]) List<String> completedConditions,
    @HiveField(7) @Default([]) List<String> pendingConditions,
  }) = _UnlockProgress;

  factory UnlockProgress.fromJson(Map<String, dynamic> json) => _$UnlockProgressFromJson(json);
}

@freezed
@HiveType(typeId: 106)
class ContentRecommendation with _$ContentRecommendation {
  const factory ContentRecommendation({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String contentId,
    @HiveField(3) required RecommendationReason reason,
    @HiveField(4) required double confidence,
    @HiveField(5) required String description,
    @HiveField(6) required DateTime generatedAt,
    @HiveField(7) @Default(false) bool isViewed,
    @HiveField(8) @Default(false) bool isAccepted,
    @HiveField(9) String? feedback,
  }) = _ContentRecommendation;

  factory ContentRecommendation.fromJson(Map<String, dynamic> json) => _$ContentRecommendationFromJson(json);
}

@HiveType(typeId: 107)
enum RecommendationReason {
  @HiveField(0)
  nearUnlock,       // 即将解锁
  @HiveField(1)
  similarContent,   // 相似内容
  @HiveField(2)
  userPreference,   // 用户偏好
  @HiveField(3)
  trending,         // 热门内容
  @HiveField(4)
  seasonal,         // 季节性推荐
  @HiveField(5)
  completion,       // 完成度推荐
}

@freezed
@HiveType(typeId: 108)
class UnlockEvent with _$UnlockEvent {
  const factory UnlockEvent({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required String contentId,
    @HiveField(4) required EventType eventType,
    @HiveField(5) required DateTime timestamp,
    @HiveField(6) @Default({}) Map<String, dynamic> eventData,
    @HiveField(7) String? description,
  }) = _UnlockEvent;

  factory UnlockEvent.fromJson(Map<String, dynamic> json) => _$UnlockEventFromJson(json);
}

@HiveType(typeId: 109)
enum EventType {
  @HiveField(0)
  conditionMet,     // 条件满足
  @HiveField(1)
  contentUnlocked,  // 内容解锁
  @HiveField(2)
  contentViewed,    // 内容查看
  @HiveField(3)
  progressUpdate,   // 进度更新
  @HiveField(4)
  collectionComplete, // 集合完成
}

@freezed
@HiveType(typeId: 110)
class ContentAnalytics with _$ContentAnalytics {
  const factory ContentAnalytics({
    @HiveField(0) required String characterId,
    @HiveField(1) required DateTime date,
    @HiveField(2) @Default(0) int totalContent,
    @HiveField(3) @Default(0) int unlockedContent,
    @HiveField(4) @Default(0) int viewedContent,
    @HiveField(5) @Default({}) Map<ContentType, int> typeDistribution,
    @HiveField(6) @Default({}) Map<ContentRarity, int> rarityDistribution,
    @HiveField(7) @Default(0.0) double averageRating,
    @HiveField(8) @Default([]) List<String> popularContent,
    @HiveField(9) @Default(0.0) double unlockRate,
    @HiveField(10) @Default(0.0) double viewRate,
  }) = _ContentAnalytics;

  factory ContentAnalytics.fromJson(Map<String, dynamic> json) => _$ContentAnalyticsFromJson(json);
}

@freezed
@HiveType(typeId: 111)
class UnlockNotification with _$UnlockNotification {
  const factory UnlockNotification({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String contentId,
    @HiveField(3) required NotificationType notificationType,
    @HiveField(4) required String title,
    @HiveField(5) required String message,
    @HiveField(6) required DateTime createdAt,
    @HiveField(7) @Default(false) bool isRead,
    @HiveField(8) @Default(false) bool isActioned,
    @HiveField(9) String? actionUrl,
    @HiveField(10) @Default({}) Map<String, dynamic> metadata,
  }) = _UnlockNotification;

  factory UnlockNotification.fromJson(Map<String, dynamic> json) => _$UnlockNotificationFromJson(json);
}

@HiveType(typeId: 112)
enum NotificationType {
  @HiveField(0)
  newUnlock,        // 新解锁
  @HiveField(1)
  progressUpdate,   // 进度更新
  @HiveField(2)
  nearUnlock,       // 即将解锁
  @HiveField(3)
  recommendation,   // 推荐
  @HiveField(4)
  collectionComplete, // 集合完成
  @HiveField(5)
  specialEvent,     // 特殊事件
}

@freezed
@HiveType(typeId: 113)
class ContentFilter with _$ContentFilter {
  const factory ContentFilter({
    @HiveField(0) @Default([]) List<ContentType> types,
    @HiveField(1) @Default([]) List<ContentRarity> rarities,
    @HiveField(2) @Default([]) List<String> tags,
    @HiveField(3) @Default(UnlockStatus.all) UnlockStatus unlockStatus,
    @HiveField(4) @Default(ViewStatus.all) ViewStatus viewStatus,
    @HiveField(5) String? searchQuery,
    @HiveField(6) @Default(SortBy.unlockOrder) SortBy sortBy,
    @HiveField(7) @Default(SortOrder.ascending) SortOrder sortOrder,
  }) = _ContentFilter;

  factory ContentFilter.fromJson(Map<String, dynamic> json) => _$ContentFilterFromJson(json);
}

@HiveType(typeId: 114)
enum UnlockStatus {
  @HiveField(0)
  all,          // 全部
  @HiveField(1)
  unlocked,     // 已解锁
  @HiveField(2)
  locked,       // 未解锁
  @HiveField(3)
  nearUnlock,   // 即将解锁
}

@HiveType(typeId: 115)
enum ViewStatus {
  @HiveField(0)
  all,          // 全部
  @HiveField(1)
  viewed,       // 已查看
  @HiveField(2)
  unviewed,     // 未查看
}

@HiveType(typeId: 116)
enum SortBy {
  @HiveField(0)
  unlockOrder,  // 解锁顺序
  @HiveField(1)
  unlockDate,   // 解锁日期
  @HiveField(2)
  rarity,       // 稀有度
  @HiveField(3)
  rating,       // 评分
  @HiveField(4)
  viewCount,    // 查看次数
  @HiveField(5)
  name,         // 名称
}

@HiveType(typeId: 117)
enum SortOrder {
  @HiveField(0)
  ascending,    // 升序
  @HiveField(1)
  descending,   // 降序
}
