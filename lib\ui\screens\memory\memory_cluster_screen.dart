import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../models/models.dart';
import '../../../modules/memory_system/memory_service.dart';
import '../../widgets/enhanced_loading.dart';
import '../../widgets/enhanced_card.dart';
import '../../widgets/enhanced_button.dart';

class MemoryClusterScreen extends ConsumerStatefulWidget {
  final String characterId;

  const MemoryClusterScreen({
    super.key,
    required this.characterId,
  });

  @override
  ConsumerState<MemoryClusterScreen> createState() => _MemoryClusterScreenState();
}

class _MemoryClusterScreenState extends ConsumerState<MemoryClusterScreen> {
  List<MemoryCluster> _clusters = [];
  Map<String, dynamic> _trends = {};
  bool _isLoading = true;
  bool _isClustering = false;
  Character? _character;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final memoryService = ref.read(memoryServiceProvider);
    final storageService = ref.read(storageServiceProvider);
    
    final character = await storageService.getCharacter(widget.characterId);
    final clusters = await memoryService.getMemoryClusters(widget.characterId);
    final trends = await memoryService.analyzeClusterTrends(widget.characterId);

    if (mounted) {
      setState(() {
        _character = character;
        _clusters = clusters;
        _trends = trends;
        _isLoading = false;
      });
    }
  }

  Future<void> _performClustering() async {
    setState(() {
      _isClustering = true;
    });

    try {
      final memoryService = ref.read(memoryServiceProvider);
      final newClusters = await memoryService.performMemoryClustering(widget.characterId);
      
      setState(() {
        _clusters = newClusters;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('生成了${newClusters.length}个记忆聚类')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('聚类失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isClustering = false;
      });
      await _loadData(); // 重新加载数据
    }
  }

  Future<void> _searchClusters(String query) async {
    if (query.isEmpty) {
      await _loadData();
      return;
    }

    final memoryService = ref.read(memoryServiceProvider);
    final results = await memoryService.searchMemoryClusters(
      characterId: widget.characterId,
      query: query,
    );

    setState(() {
      _clusters = results;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _character == null) {
      return const Scaffold(
        body: Center(
          child: EnhancedLoading(
            message: '正在加载记忆聚类...',
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${_character!.name} 的记忆聚类'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索栏
          _buildSearchBar(),
          
          // 趋势概览
          if (_trends.isNotEmpty) _buildTrendsCard(),
          
          // 聚类操作
          _buildClusteringActions(),
          
          // 聚类列表
          Expanded(
            child: _buildClusterList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索记忆聚类...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                    _loadData();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
          if (value.isNotEmpty) {
            _searchClusters(value);
          } else {
            _loadData();
          }
        },
      ),
    );
  }

  Widget _buildTrendsCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: EnhancedCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  '聚类概览',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildTrendMetric(
                    '聚类数量',
                    '${_trends['totalClusters'] ?? 0}',
                    Icons.category,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildTrendMetric(
                    '平均大小',
                    '${(_trends['averageClusterSize'] ?? 0.0).toStringAsFixed(1)}',
                    Icons.group_work,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            if (_trends['dominantThemes'] != null && (_trends['dominantThemes'] as List).isNotEmpty) ...[
              const Text(
                '主要主题:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 4,
                children: (_trends['dominantThemes'] as List<String>).take(3).map((theme) {
                  return Chip(
                    label: Text(theme),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTrendMetric(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClusteringActions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: EnhancedCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '聚类操作',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '重新分析记忆，生成新的聚类分组',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: EnhancedButton(
                text: _isClustering ? '正在聚类...' : '重新聚类',
                onPressed: _isClustering ? null : _performClustering,
                isLoading: _isClustering,
                icon: Icons.auto_awesome,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClusterList() {
    if (_clusters.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.category_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty ? '没有找到相关聚类' : '还没有记忆聚类',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _searchQuery.isNotEmpty 
                  ? '尝试使用其他关键词搜索'
                  : '点击"重新聚类"按钮来分析记忆',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _clusters.length,
      itemBuilder: (context, index) {
        final cluster = _clusters[index];
        return _buildClusterCard(cluster);
      },
    );
  }

  Widget _buildClusterCard(MemoryCluster cluster) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 聚类标题和重要性
          Row(
            children: [
              Expanded(
                child: Text(
                  cluster.theme,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getImportanceColor(cluster.importance).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.star,
                      size: 12,
                      color: _getImportanceColor(cluster.importance),
                    ),
                    const SizedBox(width: 2),
                    Text(
                      cluster.importance.toStringAsFixed(1),
                      style: TextStyle(
                        fontSize: 10,
                        color: _getImportanceColor(cluster.importance),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // 聚类描述
          Text(
            cluster.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          
          // 关键词
          if (cluster.keywords.isNotEmpty) ...[
            Wrap(
              spacing: 4,
              children: cluster.keywords.take(5).map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    keyword,
                    style: const TextStyle(fontSize: 10),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 8),
          ],
          
          // 统计信息
          Row(
            children: [
              Icon(
                Icons.memory,
                size: 14,
                color: Colors.grey[500],
              ),
              const SizedBox(width: 4),
              Text(
                '${cluster.memoryIds.length}个记忆',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
              const SizedBox(width: 16),
              Icon(
                Icons.timeline,
                size: 14,
                color: Colors.grey[500],
              ),
              const SizedBox(width: 4),
              Text(
                '连贯性 ${(cluster.coherenceScore * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
              const Spacer(),
              Text(
                DateFormat('MM/dd').format(cluster.createdAt),
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[400],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getImportanceColor(double importance) {
    if (importance >= 8) return Colors.red;
    if (importance >= 6) return Colors.orange;
    if (importance >= 4) return Colors.blue;
    return Colors.grey;
  }
}
