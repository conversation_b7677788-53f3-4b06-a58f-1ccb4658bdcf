import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../models/models.dart';
import '../../../modules/notification_system/notification_service.dart';
import '../../widgets/enhanced_loading.dart';
import '../../widgets/enhanced_card.dart';
import '../../widgets/enhanced_button.dart';

class NotificationCenterScreen extends ConsumerStatefulWidget {
  const NotificationCenterScreen({super.key});

  @override
  ConsumerState<NotificationCenterScreen> createState() => _NotificationCenterScreenState();
}

class _NotificationCenterScreenState extends ConsumerState<NotificationCenterScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<AppNotification> _allNotifications = [];
  List<AppNotification> _unreadNotifications = [];
  List<SmartNotificationSuggestion> _suggestions = [];
  NotificationSettings? _settings;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    final notificationService = ref.read(notificationServiceProvider);
    
    final allNotifications = await notificationService.getNotifications();
    final unreadNotifications = await notificationService.getNotifications(isRead: false);
    final suggestions = await notificationService.generateSmartSuggestions();
    final settings = await notificationService.getNotificationSettings();

    if (mounted) {
      setState(() {
        _allNotifications = allNotifications;
        _unreadNotifications = unreadNotifications;
        _suggestions = suggestions;
        _settings = settings;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: EnhancedLoading(
            message: '正在加载通知中心...',
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('通知中心'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: const Icon(Icons.notifications),
              text: '全部 (${_allNotifications.length})',
            ),
            Tab(
              icon: Badge(
                isLabelVisible: _unreadNotifications.isNotEmpty,
                label: Text('${_unreadNotifications.length}'),
                child: const Icon(Icons.mark_email_unread),
              ),
              text: '未读',
            ),
            Tab(
              icon: Badge(
                isLabelVisible: _suggestions.isNotEmpty,
                label: Text('${_suggestions.length}'),
                child: const Icon(Icons.lightbulb),
              ),
              text: '建议',
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              context.go('/notification-settings');
            },
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'mark_all_read',
                child: Row(
                  children: [
                    Icon(Icons.done_all),
                    SizedBox(width: 8),
                    Text('全部标为已读'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all),
                    SizedBox(width: 8),
                    Text('清空通知'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllNotificationsTab(),
          _buildUnreadNotificationsTab(),
          _buildSuggestionsTab(),
        ],
      ),
    );
  }

  Widget _buildAllNotificationsTab() {
    if (_allNotifications.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.notifications_none, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('暂无通知', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _allNotifications.length,
      itemBuilder: (context, index) {
        final notification = _allNotifications[index];
        return _buildNotificationCard(notification);
      },
    );
  }

  Widget _buildUnreadNotificationsTab() {
    if (_unreadNotifications.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle_outline, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text('没有未读通知', style: TextStyle(fontSize: 18, color: Colors.green)),
            SizedBox(height: 8),
            Text('您已查看所有通知！', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _unreadNotifications.length,
      itemBuilder: (context, index) {
        final notification = _unreadNotifications[index];
        return _buildNotificationCard(notification);
      },
    );
  }

  Widget _buildSuggestionsTab() {
    if (_suggestions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.lightbulb_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text('暂无智能建议', style: TextStyle(fontSize: 18, color: Colors.grey)),
            const SizedBox(height: 8),
            const Text('系统会根据您的使用习惯生成个性化建议', style: TextStyle(color: Colors.grey)),
            const SizedBox(height: 16),
            EnhancedButton(
              text: '生成建议',
              onPressed: _generateSuggestions,
              icon: Icons.auto_awesome,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = _suggestions[index];
        return _buildSuggestionCard(suggestion);
      },
    );
  }

  Widget _buildNotificationCard(AppNotification notification) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () => _handleNotificationTap(notification),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getNotificationTypeColor(notification.type).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getNotificationTypeLabel(notification.type),
                  style: TextStyle(
                    fontSize: 12,
                    color: _getNotificationTypeColor(notification.type),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (!notification.isRead) ...[
                const SizedBox(width: 8),
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getPriorityColor(notification.priority).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getPriorityLabel(notification.priority),
                  style: TextStyle(
                    fontSize: 10,
                    color: _getPriorityColor(notification.priority),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Text(
            notification.title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          
          Text(
            notification.body,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          
          if (notification.tags.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              children: notification.tags.map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    tag,
                    style: const TextStyle(fontSize: 10),
                  ),
                );
              }).toList(),
            ),
          ],
          
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 12,
                color: Colors.grey[500],
              ),
              const SizedBox(width: 4),
              Text(
                DateFormat('MM/dd HH:mm').format(notification.createdAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
              const Spacer(),
              if (notification.actions.isNotEmpty)
                TextButton(
                  onPressed: () => _showNotificationActions(notification),
                  child: const Text('操作'),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionCard(SmartNotificationSuggestion suggestion) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getSuggestionIcon(suggestion.type),
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  suggestion.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getConfidenceColor(suggestion.confidence).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${(suggestion.confidence * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 10,
                    color: _getConfidenceColor(suggestion.confidence),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Text(
            suggestion.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: suggestion.isApplied 
                      ? null 
                      : () => _applySuggestion(suggestion),
                  child: Text(suggestion.isApplied ? '已应用' : '应用'),
                ),
              ),
              const SizedBox(width: 8),
              OutlinedButton(
                onPressed: suggestion.isDismissed 
                    ? null 
                    : () => _dismissSuggestion(suggestion),
                child: const Text('忽略'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleNotificationTap(AppNotification notification) async {
    if (!notification.isRead) {
      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.markAsRead(notification.id);
      
      setState(() {
        final index = _allNotifications.indexWhere((n) => n.id == notification.id);
        if (index != -1) {
          _allNotifications[index] = notification.copyWith(isRead: true);
        }
        _unreadNotifications.removeWhere((n) => n.id == notification.id);
      });
    }

    // 处理通知动作
    if (notification.actionUrl != null) {
      // 导航到指定页面
      context.go(notification.actionUrl!);
    }
  }

  void _showNotificationActions(AppNotification notification) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '通知操作',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ...notification.actions.map((action) => ListTile(
              title: Text(action.title),
              onTap: () {
                Navigator.pop(context);
                _executeNotificationAction(action, notification);
              },
            )),
          ],
        ),
      ),
    );
  }

  void _executeNotificationAction(NotificationAction action, AppNotification notification) {
    switch (action.type) {
      case ActionType.open:
        // 打开应用
        break;
      case ActionType.reply:
        // 快速回复
        _showQuickReply(notification);
        break;
      case ActionType.dismiss:
        // 忽略
        _dismissNotification(notification);
        break;
      case ActionType.snooze:
        // 稍后提醒
        _snoozeNotification(notification);
        break;
      case ActionType.navigate:
        // 导航
        if (action.url != null) {
          context.go(action.url!);
        }
        break;
    }
  }

  void _showQuickReply(AppNotification notification) {
    // TODO: 实现快速回复功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('快速回复功能即将推出')),
    );
  }

  void _dismissNotification(AppNotification notification) async {
    final notificationService = ref.read(notificationServiceProvider);
    await notificationService.deleteNotification(notification.id);
    
    setState(() {
      _allNotifications.removeWhere((n) => n.id == notification.id);
      _unreadNotifications.removeWhere((n) => n.id == notification.id);
    });
  }

  void _snoozeNotification(AppNotification notification) {
    // TODO: 实现稍后提醒功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('稍后提醒功能即将推出')),
    );
  }

  Future<void> _applySuggestion(SmartNotificationSuggestion suggestion) async {
    try {
      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.applySmartSuggestion(suggestion.id);
      
      setState(() {
        final index = _suggestions.indexWhere((s) => s.id == suggestion.id);
        if (index != -1) {
          _suggestions[index] = suggestion.copyWith(isApplied: true);
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('建议已应用')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('应用失败: $e')),
        );
      }
    }
  }

  void _dismissSuggestion(SmartNotificationSuggestion suggestion) {
    setState(() {
      final index = _suggestions.indexWhere((s) => s.id == suggestion.id);
      if (index != -1) {
        _suggestions[index] = suggestion.copyWith(isDismissed: true);
      }
    });
  }

  Future<void> _generateSuggestions() async {
    try {
      final notificationService = ref.read(notificationServiceProvider);
      final newSuggestions = await notificationService.generateSmartSuggestions();
      
      setState(() {
        _suggestions = newSuggestions;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('生成了${newSuggestions.length}个建议')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('生成建议失败: $e')),
        );
      }
    }
  }

  void _handleMenuAction(String action) async {
    switch (action) {
      case 'mark_all_read':
        final notificationService = ref.read(notificationServiceProvider);
        await notificationService.markAllAsRead();
        await _loadData();
        break;
      case 'clear_all':
        _showClearAllDialog();
        break;
    }
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空所有通知'),
        content: const Text('确定要清空所有通知吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              // TODO: 实现清空所有通知
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('清空功能即将推出')),
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  String _getNotificationTypeLabel(NotificationType type) {
    switch (type) {
      case NotificationType.message:
        return '消息';
      case NotificationType.greeting:
        return '问候';
      case NotificationType.reminder:
        return '提醒';
      case NotificationType.milestone:
        return '里程碑';
      case NotificationType.emotion:
        return '情感';
      case NotificationType.memory:
        return '记忆';
      case NotificationType.system:
        return '系统';
      case NotificationType.update:
        return '更新';
      case NotificationType.achievement:
        return '成就';
      case NotificationType.recommendation:
        return '推荐';
      case NotificationType.conflict:
        return '冲突';
      case NotificationType.schedule:
        return '日程';
    }
  }

  Color _getNotificationTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.message:
        return Colors.blue;
      case NotificationType.greeting:
        return Colors.green;
      case NotificationType.reminder:
        return Colors.orange;
      case NotificationType.milestone:
        return Colors.purple;
      case NotificationType.emotion:
        return Colors.red;
      case NotificationType.memory:
        return Colors.teal;
      case NotificationType.system:
        return Colors.grey;
      case NotificationType.update:
        return Colors.indigo;
      case NotificationType.achievement:
        return Colors.amber;
      case NotificationType.recommendation:
        return Colors.cyan;
      case NotificationType.conflict:
        return Colors.deepOrange;
      case NotificationType.schedule:
        return Colors.pink;
    }
  }

  String _getPriorityLabel(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return '低';
      case NotificationPriority.normal:
        return '普通';
      case NotificationPriority.high:
        return '高';
      case NotificationPriority.urgent:
        return '紧急';
    }
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Colors.grey;
      case NotificationPriority.normal:
        return Colors.blue;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.urgent:
        return Colors.red;
    }
  }

  IconData _getSuggestionIcon(SuggestionType type) {
    switch (type) {
      case SuggestionType.quietHours:
        return Icons.bedtime;
      case SuggestionType.typeDisable:
        return Icons.notifications_off;
      case SuggestionType.priorityAdjust:
        return Icons.priority_high;
      case SuggestionType.batchSimilar:
        return Icons.group_work;
      case SuggestionType.scheduleOptimize:
        return Icons.schedule;
    }
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return Colors.green;
    if (confidence >= 0.6) return Colors.orange;
    return Colors.red;
  }
}
