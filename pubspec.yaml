name: aix_companion
description: AI虚拟伴侣应用 - 提供个性化、智能化的情感陪伴体验
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.6
  go_router: ^13.2.0
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # HTTP & Network
  http: ^1.1.2
  dio: ^5.4.0
  web_socket_channel: ^2.4.0
  
  # Local Storage
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # JSON & Serialization
  json_annotation: ^4.8.1
  freezed_annotation: ^2.4.1
  
  # Utils
  intl: ^0.19.0
  uuid: ^4.2.1
  crypto: ^3.0.3
  
  # AI & ML
  google_generative_ai: ^0.2.2
  
  # Notifications
  flutter_local_notifications: ^16.3.2
  
  # Permissions
  permission_handler: ^11.1.0
  
  # Image & Media
  image_picker: ^1.0.5
  cached_network_image: ^3.3.0
  
  # Animation
  lottie: ^2.7.0
  
  # Logging
  logger: ^2.0.2+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  freezed: ^2.4.6
  
  # Linting
  flutter_lints: ^3.0.1
  
  # Testing
  mockito: ^5.4.4

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/data/
    - assets/fonts/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
