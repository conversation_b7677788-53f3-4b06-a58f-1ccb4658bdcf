import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';

class CharacterService {
  final StorageService _storageService;

  CharacterService(this._storageService);

  // 获取所有角色
  Future<List<Character>> getAllCharacters() async {
    return await _storageService.getAllCharacters();
  }

  // 获取单个角色
  Future<Character?> getCharacter(String characterId) async {
    return await _storageService.getCharacter(characterId);
  }

  // 更新角色个性
  Future<void> updateCharacterPersonality(
    String characterId,
    PersonalityTraits newPersonality,
  ) async {
    final character = await _storageService.getCharacter(characterId);
    if (character != null) {
      final updatedCharacter = character.copyWith(personality: newPersonality);
      await _storageService.saveCharacter(updatedCharacter);
    }
  }

  // 更新角色兴趣
  Future<void> updateCharacterInterests(
    String characterId,
    List<String> newInterests,
  ) async {
    final character = await _storageService.getCharacter(characterId);
    if (character != null) {
      final updatedCharacter = character.copyWith(interests: newInterests);
      await _storageService.saveCharacter(updatedCharacter);
    }
  }

  // 更新角色特征
  Future<void> updateCharacterTraits(
    String characterId,
    List<String> newTraits,
  ) async {
    final character = await _storageService.getCharacter(characterId);
    if (character != null) {
      final updatedCharacter = character.copyWith(traits: newTraits);
      await _storageService.saveCharacter(updatedCharacter);
    }
  }

  // 更新说话风格
  Future<void> updateSpeakingStyle(
    String characterId,
    String newStyle,
  ) async {
    final character = await _storageService.getCharacter(characterId);
    if (character != null) {
      final updatedCharacter = character.copyWith(speakingStyle: newStyle);
      await _storageService.saveCharacter(updatedCharacter);
    }
  }

  // 更新亲密度
  Future<void> updateIntimacyLevel(
    String characterId,
    int newLevel,
  ) async {
    final character = await _storageService.getCharacter(characterId);
    if (character != null) {
      final updatedCharacter = character.copyWith(
        intimacyLevel: newLevel,
        lastInteraction: DateTime.now(),
      );
      await _storageService.saveCharacter(updatedCharacter);
    }
  }

  // 增加亲密度
  Future<void> increaseIntimacy(String characterId, int points) async {
    final character = await _storageService.getCharacter(characterId);
    if (character != null) {
      final newLevel = (character.intimacyLevel + points).clamp(0, 100);
      await updateIntimacyLevel(characterId, newLevel);
    }
  }

  // 创建自定义角色
  Future<Character> createCustomCharacter({
    required String name,
    required String description,
    required CharacterGender gender,
    required int age,
    required String occupation,
    required PersonalityTraits personality,
    required List<String> interests,
    required List<String> traits,
    String speakingStyle = 'casual',
  }) async {
    final character = Character(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description,
      avatarUrl: '',
      personality: personality,
      interests: interests,
      gender: gender,
      age: age,
      occupation: occupation,
      traits: traits,
      speakingStyle: speakingStyle,
    );

    await _storageService.saveCharacter(character);
    return character;
  }

  // 根据用户偏好调整角色个性
  Future<void> adaptPersonalityToUser(
    String characterId,
    Map<String, dynamic> userPreferences,
  ) async {
    final character = await _storageService.getCharacter(characterId);
    if (character == null) return;

    // 根据用户偏好微调角色个性
    var personality = character.personality;

    // 如果用户喜欢幽默，增加角色的幽默感
    if (userPreferences['humor'] == true) {
      personality = personality.copyWith(
        humor: (personality.humor + 0.1).clamp(0.0, 1.0),
      );
    }

    // 如果用户喜欢深度对话，增加角色的开放性
    if (userPreferences['deepConversation'] == true) {
      personality = personality.copyWith(
        openness: (personality.openness + 0.1).clamp(0.0, 1.0),
      );
    }

    // 如果用户喜欢情感支持，增加角色的共情能力
    if (userPreferences['emotionalSupport'] == true) {
      personality = personality.copyWith(
        empathy: (personality.empathy + 0.1).clamp(0.0, 1.0),
      );
    }

    await updateCharacterPersonality(characterId, personality);
  }

  // 获取角色个性化建议
  List<String> getPersonalizationSuggestions(Character character) {
    final suggestions = <String>[];

    if (character.personality.humor < 0.5) {
      suggestions.add('增加幽默感可以让对话更有趣');
    }

    if (character.personality.empathy < 0.6) {
      suggestions.add('提高共情能力可以更好地理解用户情感');
    }

    if (character.personality.creativity < 0.5) {
      suggestions.add('增强创造力可以产生更有趣的对话内容');
    }

    if (character.interests.length < 3) {
      suggestions.add('添加更多兴趣爱好可以丰富对话话题');
    }

    if (character.traits.length < 3) {
      suggestions.add('增加性格特征可以让角色更立体');
    }

    return suggestions;
  }

  // 分析角色与用户的匹配度
  double calculateCompatibility(Character character, Map<String, dynamic> userProfile) {
    double score = 0.0;
    int factors = 0;

    // 年龄匹配度
    if (userProfile['preferredAge'] != null) {
      final preferredAge = userProfile['preferredAge'] as int;
      final ageDiff = (character.age - preferredAge).abs();
      score += (10 - ageDiff.clamp(0, 10)) / 10.0;
      factors++;
    }

    // 性格匹配度
    if (userProfile['personalityPreferences'] != null) {
      final prefs = userProfile['personalityPreferences'] as Map<String, double>;
      
      prefs.forEach((trait, preference) {
        switch (trait) {
          case 'extroversion':
            score += 1.0 - (character.personality.extroversion - preference).abs();
            break;
          case 'humor':
            score += 1.0 - (character.personality.humor - preference).abs();
            break;
          case 'empathy':
            score += 1.0 - (character.personality.empathy - preference).abs();
            break;
        }
        factors++;
      });
    }

    // 兴趣匹配度
    if (userProfile['interests'] != null) {
      final userInterests = userProfile['interests'] as List<String>;
      final commonInterests = character.interests
          .where((interest) => userInterests.contains(interest))
          .length;
      score += commonInterests / character.interests.length;
      factors++;
    }

    return factors > 0 ? score / factors : 0.0;
  }
}

// Provider
final characterServiceProvider = Provider<CharacterService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return CharacterService(storageService);
});
