import 'dart:async';
import 'dart:isolate';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';

class PerformanceOptimizationService {
  final StorageService _storageService;
  
  // 缓存管理
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);
  
  // 批处理队列
  final List<Function> _batchQueue = [];
  Timer? _batchTimer;
  static const Duration _batchDelay = Duration(milliseconds: 500);
  
  // 内存监控
  final List<MemoryUsageSnapshot> _memorySnapshots = [];
  Timer? _memoryMonitorTimer;
  
  // 性能指标
  final Map<String, PerformanceMetric> _metrics = {};

  PerformanceOptimizationService(this._storageService) {
    _startMemoryMonitoring();
    _initializeMetrics();
  }

  // 缓存管理
  T? getCached<T>(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return null;
    
    if (DateTime.now().difference(timestamp) > _cacheExpiry) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
      return null;
    }
    
    return _cache[key] as T?;
  }

  void setCache<T>(String key, T value) {
    _cache[key] = value;
    _cacheTimestamps[key] = DateTime.now();
    
    // 限制缓存大小
    if (_cache.length > 100) {
      _evictOldestCache();
    }
  }

  void clearCache([String? pattern]) {
    if (pattern == null) {
      _cache.clear();
      _cacheTimestamps.clear();
    } else {
      final keysToRemove = _cache.keys.where((key) => key.contains(pattern)).toList();
      for (final key in keysToRemove) {
        _cache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
  }

  void _evictOldestCache() {
    if (_cacheTimestamps.isEmpty) return;
    
    final oldestEntry = _cacheTimestamps.entries
        .reduce((a, b) => a.value.isBefore(b.value) ? a : b);
    
    _cache.remove(oldestEntry.key);
    _cacheTimestamps.remove(oldestEntry.key);
  }

  // 批处理操作
  void addToBatch(Function operation) {
    _batchQueue.add(operation);
    
    _batchTimer?.cancel();
    _batchTimer = Timer(_batchDelay, _processBatch);
  }

  Future<void> _processBatch() async {
    if (_batchQueue.isEmpty) return;
    
    final operations = List<Function>.from(_batchQueue);
    _batchQueue.clear();
    
    // 在后台处理批量操作
    await _executeBatchOperations(operations);
  }

  Future<void> _executeBatchOperations(List<Function> operations) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // 分组相似操作
      final groupedOps = _groupOperations(operations);
      
      for (final group in groupedOps) {
        await _executeOperationGroup(group);
      }
      
      stopwatch.stop();
      _recordMetric('batch_processing_time', stopwatch.elapsedMilliseconds.toDouble());
      
    } catch (e) {
      print('批处理执行错误: $e');
    }
  }

  List<List<Function>> _groupOperations(List<Function> operations) {
    // 简化的分组逻辑，实际应用中可以更复杂
    final groups = <List<Function>>[];
    const groupSize = 10;
    
    for (int i = 0; i < operations.length; i += groupSize) {
      final end = (i + groupSize < operations.length) ? i + groupSize : operations.length;
      groups.add(operations.sublist(i, end));
    }
    
    return groups;
  }

  Future<void> _executeOperationGroup(List<Function> group) async {
    final futures = group.map((op) async {
      try {
        await op();
      } catch (e) {
        print('操作执行错误: $e');
      }
    });
    
    await Future.wait(futures);
  }

  // 内存优化
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _captureMemorySnapshot();
    });
  }

  void _captureMemorySnapshot() {
    // 在实际应用中，这里会使用 dart:developer 的内存分析工具
    final snapshot = MemoryUsageSnapshot(
      timestamp: DateTime.now(),
      heapUsage: _getEstimatedHeapUsage(),
      cacheSize: _cache.length,
      activeObjects: _getActiveObjectCount(),
    );
    
    _memorySnapshots.add(snapshot);
    
    // 保持最近100个快照
    if (_memorySnapshots.length > 100) {
      _memorySnapshots.removeAt(0);
    }
    
    // 检查内存使用情况
    _checkMemoryUsage(snapshot);
  }

  double _getEstimatedHeapUsage() {
    // 简化的内存使用估算
    return (_cache.length * 1024 + _memorySnapshots.length * 512).toDouble();
  }

  int _getActiveObjectCount() {
    // 估算活跃对象数量
    return _cache.length + _batchQueue.length + _metrics.length;
  }

  void _checkMemoryUsage(MemoryUsageSnapshot snapshot) {
    const memoryThreshold = 50 * 1024 * 1024; // 50MB
    
    if (snapshot.heapUsage > memoryThreshold) {
      _performMemoryCleanup();
    }
  }

  void _performMemoryCleanup() {
    // 清理过期缓存
    final now = DateTime.now();
    final expiredKeys = _cacheTimestamps.entries
        .where((entry) => now.difference(entry.value) > _cacheExpiry)
        .map((entry) => entry.key)
        .toList();
    
    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
    }
    
    // 清理旧的内存快照
    if (_memorySnapshots.length > 50) {
      _memorySnapshots.removeRange(0, _memorySnapshots.length - 50);
    }
    
    print('内存清理完成，释放了${expiredKeys.length}个缓存项');
  }

  // 数据库优化
  Future<List<T>> optimizedQuery<T>({
    required String cacheKey,
    required Future<List<T>> Function() queryFunction,
    Duration? customCacheExpiry,
  }) async {
    // 尝试从缓存获取
    final cached = getCached<List<T>>(cacheKey);
    if (cached != null) {
      _recordMetric('cache_hit', 1);
      return cached;
    }
    
    _recordMetric('cache_miss', 1);
    
    // 执行查询并缓存结果
    final stopwatch = Stopwatch()..start();
    final result = await queryFunction();
    stopwatch.stop();
    
    _recordMetric('query_time', stopwatch.elapsedMilliseconds.toDouble());
    
    // 缓存结果
    if (customCacheExpiry != null) {
      _cacheTimestamps[cacheKey] = DateTime.now().subtract(_cacheExpiry - customCacheExpiry);
    }
    setCache(cacheKey, result);
    
    return result;
  }

  // 异步操作优化
  Future<T> executeInIsolate<T>(
    Future<T> Function() computation,
    {String? debugName}
  ) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      // 在实际应用中，这里会使用 Isolate.spawn
      // 这里简化为直接执行
      final result = await computation();
      
      stopwatch.stop();
      _recordMetric('isolate_execution_time', stopwatch.elapsedMilliseconds.toDouble());
      
      return result;
    } catch (e) {
      stopwatch.stop();
      _recordMetric('isolate_execution_error', 1);
      rethrow;
    }
  }

  // 预加载优化
  Future<void> preloadData({
    required String characterId,
    bool includeMemories = true,
    bool includeConversations = true,
    bool includeEmotions = true,
  }) async {
    final futures = <Future>[];
    
    if (includeMemories) {
      futures.add(_preloadMemories(characterId));
    }
    
    if (includeConversations) {
      futures.add(_preloadConversations(characterId));
    }
    
    if (includeEmotions) {
      futures.add(_preloadEmotions(characterId));
    }
    
    await Future.wait(futures);
  }

  Future<void> _preloadMemories(String characterId) async {
    final cacheKey = 'memories_$characterId';
    await optimizedQuery(
      cacheKey: cacheKey,
      queryFunction: () => _storageService.getCharacterMemories(characterId, 'default_user'),
    );
  }

  Future<void> _preloadConversations(String characterId) async {
    final cacheKey = 'conversations_$characterId';
    await optimizedQuery(
      cacheKey: cacheKey,
      queryFunction: () => _storageService.getConversationsByCharacter(characterId),
    );
  }

  Future<void> _preloadEmotions(String characterId) async {
    final cacheKey = 'emotions_$characterId';
    await optimizedQuery(
      cacheKey: cacheKey,
      queryFunction: () => _storageService.getEmotionalStates('default_user', characterId),
    );
  }

  // 性能指标
  void _initializeMetrics() {
    _metrics['cache_hit'] = PerformanceMetric('缓存命中', 0);
    _metrics['cache_miss'] = PerformanceMetric('缓存未命中', 0);
    _metrics['query_time'] = PerformanceMetric('查询时间', 0);
    _metrics['batch_processing_time'] = PerformanceMetric('批处理时间', 0);
    _metrics['isolate_execution_time'] = PerformanceMetric('隔离执行时间', 0);
    _metrics['isolate_execution_error'] = PerformanceMetric('隔离执行错误', 0);
  }

  void _recordMetric(String name, double value) {
    final metric = _metrics[name];
    if (metric != null) {
      metric.addValue(value);
    }
  }

  Map<String, PerformanceMetric> getMetrics() {
    return Map.from(_metrics);
  }

  List<MemoryUsageSnapshot> getMemorySnapshots() {
    return List.from(_memorySnapshots);
  }

  // 性能报告
  PerformanceReport generateReport() {
    final now = DateTime.now();
    final cacheHitRate = _calculateCacheHitRate();
    final averageQueryTime = _metrics['query_time']?.average ?? 0;
    final memoryUsage = _memorySnapshots.isNotEmpty ? _memorySnapshots.last.heapUsage : 0;
    
    return PerformanceReport(
      generatedAt: now,
      cacheHitRate: cacheHitRate,
      averageQueryTime: averageQueryTime,
      currentMemoryUsage: memoryUsage,
      activeObjectCount: _getActiveObjectCount(),
      metrics: Map.from(_metrics),
      recommendations: _generateRecommendations(),
    );
  }

  double _calculateCacheHitRate() {
    final hits = _metrics['cache_hit']?.total ?? 0;
    final misses = _metrics['cache_miss']?.total ?? 0;
    final total = hits + misses;
    
    return total > 0 ? hits / total : 0;
  }

  List<String> _generateRecommendations() {
    final recommendations = <String>[];
    
    final cacheHitRate = _calculateCacheHitRate();
    if (cacheHitRate < 0.7) {
      recommendations.add('缓存命中率较低(${(cacheHitRate * 100).toInt()}%)，建议优化缓存策略');
    }
    
    final avgQueryTime = _metrics['query_time']?.average ?? 0;
    if (avgQueryTime > 100) {
      recommendations.add('平均查询时间较长(${avgQueryTime.toInt()}ms)，建议优化数据库查询');
    }
    
    if (_memorySnapshots.isNotEmpty) {
      final currentMemory = _memorySnapshots.last.heapUsage;
      if (currentMemory > 30 * 1024 * 1024) { // 30MB
        recommendations.add('内存使用量较高，建议进行内存清理');
      }
    }
    
    return recommendations;
  }

  // 清理资源
  void dispose() {
    _batchTimer?.cancel();
    _memoryMonitorTimer?.cancel();
    clearCache();
    _memorySnapshots.clear();
    _metrics.clear();
  }
}

// 数据模型
class MemoryUsageSnapshot {
  final DateTime timestamp;
  final double heapUsage;
  final int cacheSize;
  final int activeObjects;

  MemoryUsageSnapshot({
    required this.timestamp,
    required this.heapUsage,
    required this.cacheSize,
    required this.activeObjects,
  });
}

class PerformanceMetric {
  final String name;
  final List<double> _values = [];
  double _total = 0;

  PerformanceMetric(this.name, double initialValue) {
    if (initialValue != 0) {
      addValue(initialValue);
    }
  }

  void addValue(double value) {
    _values.add(value);
    _total += value;
    
    // 保持最近1000个值
    if (_values.length > 1000) {
      final removed = _values.removeAt(0);
      _total -= removed;
    }
  }

  double get total => _total;
  double get average => _values.isNotEmpty ? _total / _values.length : 0;
  double get max => _values.isNotEmpty ? _values.reduce((a, b) => a > b ? a : b) : 0;
  double get min => _values.isNotEmpty ? _values.reduce((a, b) => a < b ? a : b) : 0;
  int get count => _values.length;
}

class PerformanceReport {
  final DateTime generatedAt;
  final double cacheHitRate;
  final double averageQueryTime;
  final double currentMemoryUsage;
  final int activeObjectCount;
  final Map<String, PerformanceMetric> metrics;
  final List<String> recommendations;

  PerformanceReport({
    required this.generatedAt,
    required this.cacheHitRate,
    required this.averageQueryTime,
    required this.currentMemoryUsage,
    required this.activeObjectCount,
    required this.metrics,
    required this.recommendations,
  });
}

// Provider
final performanceOptimizationServiceProvider = Provider<PerformanceOptimizationService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return PerformanceOptimizationService(storageService);
});
