import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'character.freezed.dart';
part 'character.g.dart';

@freezed
@HiveType(typeId: 4)
class Character with _$Character {
  const factory Character({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required String description,
    @HiveField(3) required String avatarUrl,
    @HiveField(4) required PersonalityTraits personality,
    @HiveField(5) required List<String> interests,
    @HiveField(6) required CharacterGender gender,
    @HiveField(7) required int age,
    @HiveField(8) required String occupation,
    @HiveField(9) @Default([]) List<String> traits,
    @HiveField(10) @Default('casual') String speakingStyle,
    @HiveField(11) @Default(0) int intimacyLevel,
    @HiveField(12) @Default(true) bool isActive,
    @HiveField(13) DateTime? lastInteraction,
    @HiveField(14) Map<String, dynamic>? customAttributes,
  }) = _Character;

  factory Character.fromJson(Map<String, dynamic> json) => _$CharacterFromJson(json);
}

@freezed
@HiveType(typeId: 5)
class PersonalityTraits with _$PersonalityTraits {
  const factory PersonalityTraits({
    @HiveField(0) @Default(0.5) double extroversion, // 0.0 = 内向, 1.0 = 外向
    @HiveField(1) @Default(0.5) double agreeableness, // 0.0 = 竞争性, 1.0 = 合作性
    @HiveField(2) @Default(0.5) double conscientiousness, // 0.0 = 随性, 1.0 = 谨慎
    @HiveField(3) @Default(0.5) double neuroticism, // 0.0 = 稳定, 1.0 = 敏感
    @HiveField(4) @Default(0.5) double openness, // 0.0 = 传统, 1.0 = 开放
    @HiveField(5) @Default(0.5) double humor, // 0.0 = 严肃, 1.0 = 幽默
    @HiveField(6) @Default(0.5) double empathy, // 0.0 = 理性, 1.0 = 感性
    @HiveField(7) @Default(0.5) double creativity, // 0.0 = 实用, 1.0 = 创意
  }) = _PersonalityTraits;

  factory PersonalityTraits.fromJson(Map<String, dynamic> json) => _$PersonalityTraitsFromJson(json);
}

@HiveType(typeId: 6)
enum CharacterGender {
  @HiveField(0)
  male,
  @HiveField(1)
  female,
  @HiveField(2)
  nonBinary,
}
