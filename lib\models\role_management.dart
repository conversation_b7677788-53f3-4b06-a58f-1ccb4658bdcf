import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'role_management.freezed.dart';
part 'role_management.g.dart';

@freezed
@HiveType(typeId: 55)
class RoleTemplate with _$RoleTemplate {
  const factory RoleTemplate({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required String description,
    @HiveField(3) required String category,
    @HiveField(4) required RoleArchetype archetype,
    @HiveField(5) required List<String> traits,
    @HiveField(6) required List<String> interests,
    @HiveField(7) required Map<String, String> defaultAttributes,
    @HiveField(8) required String systemPrompt,
    @HiveField(9) required String greetingTemplate,
    @HiveField(10) @Default([]) List<String> tags,
    @HiveField(11) @Default(0) int popularity,
    @HiveField(12) @Default(false) bool isCustom,
    @HiveField(13) @Default(false) bool isPublic,
    @HiveField(14) required DateTime createdAt,
    @HiveField(15) String? createdBy,
    @HiveField(16) Map<String, dynamic>? metadata,
  }) = _RoleTemplate;

  factory RoleTemplate.fromJson(Map<String, dynamic> json) => _$RoleTemplateFromJson(json);
}

@HiveType(typeId: 56)
enum RoleArchetype {
  @HiveField(0)
  companion,    // 伴侣型
  @HiveField(1)
  friend,       // 朋友型
  @HiveField(2)
  mentor,       // 导师型
  @HiveField(3)
  assistant,    // 助手型
  @HiveField(4)
  entertainer,  // 娱乐型
  @HiveField(5)
  therapist,    // 治疗师型
  @HiveField(6)
  teacher,      // 教师型
  @HiveField(7)
  creative,     // 创意型
}

@freezed
@HiveType(typeId: 57)
class RoleCollection with _$RoleCollection {
  const factory RoleCollection({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required String description,
    @HiveField(3) required String userId,
    @HiveField(4) required List<String> characterIds,
    @HiveField(5) @Default([]) List<String> tags,
    @HiveField(6) @Default(false) bool isDefault,
    @HiveField(7) @Default(false) bool isShared,
    @HiveField(8) required DateTime createdAt,
    @HiveField(9) required DateTime updatedAt,
    @HiveField(10) Map<String, dynamic>? metadata,
  }) = _RoleCollection;

  factory RoleCollection.fromJson(Map<String, dynamic> json) => _$RoleCollectionFromJson(json);
}

@freezed
@HiveType(typeId: 58)
class RoleInteractionRule with _$RoleInteractionRule {
  const factory RoleInteractionRule({
    @HiveField(0) required String id,
    @HiveField(1) required String fromCharacterId,
    @HiveField(2) required String toCharacterId,
    @HiveField(3) required InteractionType type,
    @HiveField(4) required double strength,
    @HiveField(5) required List<String> contexts,
    @HiveField(6) required Map<String, String> responses,
    @HiveField(7) @Default(true) bool isActive,
    @HiveField(8) required DateTime createdAt,
    @HiveField(9) Map<String, dynamic>? conditions,
  }) = _RoleInteractionRule;

  factory RoleInteractionRule.fromJson(Map<String, dynamic> json) => _$RoleInteractionRuleFromJson(json);
}

@HiveType(typeId: 59)
enum InteractionType {
  @HiveField(0)
  friendly,     // 友好
  @HiveField(1)
  romantic,     // 浪漫
  @HiveField(2)
  competitive,  // 竞争
  @HiveField(3)
  supportive,   // 支持
  @HiveField(4)
  conflicting,  // 冲突
  @HiveField(5)
  neutral,      // 中性
  @HiveField(6)
  protective,   // 保护
  @HiveField(7)
  mentoring,    // 指导
}

@freezed
@HiveType(typeId: 60)
class RoleSchedule with _$RoleSchedule {
  const factory RoleSchedule({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required String userId,
    @HiveField(3) required ScheduleType type,
    @HiveField(4) required DateTime startTime,
    @HiveField(5) DateTime? endTime,
    @HiveField(6) required List<int> weekdays,
    @HiveField(7) required String activity,
    @HiveField(8) required String description,
    @HiveField(9) @Default(true) bool isActive,
    @HiveField(10) @Default(0) int priority,
    @HiveField(11) Map<String, dynamic>? settings,
  }) = _RoleSchedule;

  factory RoleSchedule.fromJson(Map<String, dynamic> json) => _$RoleScheduleFromJson(json);
}

@HiveType(typeId: 61)
enum ScheduleType {
  @HiveField(0)
  greeting,     // 问候
  @HiveField(1)
  reminder,     // 提醒
  @HiveField(2)
  activity,     // 活动
  @HiveField(3)
  checkIn,      // 签到
  @HiveField(4)
  story,        // 故事分享
  @HiveField(5)
  game,         // 游戏
}

@freezed
@HiveType(typeId: 62)
class RolePreset with _$RolePreset {
  const factory RolePreset({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required String description,
    @HiveField(3) required RoleArchetype archetype,
    @HiveField(4) required Map<String, dynamic> configuration,
    @HiveField(5) required List<String> requiredTraits,
    @HiveField(6) required List<String> suggestedInterests,
    @HiveField(7) @Default([]) List<String> compatibleWith,
    @HiveField(8) @Default([]) List<String> conflictsWith,
    @HiveField(9) @Default(0) double difficultyLevel,
    @HiveField(10) @Default(false) bool isPremium,
    @HiveField(11) required DateTime createdAt,
  }) = _RolePreset;

  factory RolePreset.fromJson(Map<String, dynamic> json) => _$RolePresetFromJson(json);
}

@freezed
@HiveType(typeId: 63)
class RoleAnalytics with _$RoleAnalytics {
  const factory RoleAnalytics({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required String userId,
    @HiveField(3) required DateTime analysisDate,
    @HiveField(4) required Map<String, double> performanceMetrics,
    @HiveField(5) required Map<String, int> interactionCounts,
    @HiveField(6) required double userSatisfaction,
    @HiveField(7) required double roleConsistency,
    @HiveField(8) required List<String> strengths,
    @HiveField(9) required List<String> improvements,
    @HiveField(10) required Map<String, dynamic> behaviorPatterns,
    @HiveField(11) Map<String, dynamic>? recommendations,
  }) = _RoleAnalytics;

  factory RoleAnalytics.fromJson(Map<String, dynamic> json) => _$RoleAnalyticsFromJson(json);
}

@freezed
@HiveType(typeId: 64)
class RoleCustomization with _$RoleCustomization {
  const factory RoleCustomization({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required String userId,
    @HiveField(3) required CustomizationType type,
    @HiveField(4) required String property,
    @HiveField(5) required dynamic value,
    @HiveField(6) dynamic previousValue,
    @HiveField(7) required DateTime appliedAt,
    @HiveField(8) @Default(true) bool isActive,
    @HiveField(9) String? reason,
    @HiveField(10) Map<String, dynamic>? metadata,
  }) = _RoleCustomization;

  factory RoleCustomization.fromJson(Map<String, dynamic> json) => _$RoleCustomizationFromJson(json);
}

@HiveType(typeId: 65)
enum CustomizationType {
  @HiveField(0)
  personality,  // 性格调整
  @HiveField(1)
  appearance,   // 外观调整
  @HiveField(2)
  behavior,     // 行为调整
  @HiveField(3)
  speech,       // 说话方式
  @HiveField(4)
  interests,    // 兴趣调整
  @HiveField(5)
  background,   // 背景故事
  @HiveField(6)
  skills,       // 技能调整
}

@freezed
@HiveType(typeId: 66)
class RoleEvolution with _$RoleEvolution {
  const factory RoleEvolution({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required String userId,
    @HiveField(3) required EvolutionTrigger trigger,
    @HiveField(4) required Map<String, dynamic> changes,
    @HiveField(5) required DateTime triggeredAt,
    @HiveField(6) required double impactScore,
    @HiveField(7) required String description,
    @HiveField(8) @Default(false) bool isReversible,
    @HiveField(9) Map<String, dynamic>? conditions,
  }) = _RoleEvolution;

  factory RoleEvolution.fromJson(Map<String, dynamic> json) => _$RoleEvolutionFromJson(json);
}

@HiveType(typeId: 67)
enum EvolutionTrigger {
  @HiveField(0)
  intimacyLevel,    // 亲密度达到
  @HiveField(1)
  timeSpent,        // 相处时间
  @HiveField(2)
  userBehavior,     // 用户行为
  @HiveField(3)
  specialEvent,     // 特殊事件
  @HiveField(4)
  userRequest,      // 用户要求
  @HiveField(5)
  systemUpdate,     // 系统更新
  @HiveField(6)
  seasonalChange,   // 季节变化
}

@freezed
@HiveType(typeId: 68)
class RoleConflict with _$RoleConflict {
  const factory RoleConflict({
    @HiveField(0) required String id,
    @HiveField(1) required List<String> characterIds,
    @HiveField(2) required String userId,
    @HiveField(3) required ConflictType type,
    @HiveField(4) required String description,
    @HiveField(5) required ConflictSeverity severity,
    @HiveField(6) required DateTime detectedAt,
    @HiveField(7) @Default(ConflictStatus.active) ConflictStatus status,
    @HiveField(8) @Default([]) List<String> resolutionOptions,
    @HiveField(9) String? chosenResolution,
    @HiveField(10) DateTime? resolvedAt,
  }) = _RoleConflict;

  factory RoleConflict.fromJson(Map<String, dynamic> json) => _$RoleConflictFromJson(json);
}

@HiveType(typeId: 69)
enum ConflictType {
  @HiveField(0)
  personalityClash,  // 性格冲突
  @HiveField(1)
  resourceCompetition, // 资源竞争
  @HiveField(2)
  attentionRivalry,  // 注意力竞争
  @HiveField(3)
  valueDisagreement, // 价值观分歧
  @HiveField(4)
  roleOverlap,       // 角色重叠
}

@HiveType(typeId: 70)
enum ConflictSeverity {
  @HiveField(0)
  low,      // 轻微
  @HiveField(1)
  medium,   // 中等
  @HiveField(2)
  high,     // 严重
  @HiveField(3)
  critical, // 关键
}

@HiveType(typeId: 71)
enum ConflictStatus {
  @HiveField(0)
  active,     // 活跃
  @HiveField(1)
  resolved,   // 已解决
  @HiveField(2)
  escalated,  // 升级
  @HiveField(3)
  ignored,    // 忽略
}

@freezed
@HiveType(typeId: 72)
class RoleRecommendation with _$RoleRecommendation {
  const factory RoleRecommendation({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required RecommendationType type,
    @HiveField(3) required String title,
    @HiveField(4) required String description,
    @HiveField(5) required double confidence,
    @HiveField(6) required Map<String, dynamic> data,
    @HiveField(7) required DateTime generatedAt,
    @HiveField(8) @Default(false) bool isViewed,
    @HiveField(9) @Default(false) bool isAccepted,
    @HiveField(10) String? feedback,
  }) = _RoleRecommendation;

  factory RoleRecommendation.fromJson(Map<String, dynamic> json) => _$RoleRecommendationFromJson(json);
}

@HiveType(typeId: 73)
enum RecommendationType {
  @HiveField(0)
  newRole,          // 新角色推荐
  @HiveField(1)
  roleImprovement,  // 角色改进
  @HiveField(2)
  interactionTip,   // 互动建议
  @HiveField(3)
  conflictResolution, // 冲突解决
  @HiveField(4)
  customization,    // 自定义建议
}
