import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../models/models.dart';
import '../../../modules/role_management/role_management_service.dart';
import '../../widgets/enhanced_loading.dart';
import '../../widgets/enhanced_card.dart';
import '../../widgets/enhanced_button.dart';

class RoleManagementScreen extends ConsumerStatefulWidget {
  const RoleManagementScreen({super.key});

  @override
  ConsumerState<RoleManagementScreen> createState() => _RoleManagementScreenState();
}

class _RoleManagementScreenState extends ConsumerState<RoleManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<Character> _characters = [];
  List<RoleCollection> _collections = [];
  List<RoleRecommendation> _recommendations = [];
  List<RoleConflict> _conflicts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    final storageService = ref.read(storageServiceProvider);
    final roleService = ref.read(roleManagementServiceProvider);
    
    final characters = await storageService.getAllCharacters();
    final collections = await roleService.getRoleCollections();
    final recommendations = await roleService.getRoleRecommendations();
    final conflicts = await roleService.getRoleConflicts();

    if (mounted) {
      setState(() {
        _characters = characters;
        _collections = collections;
        _recommendations = recommendations;
        _conflicts = conflicts;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: EnhancedLoading(
            message: '正在加载角色管理...',
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('角色管理'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.people), text: '我的角色'),
            Tab(icon: Icon(Icons.collections), text: '角色集合'),
            Tab(icon: Icon(Icons.lightbulb), text: '推荐'),
            Tab(icon: Icon(Icons.warning), text: '冲突'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCharactersTab(),
          _buildCollectionsTab(),
          _buildRecommendationsTab(),
          _buildConflictsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          _showCreateOptions(context);
        },
        icon: const Icon(Icons.add),
        label: const Text('创建'),
      ),
    );
  }

  Widget _buildCharactersTab() {
    if (_characters.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('还没有角色', style: TextStyle(fontSize: 18, color: Colors.grey)),
            SizedBox(height: 8),
            Text('点击右下角的"+"按钮创建第一个角色', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _characters.length,
      itemBuilder: (context, index) {
        final character = _characters[index];
        return _buildCharacterCard(character);
      },
    );
  }

  Widget _buildCharacterCard(Character character) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: _getCharacterColor(character.id),
                child: Text(
                  character.name[0],
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      character.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${character.age}岁 · ${character.occupation}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: character.isActive ? Colors.green[100] : Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  character.isActive ? '活跃' : '休眠',
                  style: TextStyle(
                    fontSize: 10,
                    color: character.isActive ? Colors.green[700] : Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          Text(
            character.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          
          // 特质标签
          Wrap(
            spacing: 4,
            children: character.traits.take(4).map((trait) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  trait,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.blue[700],
                  ),
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 12),
          
          // 操作按钮
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    context.go('/chat/${character.id}');
                  },
                  icon: const Icon(Icons.chat, size: 16),
                  label: const Text('聊天'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    context.go('/character-customization/${character.id}');
                  },
                  icon: const Icon(Icons.tune, size: 16),
                  label: const Text('自定义'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCollectionsTab() {
    if (_collections.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.collections_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('还没有角色集合', style: TextStyle(fontSize: 18, color: Colors.grey)),
            SizedBox(height: 8),
            Text('创建集合来组织您的角色', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _collections.length,
      itemBuilder: (context, index) {
        final collection = _collections[index];
        return _buildCollectionCard(collection);
      },
    );
  }

  Widget _buildCollectionCard(RoleCollection collection) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                collection.isDefault ? Icons.star : Icons.collections,
                color: collection.isDefault ? Colors.amber : Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  collection.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                '${collection.characterIds.length}个角色',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Text(
            collection.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          
          if (collection.tags.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              children: collection.tags.map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    tag,
                    style: const TextStyle(fontSize: 10),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecommendationsTab() {
    if (_recommendations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.lightbulb_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text('暂无推荐', style: TextStyle(fontSize: 18, color: Colors.grey)),
            const SizedBox(height: 8),
            const Text('系统会根据您的使用情况生成推荐', style: TextStyle(color: Colors.grey)),
            const SizedBox(height: 16),
            EnhancedButton(
              text: '生成推荐',
              onPressed: _generateRecommendations,
              icon: Icons.auto_awesome,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _recommendations.length,
      itemBuilder: (context, index) {
        final recommendation = _recommendations[index];
        return _buildRecommendationCard(recommendation);
      },
    );
  }

  Widget _buildRecommendationCard(RoleRecommendation recommendation) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getRecommendationIcon(recommendation.type),
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  recommendation.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getConfidenceColor(recommendation.confidence).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${(recommendation.confidence * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 10,
                    color: _getConfidenceColor(recommendation.confidence),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Text(
            recommendation.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    // TODO: 实现推荐操作
                  },
                  child: const Text('应用'),
                ),
              ),
              const SizedBox(width: 8),
              OutlinedButton(
                onPressed: () {
                  // TODO: 忽略推荐
                },
                child: const Text('忽略'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildConflictsTab() {
    if (_conflicts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle_outline, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text('没有冲突', style: TextStyle(fontSize: 18, color: Colors.green)),
            SizedBox(height: 8),
            Text('您的角色配置很和谐！', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _conflicts.length,
      itemBuilder: (context, index) {
        final conflict = _conflicts[index];
        return _buildConflictCard(conflict);
      },
    );
  }

  Widget _buildConflictCard(RoleConflict conflict) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning,
                color: _getSeverityColor(conflict.severity),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getConflictTypeLabel(conflict.type),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getSeverityColor(conflict.severity).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getSeverityLabel(conflict.severity),
                  style: TextStyle(
                    fontSize: 10,
                    color: _getSeverityColor(conflict.severity),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Text(
            conflict.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          
          if (conflict.resolutionOptions.isNotEmpty) ...[
            const SizedBox(height: 12),
            const Text(
              '解决方案:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 4),
            ...conflict.resolutionOptions.map((option) => Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 2),
              child: Row(
                children: [
                  const Icon(Icons.arrow_right, size: 12),
                  const SizedBox(width: 4),
                  Expanded(child: Text(option, style: const TextStyle(fontSize: 12))),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  void _showCreateOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.person_add),
              title: const Text('创建新角色'),
              onTap: () {
                Navigator.pop(context);
                context.go('/role-template-selection');
              },
            ),
            ListTile(
              leading: const Icon(Icons.collections),
              title: const Text('创建角色集合'),
              onTap: () {
                Navigator.pop(context);
                _showCreateCollectionDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.auto_awesome),
              title: const Text('生成推荐'),
              onTap: () {
                Navigator.pop(context);
                _generateRecommendations();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateCollectionDialog() {
    // TODO: 实现创建集合对话框
  }

  Future<void> _generateRecommendations() async {
    final roleService = ref.read(roleManagementServiceProvider);
    
    try {
      final newRecommendations = await roleService.generateRecommendations();
      setState(() {
        _recommendations = newRecommendations;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('生成了${newRecommendations.length}个推荐')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('生成推荐失败: $e')),
        );
      }
    }
  }

  Color _getCharacterColor(String characterId) {
    final colors = [Colors.pink, Colors.blue, Colors.purple, Colors.green, Colors.orange];
    return colors[characterId.hashCode % colors.length];
  }

  IconData _getRecommendationIcon(RecommendationType type) {
    switch (type) {
      case RecommendationType.newRole:
        return Icons.person_add;
      case RecommendationType.roleImprovement:
        return Icons.upgrade;
      case RecommendationType.interactionTip:
        return Icons.tips_and_updates;
      case RecommendationType.conflictResolution:
        return Icons.healing;
      case RecommendationType.customization:
        return Icons.tune;
    }
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return Colors.green;
    if (confidence >= 0.6) return Colors.orange;
    return Colors.red;
  }

  String _getConflictTypeLabel(ConflictType type) {
    switch (type) {
      case ConflictType.personalityClash:
        return '性格冲突';
      case ConflictType.resourceCompetition:
        return '资源竞争';
      case ConflictType.attentionRivalry:
        return '注意力竞争';
      case ConflictType.valueDisagreement:
        return '价值观分歧';
      case ConflictType.roleOverlap:
        return '角色重叠';
    }
  }

  String _getSeverityLabel(ConflictSeverity severity) {
    switch (severity) {
      case ConflictSeverity.low:
        return '轻微';
      case ConflictSeverity.medium:
        return '中等';
      case ConflictSeverity.high:
        return '严重';
      case ConflictSeverity.critical:
        return '关键';
    }
  }

  Color _getSeverityColor(ConflictSeverity severity) {
    switch (severity) {
      case ConflictSeverity.low:
        return Colors.yellow;
      case ConflictSeverity.medium:
        return Colors.orange;
      case ConflictSeverity.high:
        return Colors.red;
      case ConflictSeverity.critical:
        return Colors.red[900]!;
    }
  }
}
