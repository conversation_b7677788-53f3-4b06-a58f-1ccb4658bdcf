import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../models/models.dart';
import '../../../modules/group_conversation/group_conversation_service.dart';
import '../../widgets/enhanced_loading.dart';
import '../../widgets/enhanced_card.dart';
import '../../widgets/enhanced_button.dart';

class GroupConversationScreen extends ConsumerStatefulWidget {
  const GroupConversationScreen({super.key});

  @override
  ConsumerState<GroupConversationScreen> createState() => _GroupConversationScreenState();
}

class _GroupConversationScreenState extends ConsumerState<GroupConversationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<GroupConversation> _groups = [];
  List<ConversationScenario> _scenarios = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    final groupService = ref.read(groupConversationServiceProvider);
    
    final groups = await groupService.getGroups(userId: 'default_user');
    final scenarios = await groupService.getScenarios();

    if (mounted) {
      setState(() {
        _groups = groups;
        _scenarios = scenarios;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: EnhancedLoading(
            message: '正在加载群组对话...',
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('群组对话'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: Badge(
                isLabelVisible: _groups.isNotEmpty,
                label: Text('${_groups.length}'),
                child: const Icon(Icons.groups),
              ),
              text: '我的群组',
            ),
            Tab(
              icon: Badge(
                isLabelVisible: _scenarios.isNotEmpty,
                label: Text('${_scenarios.length}'),
                child: const Icon(Icons.theater_comedy),
              ),
              text: '对话场景',
            ),
            const Tab(
              icon: Icon(Icons.add),
              text: '创建群组',
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildGroupsTab(),
          _buildScenariosTab(),
          _buildCreateGroupTab(),
        ],
      ),
    );
  }

  Widget _buildGroupsTab() {
    if (_groups.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.groups_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('暂无群组对话', style: TextStyle(fontSize: 18, color: Colors.grey)),
            SizedBox(height: 8),
            Text('创建一个群组开始多人对话吧！', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _groups.length,
      itemBuilder: (context, index) {
        final group = _groups[index];
        return _buildGroupCard(group);
      },
    );
  }

  Widget _buildGroupCard(GroupConversation group) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () => _enterGroup(group),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: _getGroupTypeColor(group.type),
                child: Icon(
                  _getGroupTypeIcon(group.type),
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      group.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      group.description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getGroupStatusColor(group.status).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getGroupStatusLabel(group.status),
                  style: TextStyle(
                    fontSize: 10,
                    color: _getGroupStatusColor(group.status),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Icon(Icons.people, size: 16, color: Colors.grey[500]),
              const SizedBox(width: 4),
              Text(
                '${group.participantIds.length}人',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 16),
              Icon(Icons.chat, size: 16, color: Colors.grey[500]),
              const SizedBox(width: 4),
              Text(
                '${group.messageCount}条消息',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              if (group.lastActivity != null)
                Text(
                  DateFormat('MM/dd HH:mm').format(group.lastActivity!),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
            ],
          ),
          
          if (group.tags.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              children: group.tags.take(3).map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    tag,
                    style: const TextStyle(fontSize: 10),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildScenariosTab() {
    if (_scenarios.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.theater_comedy_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('暂无对话场景', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _scenarios.length,
      itemBuilder: (context, index) {
        final scenario = _scenarios[index];
        return _buildScenarioCard(scenario);
      },
    );
  }

  Widget _buildScenarioCard(ConversationScenario scenario) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () => _showScenarioDetails(scenario),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getScenarioTypeIcon(scenario.type),
                color: _getScenarioTypeColor(scenario.type),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      scenario.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      scenario.description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getDifficultyColor(scenario.difficulty).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getDifficultyLabel(scenario.difficulty),
                  style: TextStyle(
                    fontSize: 10,
                    color: _getDifficultyColor(scenario.difficulty),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Row(
            children: [
              Icon(Icons.people, size: 14, color: Colors.grey[500]),
              const SizedBox(width: 4),
              Text(
                '需要${scenario.requiredCharacters.length}人',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 16),
              Icon(Icons.timer, size: 14, color: Colors.grey[500]),
              const SizedBox(width: 4),
              Text(
                '约${scenario.estimatedDuration}分钟',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              if (scenario.averageRating > 0) ...[
                Icon(Icons.star, size: 14, color: Colors.amber),
                const SizedBox(width: 2),
                Text(
                  scenario.averageRating.toStringAsFixed(1),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ],
          ),
          
          if (scenario.tags.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              children: scenario.tags.take(3).map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getScenarioTypeColor(scenario.type).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    tag,
                    style: TextStyle(
                      fontSize: 10,
                      color: _getScenarioTypeColor(scenario.type),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCreateGroupTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '创建新群组',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          const Text('选择群组类型：'),
          const SizedBox(height: 8),
          
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.2,
              ),
              itemCount: GroupType.values.length,
              itemBuilder: (context, index) {
                final type = GroupType.values[index];
                return _buildGroupTypeCard(type);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupTypeCard(GroupType type) {
    return EnhancedCard(
      onTap: () => _createGroup(type),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getGroupTypeIcon(type),
            size: 32,
            color: _getGroupTypeColor(type),
          ),
          const SizedBox(height: 8),
          Text(
            _getGroupTypeLabel(type),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            _getGroupTypeDescription(type),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _enterGroup(GroupConversation group) {
    context.go('/group-chat/${group.id}');
  }

  void _createGroup(GroupType type) async {
    // 获取所有角色
    final characters = await ref.read(storageServiceProvider).getAllCharacters();
    
    if (characters.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('至少需要2个角色才能创建群组')),
      );
      return;
    }

    // 创建群组
    final groupService = ref.read(groupConversationServiceProvider);
    
    try {
      final group = await groupService.createGroup(
        name: '${_getGroupTypeLabel(type)}群组',
        description: _getGroupTypeDescription(type),
        participantIds: characters.take(3).map((c) => c.id).toList(), // 取前3个角色
        type: type,
        tags: [_getGroupTypeLabel(type)],
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('群组"${group.name}"创建成功！')),
      );
      
      _loadData();
      _enterGroup(group);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('创建群组失败：$e')),
      );
    }
  }

  void _showScenarioDetails(ConversationScenario scenario) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                scenario.name,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 16),
              
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '场景描述',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(scenario.description),
                      const SizedBox(height: 16),
                      
                      Text(
                        '场景信息',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      _buildDetailRow('类型', _getScenarioTypeLabel(scenario.type)),
                      _buildDetailRow('难度', _getDifficultyLabel(scenario.difficulty)),
                      _buildDetailRow('预计时长', '${scenario.estimatedDuration}分钟'),
                      _buildDetailRow('需要角色', '${scenario.requiredCharacters.length}个'),
                      _buildDetailRow('使用次数', '${scenario.usageCount}次'),
                      if (scenario.averageRating > 0)
                        _buildDetailRow('平均评分', scenario.averageRating.toStringAsFixed(1)),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _startScenario(scenario);
                  },
                  child: const Text('开始场景'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _startScenario(ConversationScenario scenario) {
    // TODO: 实现场景启动功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('场景"${scenario.name}"启动功能即将推出')),
    );
  }

  // 辅助方法
  String _getGroupTypeLabel(GroupType type) {
    switch (type) {
      case GroupType.casual:
        return '休闲聊天';
      case GroupType.roleplay:
        return '角色扮演';
      case GroupType.debate:
        return '辩论讨论';
      case GroupType.support:
        return '支持群组';
      case GroupType.learning:
        return '学习交流';
      case GroupType.gaming:
        return '游戏互动';
      case GroupType.creative:
        return '创意协作';
      case GroupType.therapy:
        return '心理治疗';
      case GroupType.social:
        return '社交聚会';
      case GroupType.professional:
        return '专业讨论';
    }
  }

  String _getGroupTypeDescription(GroupType type) {
    switch (type) {
      case GroupType.casual:
        return '轻松愉快的日常聊天';
      case GroupType.roleplay:
        return '沉浸式角色扮演体验';
      case GroupType.debate:
        return '理性讨论和辩论';
      case GroupType.support:
        return '互相支持和鼓励';
      case GroupType.learning:
        return '知识分享和学习';
      case GroupType.gaming:
        return '游戏相关的互动';
      case GroupType.creative:
        return '创意思维和协作';
      case GroupType.therapy:
        return '心理健康和治疗';
      case GroupType.social:
        return '社交活动和聚会';
      case GroupType.professional:
        return '专业领域讨论';
    }
  }

  IconData _getGroupTypeIcon(GroupType type) {
    switch (type) {
      case GroupType.casual:
        return Icons.chat;
      case GroupType.roleplay:
        return Icons.theater_comedy;
      case GroupType.debate:
        return Icons.forum;
      case GroupType.support:
        return Icons.favorite;
      case GroupType.learning:
        return Icons.school;
      case GroupType.gaming:
        return Icons.games;
      case GroupType.creative:
        return Icons.palette;
      case GroupType.therapy:
        return Icons.psychology;
      case GroupType.social:
        return Icons.celebration;
      case GroupType.professional:
        return Icons.work;
    }
  }

  Color _getGroupTypeColor(GroupType type) {
    switch (type) {
      case GroupType.casual:
        return Colors.blue;
      case GroupType.roleplay:
        return Colors.purple;
      case GroupType.debate:
        return Colors.orange;
      case GroupType.support:
        return Colors.pink;
      case GroupType.learning:
        return Colors.green;
      case GroupType.gaming:
        return Colors.red;
      case GroupType.creative:
        return Colors.indigo;
      case GroupType.therapy:
        return Colors.teal;
      case GroupType.social:
        return Colors.amber;
      case GroupType.professional:
        return Colors.grey;
    }
  }

  String _getGroupStatusLabel(GroupStatus status) {
    switch (status) {
      case GroupStatus.active:
        return '活跃';
      case GroupStatus.paused:
        return '暂停';
      case GroupStatus.archived:
        return '归档';
      case GroupStatus.private:
        return '私有';
      case GroupStatus.restricted:
        return '受限';
    }
  }

  Color _getGroupStatusColor(GroupStatus status) {
    switch (status) {
      case GroupStatus.active:
        return Colors.green;
      case GroupStatus.paused:
        return Colors.orange;
      case GroupStatus.archived:
        return Colors.grey;
      case GroupStatus.private:
        return Colors.blue;
      case GroupStatus.restricted:
        return Colors.red;
    }
  }

  String _getScenarioTypeLabel(ScenarioType type) {
    switch (type) {
      case ScenarioType.icebreaker:
        return '破冰对话';
      case ScenarioType.conflict:
        return '冲突解决';
      case ScenarioType.celebration:
        return '庆祝活动';
      case ScenarioType.problemSolving:
        return '问题解决';
      case ScenarioType.storytelling:
        return '故事讲述';
      case ScenarioType.debate:
        return '辩论讨论';
      case ScenarioType.support:
        return '情感支持';
      case ScenarioType.learning:
        return '学习交流';
      case ScenarioType.creative:
        return '创意协作';
      case ScenarioType.social:
        return '社交互动';
      case ScenarioType.therapeutic:
        return '治疗性对话';
      case ScenarioType.competitive:
        return '竞争性活动';
    }
  }

  IconData _getScenarioTypeIcon(ScenarioType type) {
    switch (type) {
      case ScenarioType.icebreaker:
        return Icons.handshake;
      case ScenarioType.conflict:
        return Icons.mediation;
      case ScenarioType.celebration:
        return Icons.celebration;
      case ScenarioType.problemSolving:
        return Icons.lightbulb;
      case ScenarioType.storytelling:
        return Icons.auto_stories;
      case ScenarioType.debate:
        return Icons.forum;
      case ScenarioType.support:
        return Icons.support;
      case ScenarioType.learning:
        return Icons.school;
      case ScenarioType.creative:
        return Icons.create;
      case ScenarioType.social:
        return Icons.people;
      case ScenarioType.therapeutic:
        return Icons.healing;
      case ScenarioType.competitive:
        return Icons.emoji_events;
    }
  }

  Color _getScenarioTypeColor(ScenarioType type) {
    switch (type) {
      case ScenarioType.icebreaker:
        return Colors.lightBlue;
      case ScenarioType.conflict:
        return Colors.orange;
      case ScenarioType.celebration:
        return Colors.amber;
      case ScenarioType.problemSolving:
        return Colors.green;
      case ScenarioType.storytelling:
        return Colors.purple;
      case ScenarioType.debate:
        return Colors.red;
      case ScenarioType.support:
        return Colors.pink;
      case ScenarioType.learning:
        return Colors.blue;
      case ScenarioType.creative:
        return Colors.indigo;
      case ScenarioType.social:
        return Colors.teal;
      case ScenarioType.therapeutic:
        return Colors.cyan;
      case ScenarioType.competitive:
        return Colors.deepOrange;
    }
  }

  String _getDifficultyLabel(double difficulty) {
    if (difficulty <= 1.0) return '简单';
    if (difficulty <= 2.0) return '中等';
    if (difficulty <= 3.0) return '困难';
    return '专家';
  }

  Color _getDifficultyColor(double difficulty) {
    if (difficulty <= 1.0) return Colors.green;
    if (difficulty <= 2.0) return Colors.orange;
    if (difficulty <= 3.0) return Colors.red;
    return Colors.purple;
  }
}
