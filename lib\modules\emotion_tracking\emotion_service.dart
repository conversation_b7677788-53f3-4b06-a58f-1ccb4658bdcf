import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';

class EmotionService {
  final StorageService _storageService;
  final Uuid _uuid = const Uuid();
  
  static const String _defaultUserId = 'default_user';

  EmotionService(this._storageService);

  // 分析消息的情感状态
  Future<EmotionalState> analyzeMessageEmotion({
    required String characterId,
    required Message message,
  }) async {
    final emotions = _extractEmotions(message.content);
    final valence = _calculateValence(emotions);
    final arousal = _calculateArousal(emotions);
    final intensity = _determineIntensity(emotions, valence, arousal);
    final dominantEmotion = _findDominantEmotion(emotions);
    final keywords = _extractEmotionalKeywords(message.content);

    final emotionalState = EmotionalState(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      timestamp: message.timestamp,
      emotions: emotions,
      valence: valence,
      arousal: arousal,
      intensity: intensity,
      triggerMessageId: message.id,
      dominantEmotion: dominantEmotion?.name,
      emotionalKeywords: keywords,
      confidence: _calculateConfidence(emotions, keywords),
    );

    await _storageService.saveEmotionalState(emotionalState);
    
    // 检测情感触发器
    await _detectEmotionalTriggers(characterId, message, emotionalState);
    
    return emotionalState;
  }

  // 提取情感
  Map<EmotionType, double> _extractEmotions(String content) {
    final emotions = <EmotionType, double>{};
    final lowerContent = content.toLowerCase();

    // 快乐相关
    final joyKeywords = ['开心', '高兴', '快乐', '兴奋', '愉快', '满足', '幸福', '欢乐', '喜悦'];
    emotions[EmotionType.joy] = _calculateEmotionScore(lowerContent, joyKeywords);

    // 悲伤相关
    final sadnessKeywords = ['难过', '伤心', '沮丧', '失望', '痛苦', '悲伤', '郁闷', '低落'];
    emotions[EmotionType.sadness] = _calculateEmotionScore(lowerContent, sadnessKeywords);

    // 愤怒相关
    final angerKeywords = ['生气', '愤怒', '烦躁', '恼火', '气愤', '暴躁', '不爽'];
    emotions[EmotionType.anger] = _calculateEmotionScore(lowerContent, angerKeywords);

    // 恐惧相关
    final fearKeywords = ['害怕', '恐惧', '担心', '紧张', '不安', '焦虑', '惊慌'];
    emotions[EmotionType.fear] = _calculateEmotionScore(lowerContent, fearKeywords);

    // 惊讶相关
    final surpriseKeywords = ['惊讶', '震惊', '意外', '没想到', '想不到', '吃惊'];
    emotions[EmotionType.surprise] = _calculateEmotionScore(lowerContent, surpriseKeywords);

    // 厌恶相关
    final disgustKeywords = ['讨厌', '恶心', '厌恶', '反感', '不喜欢'];
    emotions[EmotionType.disgust] = _calculateEmotionScore(lowerContent, disgustKeywords);

    // 信任相关
    final trustKeywords = ['信任', '相信', '依赖', '可靠', '安全'];
    emotions[EmotionType.trust] = _calculateEmotionScore(lowerContent, trustKeywords);

    // 期待相关
    final anticipationKeywords = ['期待', '期望', '希望', '盼望', '等待'];
    emotions[EmotionType.anticipation] = _calculateEmotionScore(lowerContent, anticipationKeywords);

    // 爱相关
    final loveKeywords = ['爱', '喜欢', '爱情', '恋爱', '深爱', '热爱'];
    emotions[EmotionType.love] = _calculateEmotionScore(lowerContent, loveKeywords);

    // 兴奋相关
    final excitementKeywords = ['激动', '兴奋', '刺激', '振奋', '热情'];
    emotions[EmotionType.excitement] = _calculateEmotionScore(lowerContent, excitementKeywords);

    // 焦虑相关
    final anxietyKeywords = ['焦虑', '不安', '忧虑', '担忧', '紧张'];
    emotions[EmotionType.anxiety] = _calculateEmotionScore(lowerContent, anxietyKeywords);

    // 沮丧相关
    final frustrationKeywords = ['沮丧', '挫败', '失落', '无奈', '绝望'];
    emotions[EmotionType.frustration] = _calculateEmotionScore(lowerContent, frustrationKeywords);

    // 满足相关
    final contentmentKeywords = ['满足', '知足', '安逸', '舒适', '平静'];
    emotions[EmotionType.contentment] = _calculateEmotionScore(lowerContent, contentmentKeywords);

    // 孤独相关
    final lonelinessKeywords = ['孤独', '寂寞', '孤单', '独自', '一个人'];
    emotions[EmotionType.loneliness] = _calculateEmotionScore(lowerContent, lonelinessKeywords);

    // 感激相关
    final gratitudeKeywords = ['感谢', '感激', '感恩', '谢谢', '感谢你'];
    emotions[EmotionType.gratitude] = _calculateEmotionScore(lowerContent, gratitudeKeywords);

    // 困惑相关
    final confusionKeywords = ['困惑', '迷惑', '不明白', '不懂', '疑惑'];
    emotions[EmotionType.confusion] = _calculateEmotionScore(lowerContent, confusionKeywords);

    return emotions;
  }

  // 计算情感分数
  double _calculateEmotionScore(String content, List<String> keywords) {
    double score = 0.0;
    for (final keyword in keywords) {
      if (content.contains(keyword)) {
        score += 1.0;
      }
    }
    return (score / keywords.length).clamp(0.0, 1.0);
  }

  // 计算情感效价
  double _calculateValence(Map<EmotionType, double> emotions) {
    final positiveEmotions = [
      EmotionType.joy,
      EmotionType.love,
      EmotionType.excitement,
      EmotionType.contentment,
      EmotionType.gratitude,
      EmotionType.trust,
    ];
    
    final negativeEmotions = [
      EmotionType.sadness,
      EmotionType.anger,
      EmotionType.fear,
      EmotionType.disgust,
      EmotionType.anxiety,
      EmotionType.frustration,
      EmotionType.loneliness,
    ];

    double positiveScore = 0.0;
    double negativeScore = 0.0;

    for (final emotion in positiveEmotions) {
      positiveScore += emotions[emotion] ?? 0.0;
    }

    for (final emotion in negativeEmotions) {
      negativeScore += emotions[emotion] ?? 0.0;
    }

    if (positiveScore + negativeScore == 0) return 0.0;
    
    return (positiveScore - negativeScore) / (positiveScore + negativeScore);
  }

  // 计算情感唤醒度
  double _calculateArousal(Map<EmotionType, double> emotions) {
    final highArousalEmotions = [
      EmotionType.excitement,
      EmotionType.anger,
      EmotionType.fear,
      EmotionType.surprise,
      EmotionType.anxiety,
    ];

    double arousalScore = 0.0;
    for (final emotion in highArousalEmotions) {
      arousalScore += emotions[emotion] ?? 0.0;
    }

    return (arousalScore / highArousalEmotions.length).clamp(0.0, 1.0);
  }

  // 确定情感强度
  EmotionIntensity _determineIntensity(
    Map<EmotionType, double> emotions,
    double valence,
    double arousal,
  ) {
    final maxEmotion = emotions.values.isNotEmpty 
        ? emotions.values.reduce((a, b) => a > b ? a : b)
        : 0.0;
    
    final intensityScore = (maxEmotion + arousal.abs() + valence.abs()) / 3;

    if (intensityScore >= 0.8) return EmotionIntensity.extreme;
    if (intensityScore >= 0.6) return EmotionIntensity.high;
    if (intensityScore >= 0.3) return EmotionIntensity.medium;
    return EmotionIntensity.low;
  }

  // 找到主导情感
  EmotionType? _findDominantEmotion(Map<EmotionType, double> emotions) {
    if (emotions.isEmpty) return null;
    
    EmotionType? dominant;
    double maxScore = 0.0;
    
    emotions.forEach((emotion, score) {
      if (score > maxScore) {
        maxScore = score;
        dominant = emotion;
      }
    });
    
    return maxScore > 0.1 ? dominant : null;
  }

  // 提取情感关键词
  List<String> _extractEmotionalKeywords(String content) {
    final keywords = <String>[];
    final emotionalWords = [
      '开心', '高兴', '快乐', '兴奋', '愉快', '满足', '幸福',
      '难过', '伤心', '沮丧', '失望', '痛苦', '悲伤',
      '生气', '愤怒', '烦躁', '恼火', '气愤',
      '害怕', '恐惧', '担心', '紧张', '不安', '焦虑',
      '惊讶', '震惊', '意外', '没想到',
      '讨厌', '恶心', '厌恶', '反感',
      '信任', '相信', '依赖', '可靠',
      '期待', '期望', '希望', '盼望',
      '爱', '喜欢', '爱情', '恋爱',
      '感谢', '感激', '感恩', '谢谢',
    ];

    for (final word in emotionalWords) {
      if (content.contains(word)) {
        keywords.add(word);
      }
    }

    return keywords;
  }

  // 计算置信度
  double _calculateConfidence(Map<EmotionType, double> emotions, List<String> keywords) {
    final emotionCount = emotions.values.where((score) => score > 0).length;
    final keywordCount = keywords.length;
    
    if (emotionCount == 0 && keywordCount == 0) return 0.0;
    
    final emotionConfidence = emotionCount / emotions.length;
    final keywordConfidence = keywordCount > 0 ? 1.0 : 0.0;
    
    return (emotionConfidence + keywordConfidence) / 2;
  }

  // 检测情感触发器
  Future<void> _detectEmotionalTriggers(
    String characterId,
    Message message,
    EmotionalState emotionalState,
  ) async {
    if (emotionalState.dominantEmotion == null) return;

    final dominantEmotion = EmotionType.values.firstWhere(
      (e) => e.name == emotionalState.dominantEmotion,
    );

    // 检测关键词触发器
    for (final keyword in emotionalState.emotionalKeywords) {
      await _updateOrCreateTrigger(
        characterId: characterId,
        trigger: keyword,
        type: TriggerType.keyword,
        resultingEmotion: dominantEmotion,
        messageId: message.id,
        strength: emotionalState.emotions[dominantEmotion] ?? 0.0,
      );
    }
  }

  // 更新或创建触发器
  Future<void> _updateOrCreateTrigger({
    required String characterId,
    required String trigger,
    required TriggerType type,
    required EmotionType resultingEmotion,
    required String messageId,
    required double strength,
  }) async {
    final existingTrigger = await _storageService.getEmotionalTrigger(
      _defaultUserId,
      characterId,
      trigger,
      type,
    );

    if (existingTrigger != null) {
      // 更新现有触发器
      final updatedTrigger = existingTrigger.copyWith(
        strength: (existingTrigger.strength + strength) / 2,
        occurrenceCount: existingTrigger.occurrenceCount + 1,
        lastOccurred: DateTime.now(),
        relatedMessageIds: [...existingTrigger.relatedMessageIds, messageId],
      );
      await _storageService.saveEmotionalTrigger(updatedTrigger);
    } else {
      // 创建新触发器
      final newTrigger = EmotionalTrigger(
        id: _uuid.v4(),
        userId: _defaultUserId,
        characterId: characterId,
        trigger: trigger,
        type: type,
        resultingEmotion: resultingEmotion,
        strength: strength,
        occurrenceCount: 1,
        firstDetected: DateTime.now(),
        lastOccurred: DateTime.now(),
        relatedMessageIds: [messageId],
      );
      await _storageService.saveEmotionalTrigger(newTrigger);
    }
  }

  // 生成每日情绪摘要
  Future<MoodSummary> generateDailySummary({
    required String characterId,
    required DateTime date,
  }) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final emotionalStates = await _storageService.getEmotionalStatesInRange(
      _defaultUserId,
      characterId,
      startOfDay,
      endOfDay,
    );

    if (emotionalStates.isEmpty) {
      return MoodSummary(
        id: _uuid.v4(),
        userId: _defaultUserId,
        characterId: characterId,
        date: date,
        averageEmotions: {},
        overallValence: 0.0,
        overallArousal: 0.0,
        dominantEmotion: EmotionType.contentment,
        totalInteractions: 0,
        emotionalHighlights: [],
      );
    }

    // 计算平均情感
    final averageEmotions = <EmotionType, double>{};
    for (final emotionType in EmotionType.values) {
      final scores = emotionalStates
          .map((state) => state.emotions[emotionType] ?? 0.0)
          .toList();
      averageEmotions[emotionType] = scores.isNotEmpty
          ? scores.reduce((a, b) => a + b) / scores.length
          : 0.0;
    }

    // 计算整体效价和唤醒度
    final overallValence = emotionalStates
        .map((state) => state.valence)
        .reduce((a, b) => a + b) / emotionalStates.length;
    
    final overallArousal = emotionalStates
        .map((state) => state.arousal)
        .reduce((a, b) => a + b) / emotionalStates.length;

    // 找到主导情感
    final dominantEmotion = averageEmotions.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    // 生成情感亮点
    final highlights = _generateEmotionalHighlights(emotionalStates);

    final summary = MoodSummary(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      date: date,
      averageEmotions: averageEmotions,
      overallValence: overallValence,
      overallArousal: overallArousal,
      dominantEmotion: dominantEmotion,
      totalInteractions: emotionalStates.length,
      emotionalHighlights: highlights,
    );

    await _storageService.saveMoodSummary(summary);
    return summary;
  }

  // 生成情感亮点
  List<String> _generateEmotionalHighlights(List<EmotionalState> states) {
    final highlights = <String>[];

    // 找到最高兴的时刻
    final joyfulStates = states.where((s) => (s.emotions[EmotionType.joy] ?? 0.0) > 0.7).toList();
    if (joyfulStates.isNotEmpty) {
      highlights.add('今天有${joyfulStates.length}个快乐时刻');
    }

    // 找到最平静的时刻
    final calmStates = states.where((s) => s.arousal < 0.3).toList();
    if (calmStates.isNotEmpty) {
      highlights.add('今天有${calmStates.length}个平静时刻');
    }

    // 检查情感强度
    final highIntensityStates = states.where((s) => s.intensity == EmotionIntensity.high || s.intensity == EmotionIntensity.extreme).toList();
    if (highIntensityStates.isNotEmpty) {
      highlights.add('今天情感比较强烈，有${highIntensityStates.length}个高强度情感时刻');
    }

    return highlights;
  }

  // 获取情感状态历史
  Future<List<EmotionalState>> getEmotionalHistory({
    required String characterId,
    int limit = 50,
  }) async {
    return await _storageService.getEmotionalStates(_defaultUserId, characterId, limit: limit);
  }

  // 获取情感触发器
  Future<List<EmotionalTrigger>> getEmotionalTriggers(String characterId) async {
    return await _storageService.getEmotionalTriggers(_defaultUserId, characterId);
  }

  // 获取最新的情绪摘要
  Future<MoodSummary?> getLatestMoodSummary(String characterId) async {
    return await _storageService.getLatestMoodSummary(_defaultUserId, characterId);
  }

  // 生成情感洞察
  Future<List<EmotionalInsight>> generateEmotionalInsights(String characterId) async {
    final insights = <EmotionalInsight>[];
    final recentStates = await getEmotionalHistory(characterId: characterId, limit: 20);
    
    if (recentStates.isEmpty) return insights;

    // 分析情感趋势
    final avgValence = recentStates.map((s) => s.valence).reduce((a, b) => a + b) / recentStates.length;
    
    if (avgValence < -0.3) {
      insights.add(EmotionalInsight(
        id: _uuid.v4(),
        userId: _defaultUserId,
        characterId: characterId,
        generatedAt: DateTime.now(),
        category: InsightCategory.wellbeing,
        title: '情绪偏向消极',
        description: '最近的对话中，你的情绪偏向消极。',
        recommendations: [
          '尝试分享一些积极的事情',
          '聊聊让你开心的话题',
          '考虑进行一些放松活动',
        ],
        confidence: 0.8,
        supportingDataIds: recentStates.map((s) => s.id).toList(),
        isActionable: true,
      ));
    }

    return insights;
  }
}

// Provider
final emotionServiceProvider = Provider<EmotionService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return EmotionService(storageService);
});
