import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/models.dart';
import 'storage_service.dart';

class ChatService {
  final StorageService _storageService;
  final Uuid _uuid = const Uuid();

  ChatService(this._storageService);

  // 发送消息
  Future<Message> sendMessage({
    required String conversationId,
    required String content,
    required String characterId,
    MessageType type = MessageType.text,
  }) async {
    final message = Message(
      id: _uuid.v4(),
      conversationId: conversationId,
      content: content,
      type: type,
      sender: MessageSender.user,
      timestamp: DateTime.now(),
      status: MessageStatus.sent,
      characterId: characterId,
    );

    await _storageService.saveMessage(message);
    await _updateConversation(conversationId, message);
    
    return message;
  }

  // 接收AI回复
  Future<Message> receiveMessage({
    required String conversationId,
    required String content,
    required String characterId,
    MessageType type = MessageType.text,
  }) async {
    final message = Message(
      id: _uuid.v4(),
      conversationId: conversationId,
      content: content,
      type: type,
      sender: MessageSender.character,
      timestamp: DateTime.now(),
      status: MessageStatus.delivered,
      characterId: characterId,
    );

    await _storageService.saveMessage(message);
    await _updateConversation(conversationId, message);
    
    return message;
  }

  // 获取对话消息
  Future<List<Message>> getConversationMessages(String conversationId) async {
    return await _storageService.getConversationMessages(conversationId);
  }

  // 创建新对话
  Future<Conversation> createConversation({
    required String characterId,
    String? title,
  }) async {
    final conversation = Conversation(
      id: _uuid.v4(),
      characterId: characterId,
      title: title ?? '新对话',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _storageService.saveConversation(conversation);
    return conversation;
  }

  // 获取角色的所有对话
  Future<List<Conversation>> getCharacterConversations(String characterId) async {
    return await _storageService.getCharacterConversations(characterId);
  }

  // 更新对话信息
  Future<void> _updateConversation(String conversationId, Message lastMessage) async {
    final conversation = await _storageService.getConversation(conversationId);
    if (conversation != null) {
      final updatedConversation = conversation.copyWith(
        updatedAt: DateTime.now(),
        lastMessageId: lastMessage.id,
        lastMessagePreview: lastMessage.content,
        messageCount: conversation.messageCount + 1,
        messageIds: [...conversation.messageIds, lastMessage.id],
      );
      await _storageService.saveConversation(updatedConversation);
    }
  }

  // 标记消息为已读
  Future<void> markMessageAsRead(String messageId) async {
    final message = await _storageService.getMessage(messageId);
    if (message != null && message.sender == MessageSender.character) {
      final updatedMessage = message.copyWith(status: MessageStatus.read);
      await _storageService.saveMessage(updatedMessage);
    }
  }

  // 删除对话
  Future<void> deleteConversation(String conversationId) async {
    await _storageService.deleteConversation(conversationId);
  }

  // 清空对话消息
  Future<void> clearConversationMessages(String conversationId) async {
    await _storageService.clearConversationMessages(conversationId);
  }
}

// Provider
final chatServiceProvider = Provider<ChatService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return ChatService(storageService);
});
