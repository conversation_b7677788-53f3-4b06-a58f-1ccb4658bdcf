import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';
import 'memory_service.dart';

class AdvancedMemoryRetrievalService {
  final StorageService _storageService;
  final MemoryService _memoryService;
  
  static const String _defaultUserId = 'default_user';

  AdvancedMemoryRetrievalService(this._storageService, this._memoryService);

  // 语义搜索记忆
  Future<List<Memory>> semanticSearch({
    required String characterId,
    required String query,
    int limit = 10,
    double threshold = 0.3,
  }) async {
    final allMemories = await _storageService.getCharacterMemories(characterId, _defaultUserId);
    
    // 计算语义相似度分数
    final scoredMemories = <MapEntry<Memory, double>>[];
    
    for (final memory in allMemories) {
      final score = _calculateSemanticSimilarity(query, memory);
      if (score >= threshold) {
        scoredMemories.add(MapEntry(memory, score));
      }
    }

    // 按相似度排序
    scoredMemories.sort((a, b) => b.value.compareTo(a.value));
    
    return scoredMemories.take(limit).map((e) => e.key).toList();
  }

  // 计算语义相似度
  double _calculateSemanticSimilarity(String query, Memory memory) {
    final queryWords = _extractKeywords(query.toLowerCase());
    final memoryText = '${memory.content} ${memory.summary} ${memory.tags.join(' ')}'.toLowerCase();
    final memoryWords = _extractKeywords(memoryText);
    
    // 直接匹配分数
    double directScore = 0.0;
    for (final word in queryWords) {
      if (memoryText.contains(word)) {
        directScore += 1.0;
      }
    }
    directScore = directScore / queryWords.length;

    // 关键词重叠分数
    final intersection = queryWords.toSet().intersection(memoryWords.toSet());
    final union = queryWords.toSet().union(memoryWords.toSet());
    final overlapScore = union.isNotEmpty ? intersection.length / union.length : 0.0;

    // 语义扩展分数
    final semanticScore = _calculateSemanticExpansion(queryWords, memoryWords);

    // 综合分数
    final finalScore = (directScore * 0.5) + (overlapScore * 0.3) + (semanticScore * 0.2);
    
    // 考虑记忆重要性和访问频率
    final importanceBonus = memory.importance / 10.0;
    final accessBonus = (memory.accessCount / 10.0).clamp(0.0, 0.2);
    
    return (finalScore + importanceBonus + accessBonus).clamp(0.0, 1.0);
  }

  // 提取关键词
  List<String> _extractKeywords(String text) {
    final stopWords = {
      '的', '了', '是', '我', '你', '他', '她', '它', '在', '有', '和', '与', '或', '但', '而',
      '就', '都', '也', '还', '又', '再', '很', '最', '更', '太', '非常', '这', '那', '些',
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
      'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did'
    };
    
    final words = text.split(RegExp(r'[\s，。！？；：""''（）【】《》\[\]{}().,!?;:"\'-]'))
        .where((word) => word.isNotEmpty && word.length > 1 && !stopWords.contains(word))
        .toList();
    
    return words;
  }

  // 计算语义扩展分数
  double _calculateSemanticExpansion(List<String> queryWords, List<String> memoryWords) {
    final synonyms = _getSynonyms();
    double score = 0.0;
    int matches = 0;

    for (final queryWord in queryWords) {
      final querySynonyms = synonyms[queryWord] ?? [];
      for (final memoryWord in memoryWords) {
        if (querySynonyms.contains(memoryWord)) {
          score += 0.8; // 同义词匹配权重
          matches++;
        }
      }
    }

    return matches > 0 ? score / matches : 0.0;
  }

  // 获取同义词映射
  Map<String, List<String>> _getSynonyms() {
    return {
      '开心': ['高兴', '快乐', '愉快', '兴奋', '喜悦'],
      '难过': ['伤心', '沮丧', '失落', '痛苦', '悲伤'],
      '工作': ['职业', '事业', '任务', '项目', '业务'],
      '学习': ['学习', '教育', '知识', '课程', '培训'],
      '朋友': ['好友', '伙伴', '同伴', '友人', '知己'],
      '家人': ['家庭', '亲人', '父母', '兄弟', '姐妹'],
      '喜欢': ['爱好', '偏爱', '钟爱', '热爱', '喜爱'],
      '讨厌': ['厌恶', '反感', '不喜欢', '憎恨', '排斥'],
    };
  }

  // 基于上下文的记忆检索
  Future<List<Memory>> contextualRetrieval({
    required String characterId,
    required String currentContext,
    required List<String> recentTopics,
    int limit = 5,
  }) async {
    final allMemories = await _storageService.getCharacterMemories(characterId, _defaultUserId);
    final scoredMemories = <MapEntry<Memory, double>>[];

    for (final memory in allMemories) {
      double score = 0.0;

      // 当前上下文相关性
      score += _calculateSemanticSimilarity(currentContext, memory) * 0.4;

      // 最近话题相关性
      for (final topic in recentTopics) {
        score += _calculateSemanticSimilarity(topic, memory) * 0.3;
      }

      // 时间衰减因子
      final daysSinceCreated = DateTime.now().difference(memory.timestamp).inDays;
      final timeDecay = 1.0 / (1.0 + daysSinceCreated * 0.1);
      score *= timeDecay;

      // 重要性加权
      score *= (1.0 + memory.importance / 10.0);

      if (score > 0.1) {
        scoredMemories.add(MapEntry(memory, score));
      }
    }

    scoredMemories.sort((a, b) => b.value.compareTo(a.value));
    return scoredMemories.take(limit).map((e) => e.key).toList();
  }

  // 情感驱动的记忆检索
  Future<List<Memory>> emotionDrivenRetrieval({
    required String characterId,
    required EmotionType currentEmotion,
    double emotionIntensity = 1.0,
    int limit = 5,
  }) async {
    final allMemories = await _storageService.getCharacterMemories(characterId, _defaultUserId);
    final emotionalStates = await _storageService.getEmotionalStates(_defaultUserId, characterId);
    
    final scoredMemories = <MapEntry<Memory, double>>[];

    for (final memory in allMemories) {
      double score = 0.0;

      // 查找与记忆相关的情感状态
      final relatedEmotions = emotionalStates.where((state) =>
          state.timestamp.isAfter(memory.timestamp.subtract(const Duration(minutes: 30))) &&
          state.timestamp.isBefore(memory.timestamp.add(const Duration(minutes: 30)))
      ).toList();

      if (relatedEmotions.isNotEmpty) {
        for (final emotionState in relatedEmotions) {
          final emotionValue = emotionState.emotions[currentEmotion] ?? 0.0;
          score += emotionValue * emotionIntensity;
        }
        score /= relatedEmotions.length;
      }

      // 记忆类型相关性
      if (memory.type == MemoryType.emotion) {
        score += 0.3;
      }

      // 重要性加权
      score *= (1.0 + memory.importance / 10.0);

      if (score > 0.1) {
        scoredMemories.add(MapEntry(memory, score));
      }
    }

    scoredMemories.sort((a, b) => b.value.compareTo(a.value));
    return scoredMemories.take(limit).map((e) => e.key).toList();
  }

  // 时间范围记忆检索
  Future<List<Memory>> temporalRetrieval({
    required String characterId,
    required DateTime startTime,
    required DateTime endTime,
    MemoryType? type,
    double? minImportance,
  }) async {
    final allMemories = await _storageService.getCharacterMemories(characterId, _defaultUserId);
    
    var filteredMemories = allMemories.where((memory) =>
        memory.timestamp.isAfter(startTime) && memory.timestamp.isBefore(endTime)
    );

    if (type != null) {
      filteredMemories = filteredMemories.where((memory) => memory.type == type);
    }

    if (minImportance != null) {
      filteredMemories = filteredMemories.where((memory) => memory.importance >= minImportance);
    }

    return filteredMemories.toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  // 关联记忆检索
  Future<List<Memory>> associativeRetrieval({
    required String characterId,
    required String memoryId,
    int limit = 5,
  }) async {
    final targetMemory = await _storageService.getMemory(memoryId);
    if (targetMemory == null) return [];

    final allMemories = await _storageService.getCharacterMemories(characterId, _defaultUserId);
    final scoredMemories = <MapEntry<Memory, double>>[];

    for (final memory in allMemories) {
      if (memory.id == memoryId) continue;

      double score = 0.0;

      // 内容相似性
      score += _calculateSemanticSimilarity(targetMemory.content, memory) * 0.4;

      // 标签重叠
      final commonTags = targetMemory.tags.toSet().intersection(memory.tags.toSet());
      if (targetMemory.tags.isNotEmpty && memory.tags.isNotEmpty) {
        score += (commonTags.length / targetMemory.tags.length) * 0.3;
      }

      // 类型相关性
      if (targetMemory.type == memory.type) {
        score += 0.2;
      }

      // 时间接近性
      final timeDiff = targetMemory.timestamp.difference(memory.timestamp).inDays.abs();
      final timeScore = 1.0 / (1.0 + timeDiff * 0.1);
      score += timeScore * 0.1;

      if (score > 0.1) {
        scoredMemories.add(MapEntry(memory, score));
      }
    }

    scoredMemories.sort((a, b) => b.value.compareTo(a.value));
    return scoredMemories.take(limit).map((e) => e.key).toList();
  }

  // 智能记忆推荐
  Future<List<Memory>> intelligentRecommendation({
    required String characterId,
    required String currentConversationContext,
    int limit = 3,
  }) async {
    // 获取最近的对话主题
    final recentMessages = await _getRecentMessages(characterId, 10);
    final recentTopics = _extractTopicsFromMessages(recentMessages);

    // 获取用户当前情感状态
    final currentEmotion = await _getCurrentEmotion(characterId);

    // 综合多种检索策略
    final semanticResults = await semanticSearch(
      characterId: characterId,
      query: currentConversationContext,
      limit: limit,
    );

    final contextualResults = await contextualRetrieval(
      characterId: characterId,
      currentContext: currentConversationContext,
      recentTopics: recentTopics,
      limit: limit,
    );

    List<Memory> emotionResults = [];
    if (currentEmotion != null) {
      emotionResults = await emotionDrivenRetrieval(
        characterId: characterId,
        currentEmotion: currentEmotion,
        limit: limit,
      );
    }

    // 合并和去重结果
    final allResults = <String, Memory>{};
    
    // 语义搜索结果权重最高
    for (int i = 0; i < semanticResults.length; i++) {
      allResults[semanticResults[i].id] = semanticResults[i];
    }

    // 上下文结果
    for (int i = 0; i < contextualResults.length; i++) {
      if (!allResults.containsKey(contextualResults[i].id)) {
        allResults[contextualResults[i].id] = contextualResults[i];
      }
    }

    // 情感结果
    for (int i = 0; i < emotionResults.length; i++) {
      if (!allResults.containsKey(emotionResults[i].id)) {
        allResults[emotionResults[i].id] = emotionResults[i];
      }
    }

    return allResults.values.take(limit).toList();
  }

  // 获取最近消息
  Future<List<Message>> _getRecentMessages(String characterId, int count) async {
    final conversations = await _storageService.getConversationsByCharacter(characterId);
    final allMessages = <Message>[];

    for (final conversation in conversations) {
      final messages = await _storageService.getMessages(conversation.id);
      allMessages.addAll(messages);
    }

    allMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return allMessages.take(count).toList();
  }

  // 从消息中提取主题
  List<String> _extractTopicsFromMessages(List<Message> messages) {
    final topics = <String>[];
    
    for (final message in messages) {
      if (message.sender == MessageSender.user) {
        final keywords = _extractKeywords(message.content);
        topics.addAll(keywords);
      }
    }

    // 统计词频并返回最常见的主题
    final topicCounts = <String, int>{};
    for (final topic in topics) {
      topicCounts[topic] = (topicCounts[topic] ?? 0) + 1;
    }

    final sortedTopics = topicCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedTopics.take(5).map((e) => e.key).toList();
  }

  // 获取当前情感状态
  Future<EmotionType?> _getCurrentEmotion(String characterId) async {
    final recentStates = await _storageService.getEmotionalStates(_defaultUserId, characterId);
    
    if (recentStates.isEmpty) return null;

    // 获取最近的情感状态
    recentStates.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    final latestState = recentStates.first;

    // 找到最强烈的情感
    EmotionType? dominantEmotion;
    double maxIntensity = 0.0;

    for (final entry in latestState.emotions.entries) {
      if (entry.value > maxIntensity) {
        maxIntensity = entry.value;
        dominantEmotion = entry.key;
      }
    }

    return dominantEmotion;
  }

  // 记忆重要性重新评估
  Future<void> reevaluateMemoryImportance(String characterId) async {
    final memories = await _storageService.getCharacterMemories(characterId, _defaultUserId);
    
    for (final memory in memories) {
      double newImportance = memory.importance;

      // 基于访问频率调整
      if (memory.accessCount > 5) {
        newImportance += 1.0;
      }

      // 基于时间衰减
      final daysSinceCreated = DateTime.now().difference(memory.timestamp).inDays;
      if (daysSinceCreated > 30) {
        newImportance *= 0.9;
      }

      // 基于关联性调整
      final associatedMemories = await associativeRetrieval(
        characterId: characterId,
        memoryId: memory.id,
        limit: 3,
      );
      if (associatedMemories.length > 2) {
        newImportance += 0.5;
      }

      // 更新记忆重要性
      if ((newImportance - memory.importance).abs() > 0.1) {
        final updatedMemory = memory.copyWith(importance: newImportance.clamp(0.0, 10.0));
        await _storageService.saveMemory(updatedMemory);
      }
    }
  }

  // 记忆检索统计
  Future<Map<String, dynamic>> getRetrievalStatistics(String characterId) async {
    final memories = await _storageService.getCharacterMemories(characterId, _defaultUserId);
    
    final totalMemories = memories.length;
    final totalAccesses = memories.fold<int>(0, (sum, memory) => sum + memory.accessCount);
    final averageImportance = memories.isNotEmpty 
        ? memories.map((m) => m.importance).reduce((a, b) => a + b) / memories.length
        : 0.0;

    final typeDistribution = <MemoryType, int>{};
    for (final memory in memories) {
      typeDistribution[memory.type] = (typeDistribution[memory.type] ?? 0) + 1;
    }

    final mostAccessedMemory = memories.isNotEmpty
        ? memories.reduce((a, b) => a.accessCount > b.accessCount ? a : b)
        : null;

    return {
      'totalMemories': totalMemories,
      'totalAccesses': totalAccesses,
      'averageImportance': averageImportance,
      'typeDistribution': typeDistribution.map((k, v) => MapEntry(k.name, v)),
      'mostAccessedMemory': mostAccessedMemory?.summary,
      'averageAccessCount': totalMemories > 0 ? totalAccesses / totalMemories : 0.0,
    };
  }
}

// Provider
final advancedMemoryRetrievalServiceProvider = Provider<AdvancedMemoryRetrievalService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  final memoryService = ref.watch(memoryServiceProvider);
  return AdvancedMemoryRetrievalService(storageService, memoryService);
});
