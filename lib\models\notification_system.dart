import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'notification_system.freezed.dart';
part 'notification_system.g.dart';

@freezed
@HiveType(typeId: 74)
class AppNotification with _$AppNotification {
  const factory AppNotification({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) String? characterId,
    @HiveField(3) required NotificationType type,
    @HiveField(4) required String title,
    @HiveField(5) required String body,
    @HiveField(6) String? imageUrl,
    @HiveField(7) required DateTime createdAt,
    @HiveField(8) DateTime? scheduledAt,
    @HiveField(9) @Default(false) bool isRead,
    @HiveField(10) @Default(false) bool isDelivered,
    @HiveField(11) @Default(NotificationPriority.normal) NotificationPriority priority,
    @HiveField(12) @Default({}) Map<String, dynamic> data,
    @HiveField(13) String? actionUrl,
    @HiveField(14) @Default([]) List<NotificationAction> actions,
    @HiveField(15) DateTime? expiresAt,
    @HiveField(16) @Default([]) List<String> tags,
  }) = _AppNotification;

  factory AppNotification.fromJson(Map<String, dynamic> json) => _$AppNotificationFromJson(json);
}

@HiveType(typeId: 75)
enum NotificationType {
  @HiveField(0)
  message,          // 新消息
  @HiveField(1)
  greeting,         // 问候
  @HiveField(2)
  reminder,         // 提醒
  @HiveField(3)
  milestone,        // 里程碑
  @HiveField(4)
  emotion,          // 情感变化
  @HiveField(5)
  memory,           // 记忆相关
  @HiveField(6)
  system,           // 系统通知
  @HiveField(7)
  update,           // 更新通知
  @HiveField(8)
  achievement,      // 成就解锁
  @HiveField(9)
  recommendation,   // 推荐
  @HiveField(10)
  conflict,         // 冲突警告
  @HiveField(11)
  schedule,         // 日程提醒
}

@HiveType(typeId: 76)
enum NotificationPriority {
  @HiveField(0)
  low,      // 低优先级
  @HiveField(1)
  normal,   // 普通
  @HiveField(2)
  high,     // 高优先级
  @HiveField(3)
  urgent,   // 紧急
}

@freezed
@HiveType(typeId: 77)
class NotificationAction with _$NotificationAction {
  const factory NotificationAction({
    @HiveField(0) required String id,
    @HiveField(1) required String title,
    @HiveField(2) required ActionType type,
    @HiveField(3) String? url,
    @HiveField(4) Map<String, dynamic>? data,
  }) = _NotificationAction;

  factory NotificationAction.fromJson(Map<String, dynamic> json) => _$NotificationActionFromJson(json);
}

@HiveType(typeId: 78)
enum ActionType {
  @HiveField(0)
  open,     // 打开应用
  @HiveField(1)
  reply,    // 快速回复
  @HiveField(2)
  dismiss,  // 忽略
  @HiveField(3)
  snooze,   // 稍后提醒
  @HiveField(4)
  navigate, // 导航到页面
}

@freezed
@HiveType(typeId: 79)
class NotificationSettings with _$NotificationSettings {
  const factory NotificationSettings({
    @HiveField(0) required String userId,
    @HiveField(1) @Default(true) bool enabled,
    @HiveField(2) @Default(true) bool soundEnabled,
    @HiveField(3) @Default(true) bool vibrationEnabled,
    @HiveField(4) @Default(true) bool showPreview,
    @HiveField(5) @Default({}) Map<NotificationType, bool> typeSettings,
    @HiveField(6) @Default([]) List<QuietHours> quietHours,
    @HiveField(7) @Default(NotificationPriority.normal) NotificationPriority minPriority,
    @HiveField(8) @Default(30) int maxNotificationsPerDay,
    @HiveField(9) @Default(true) bool groupSimilar,
    @HiveField(10) @Default(24) int autoDeleteAfterHours,
    @HiveField(11) DateTime? updatedAt,
  }) = _NotificationSettings;

  factory NotificationSettings.fromJson(Map<String, dynamic> json) => _$NotificationSettingsFromJson(json);
}

@freezed
@HiveType(typeId: 80)
class QuietHours with _$QuietHours {
  const factory QuietHours({
    @HiveField(0) required String id,
    @HiveField(1) required TimeOfDay startTime,
    @HiveField(2) required TimeOfDay endTime,
    @HiveField(3) @Default([]) List<int> weekdays,
    @HiveField(4) @Default(true) bool enabled,
    @HiveField(5) String? name,
  }) = _QuietHours;

  factory QuietHours.fromJson(Map<String, dynamic> json) => _$QuietHoursFromJson(json);
}

@freezed
@HiveType(typeId: 81)
class TimeOfDay with _$TimeOfDay {
  const factory TimeOfDay({
    @HiveField(0) required int hour,
    @HiveField(1) required int minute,
  }) = _TimeOfDay;

  factory TimeOfDay.fromJson(Map<String, dynamic> json) => _$TimeOfDayFromJson(json);
}

@freezed
@HiveType(typeId: 82)
class NotificationTemplate with _$NotificationTemplate {
  const factory NotificationTemplate({
    @HiveField(0) required String id,
    @HiveField(1) required NotificationType type,
    @HiveField(2) required String titleTemplate,
    @HiveField(3) required String bodyTemplate,
    @HiveField(4) @Default({}) Map<String, String> variables,
    @HiveField(5) @Default(NotificationPriority.normal) NotificationPriority priority,
    @HiveField(6) @Default([]) List<NotificationAction> defaultActions,
    @HiveField(7) @Default(true) bool enabled,
    @HiveField(8) String? imageUrl,
    @HiveField(9) @Default([]) List<String> tags,
  }) = _NotificationTemplate;

  factory NotificationTemplate.fromJson(Map<String, dynamic> json) => _$NotificationTemplateFromJson(json);
}

@freezed
@HiveType(typeId: 83)
class NotificationRule with _$NotificationRule {
  const factory NotificationRule({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String name,
    @HiveField(3) required RuleTrigger trigger,
    @HiveField(4) required List<RuleCondition> conditions,
    @HiveField(5) required RuleAction action,
    @HiveField(6) @Default(true) bool enabled,
    @HiveField(7) @Default(0) int priority,
    @HiveField(8) required DateTime createdAt,
    @HiveField(9) DateTime? lastTriggered,
    @HiveField(10) @Default(0) int triggerCount,
  }) = _NotificationRule;

  factory NotificationRule.fromJson(Map<String, dynamic> json) => _$NotificationRuleFromJson(json);
}

@freezed
@HiveType(typeId: 84)
class RuleTrigger with _$RuleTrigger {
  const factory RuleTrigger({
    @HiveField(0) required TriggerType type,
    @HiveField(1) @Default({}) Map<String, dynamic> parameters,
  }) = _RuleTrigger;

  factory RuleTrigger.fromJson(Map<String, dynamic> json) => _$RuleTriggerFromJson(json);
}

@HiveType(typeId: 85)
enum TriggerType {
  @HiveField(0)
  messageReceived,    // 收到消息
  @HiveField(1)
  emotionChanged,     // 情感变化
  @HiveField(2)
  intimacyLevelUp,    // 亲密度提升
  @HiveField(3)
  timeSchedule,       // 时间触发
  @HiveField(4)
  memoryCreated,      // 创建记忆
  @HiveField(5)
  longInactive,       // 长时间未活跃
  @HiveField(6)
  achievementUnlocked, // 成就解锁
  @HiveField(7)
  conflictDetected,   // 检测到冲突
}

@freezed
@HiveType(typeId: 86)
class RuleCondition with _$RuleCondition {
  const factory RuleCondition({
    @HiveField(0) required String field,
    @HiveField(1) required ConditionOperator operator,
    @HiveField(2) required dynamic value,
    @HiveField(3) @Default(LogicalOperator.and) LogicalOperator logicalOperator,
  }) = _RuleCondition;

  factory RuleCondition.fromJson(Map<String, dynamic> json) => _$RuleConditionFromJson(json);
}

@HiveType(typeId: 87)
enum ConditionOperator {
  @HiveField(0)
  equals,           // 等于
  @HiveField(1)
  notEquals,        // 不等于
  @HiveField(2)
  greaterThan,      // 大于
  @HiveField(3)
  lessThan,         // 小于
  @HiveField(4)
  contains,         // 包含
  @HiveField(5)
  notContains,      // 不包含
  @HiveField(6)
  startsWith,       // 开始于
  @HiveField(7)
  endsWith,         // 结束于
}

@HiveType(typeId: 88)
enum LogicalOperator {
  @HiveField(0)
  and,  // 与
  @HiveField(1)
  or,   // 或
}

@freezed
@HiveType(typeId: 89)
class RuleAction with _$RuleAction {
  const factory RuleAction({
    @HiveField(0) required RuleActionType type,
    @HiveField(1) @Default({}) Map<String, dynamic> parameters,
  }) = _RuleAction;

  factory RuleAction.fromJson(Map<String, dynamic> json) => _$RuleActionFromJson(json);
}

@HiveType(typeId: 90)
enum RuleActionType {
  @HiveField(0)
  sendNotification,   // 发送通知
  @HiveField(1)
  scheduleNotification, // 计划通知
  @HiveField(2)
  cancelNotification, // 取消通知
  @HiveField(3)
  updateSettings,     // 更新设置
  @HiveField(4)
  triggerAction,      // 触发动作
}

@freezed
@HiveType(typeId: 91)
class NotificationAnalytics with _$NotificationAnalytics {
  const factory NotificationAnalytics({
    @HiveField(0) required String userId,
    @HiveField(1) required DateTime date,
    @HiveField(2) @Default(0) int totalSent,
    @HiveField(3) @Default(0) int totalDelivered,
    @HiveField(4) @Default(0) int totalRead,
    @HiveField(5) @Default(0) int totalClicked,
    @HiveField(6) @Default({}) Map<NotificationType, int> typeBreakdown,
    @HiveField(7) @Default({}) Map<NotificationPriority, int> priorityBreakdown,
    @HiveField(8) @Default(0.0) double averageResponseTime,
    @HiveField(9) @Default([]) List<String> topActions,
    @HiveField(10) @Default(0) int dismissedCount,
  }) = _NotificationAnalytics;

  factory NotificationAnalytics.fromJson(Map<String, dynamic> json) => _$NotificationAnalyticsFromJson(json);
}

@freezed
@HiveType(typeId: 92)
class NotificationBatch with _$NotificationBatch {
  const factory NotificationBatch({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String title,
    @HiveField(3) required List<String> notificationIds,
    @HiveField(4) required DateTime createdAt,
    @HiveField(5) @Default(false) bool isCollapsed,
    @HiveField(6) @Default(0) int readCount,
    @HiveField(7) NotificationType? dominantType,
    @HiveField(8) String? summary,
  }) = _NotificationBatch;

  factory NotificationBatch.fromJson(Map<String, dynamic> json) => _$NotificationBatchFromJson(json);
}

@freezed
@HiveType(typeId: 93)
class SmartNotificationSuggestion with _$SmartNotificationSuggestion {
  const factory SmartNotificationSuggestion({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required SuggestionType type,
    @HiveField(3) required String title,
    @HiveField(4) required String description,
    @HiveField(5) required double confidence,
    @HiveField(6) required Map<String, dynamic> data,
    @HiveField(7) required DateTime generatedAt,
    @HiveField(8) @Default(false) bool isApplied,
    @HiveField(9) @Default(false) bool isDismissed,
    @HiveField(10) String? feedback,
  }) = _SmartNotificationSuggestion;

  factory SmartNotificationSuggestion.fromJson(Map<String, dynamic> json) => _$SmartNotificationSuggestionFromJson(json);
}

@HiveType(typeId: 94)
enum SuggestionType {
  @HiveField(0)
  quietHours,       // 免打扰时间建议
  @HiveField(1)
  typeDisable,      // 禁用某类型通知
  @HiveField(2)
  priorityAdjust,   // 优先级调整
  @HiveField(3)
  batchSimilar,     // 批量相似通知
  @HiveField(4)
  scheduleOptimize, // 优化发送时间
}
