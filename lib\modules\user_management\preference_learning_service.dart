import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';

class PreferenceLearningService {
  final StorageService _storageService;
  final Uuid _uuid = const Uuid();
  
  static const String _defaultUserId = 'default_user';

  PreferenceLearningService(this._storageService);

  // 分析消息并学习用户偏好
  Future<List<UserPreference>> analyzeMessageForPreferences({
    required String characterId,
    required Message message,
  }) async {
    final preferences = <UserPreference>[];
    final content = message.content.toLowerCase();

    // 分析喜好偏好
    final likes = _extractLikes(content, message);
    preferences.addAll(likes.map((like) => _createPreference(
      characterId: characterId,
      type: PreferenceType.likes,
      category: 'general',
      content: like,
      confidence: 0.7,
      evidenceMessageIds: [message.id],
    )));

    // 分析厌恶偏好
    final dislikes = _extractDislikes(content, message);
    preferences.addAll(dislikes.map((dislike) => _createPreference(
      characterId: characterId,
      type: PreferenceType.dislikes,
      category: 'general',
      content: dislike,
      confidence: 0.8,
      evidenceMessageIds: [message.id],
    )));

    // 分析兴趣爱好
    final interests = _extractInterests(content, message);
    preferences.addAll(interests.map((interest) => _createPreference(
      characterId: characterId,
      type: PreferenceType.interests,
      category: 'hobby',
      content: interest,
      confidence: 0.6,
      evidenceMessageIds: [message.id],
    )));

    // 分析沟通偏好
    final communicationPrefs = _extractCommunicationPreferences(content, message);
    preferences.addAll(communicationPrefs.map((pref) => _createPreference(
      characterId: characterId,
      type: PreferenceType.communication,
      category: 'style',
      content: pref,
      confidence: 0.5,
      evidenceMessageIds: [message.id],
    )));

    // 保存新发现的偏好
    for (final preference in preferences) {
      await _saveOrUpdatePreference(preference);
    }

    return preferences;
  }

  // 提取喜好信息
  List<String> _extractLikes(String content, Message message) {
    final likes = <String>[];
    final likePatterns = [
      r'我喜欢(.+?)(?:[，。！？\s]|$)',
      r'我爱(.+?)(?:[，。！？\s]|$)',
      r'我很喜欢(.+?)(?:[，。！？\s]|$)',
      r'我特别喜欢(.+?)(?:[，。！？\s]|$)',
      r'(.+?)很好',
      r'(.+?)不错',
      r'(.+?)很棒',
    ];

    for (final pattern in likePatterns) {
      final regex = RegExp(pattern);
      final matches = regex.allMatches(content);
      for (final match in matches) {
        final like = match.group(1)?.trim();
        if (like != null && like.isNotEmpty && like.length < 20) {
          likes.add(like);
        }
      }
    }

    return likes;
  }

  // 提取厌恶信息
  List<String> _extractDislikes(String content, Message message) {
    final dislikes = <String>[];
    final dislikePatterns = [
      r'我不喜欢(.+?)(?:[，。！？\s]|$)',
      r'我讨厌(.+?)(?:[，。！？\s]|$)',
      r'我不爱(.+?)(?:[，。！？\s]|$)',
      r'(.+?)很烦',
      r'(.+?)很讨厌',
      r'(.+?)不好',
    ];

    for (final pattern in dislikePatterns) {
      final regex = RegExp(pattern);
      final matches = regex.allMatches(content);
      for (final match in matches) {
        final dislike = match.group(1)?.trim();
        if (dislike != null && dislike.isNotEmpty && dislike.length < 20) {
          dislikes.add(dislike);
        }
      }
    }

    return dislikes;
  }

  // 提取兴趣信息
  List<String> _extractInterests(String content, Message message) {
    final interests = <String>[];
    
    // 兴趣关键词
    final interestKeywords = [
      '音乐', '电影', '读书', '运动', '游戏', '旅行', '摄影', '绘画',
      '编程', '写作', '烹饪', '园艺', '瑜伽', '健身', '舞蹈', '唱歌',
      '篮球', '足球', '网球', '游泳', '跑步', '爬山', '骑行',
      '小说', '漫画', '动漫', '电视剧', '综艺', '纪录片',
    ];

    for (final keyword in interestKeywords) {
      if (content.contains(keyword)) {
        interests.add(keyword);
      }
    }

    return interests;
  }

  // 提取沟通偏好
  List<String> _extractCommunicationPreferences(String content, Message message) {
    final prefs = <String>[];

    // 分析消息长度偏好
    if (message.content.length > 100) {
      prefs.add('详细交流');
    } else if (message.content.length < 20) {
      prefs.add('简洁交流');
    }

    // 分析情感表达偏好
    if (_containsEmotionalExpression(content)) {
      prefs.add('情感表达');
    }

    // 分析问题询问偏好
    if (_containsQuestions(content)) {
      prefs.add('互动询问');
    }

    return prefs;
  }

  // 检测情感表达
  bool _containsEmotionalExpression(String content) {
    final emotionalWords = [
      '开心', '高兴', '快乐', '兴奋', '激动', '满足', '幸福',
      '难过', '伤心', '沮丧', '失望', '郁闷', '痛苦',
      '生气', '愤怒', '烦躁', '焦虑', '担心', '害怕',
      '感动', '温暖', '感谢', '感激', '爱', '喜欢'
    ];
    
    return emotionalWords.any((word) => content.contains(word));
  }

  // 检测问题询问
  bool _containsQuestions(String content) {
    return content.contains('?') || content.contains('？') ||
           content.contains('怎么') || content.contains('什么') ||
           content.contains('为什么') || content.contains('如何');
  }

  // 创建偏好对象
  UserPreference _createPreference({
    required String characterId,
    required PreferenceType type,
    required String category,
    required String content,
    required double confidence,
    required List<String> evidenceMessageIds,
  }) {
    return UserPreference(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      type: type,
      category: category,
      content: content,
      confidence: confidence,
      discoveredAt: DateTime.now(),
      lastUpdated: DateTime.now(),
      evidenceMessageIds: evidenceMessageIds,
    );
  }

  // 保存或更新偏好
  Future<void> _saveOrUpdatePreference(UserPreference preference) async {
    // 检查是否已存在相似偏好
    final existingPreferences = await _storageService.getUserPreferences(
      _defaultUserId,
      preference.characterId,
    );

    final similar = existingPreferences.where((p) =>
        p.type == preference.type &&
        p.category == preference.category &&
        p.content.toLowerCase() == preference.content.toLowerCase()).firstOrNull;

    if (similar != null) {
      // 更新现有偏好
      final updated = similar.copyWith(
        confidence: (similar.confidence + preference.confidence) / 2,
        frequency: similar.frequency + 1,
        lastUpdated: DateTime.now(),
        evidenceMessageIds: [
          ...similar.evidenceMessageIds,
          ...preference.evidenceMessageIds,
        ],
      );
      await _storageService.saveUserPreference(updated);
    } else {
      // 保存新偏好
      await _storageService.saveUserPreference(preference);
    }
  }

  // 分析用户行为模式
  Future<UserBehaviorAnalysis> analyzeUserBehavior({
    required String characterId,
    required List<Message> recentMessages,
  }) async {
    final topicInterests = <String, double>{};
    final communicationPatterns = <String, double>{};
    final emotionalResponses = <String, double>{};
    final activityFrequency = <String, int>{};

    // 分析话题兴趣
    for (final message in recentMessages) {
      if (message.sender == MessageSender.user) {
        _analyzeTopicInterests(message, topicInterests);
        _analyzeCommunicationPatterns(message, communicationPatterns);
        _analyzeEmotionalResponses(message, emotionalResponses);
      }
    }

    // 计算参与度分数
    final engagementScore = _calculateEngagementScore(recentMessages);

    // 确定偏好和避免的话题
    final preferredTopics = topicInterests.entries
        .where((e) => e.value > 0.6)
        .map((e) => e.key)
        .toList();

    final avoidedTopics = topicInterests.entries
        .where((e) => e.value < 0.3)
        .map((e) => e.key)
        .toList();

    final analysis = UserBehaviorAnalysis(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      analysisDate: DateTime.now(),
      topicInterests: topicInterests,
      communicationPatterns: communicationPatterns,
      emotionalResponses: emotionalResponses,
      activityFrequency: activityFrequency,
      engagementScore: engagementScore,
      preferredTopics: preferredTopics,
      avoidedTopics: avoidedTopics,
    );

    await _storageService.saveUserBehaviorAnalysis(analysis);
    return analysis;
  }

  // 分析话题兴趣
  void _analyzeTopicInterests(Message message, Map<String, double> interests) {
    final content = message.content.toLowerCase();
    final topics = {
      '工作': ['工作', '职业', '公司', '同事', '老板', '项目'],
      '学习': ['学习', '考试', '课程', '学校', '老师', '作业'],
      '娱乐': ['电影', '音乐', '游戏', '电视', '综艺', '娱乐'],
      '运动': ['运动', '健身', '跑步', '游泳', '篮球', '足球'],
      '旅行': ['旅行', '旅游', '出差', '度假', '景点', '酒店'],
      '美食': ['吃', '美食', '餐厅', '烹饪', '菜', '饭'],
      '情感': ['爱情', '友情', '家人', '朋友', '恋人', '感情'],
      '健康': ['健康', '医生', '医院', '药', '身体', '锻炼'],
    };

    for (final entry in topics.entries) {
      final topic = entry.key;
      final keywords = entry.value;
      
      final matches = keywords.where((keyword) => content.contains(keyword)).length;
      if (matches > 0) {
        interests[topic] = (interests[topic] ?? 0.0) + (matches / keywords.length);
      }
    }
  }

  // 分析沟通模式
  void _analyzeCommunicationPatterns(Message message, Map<String, double> patterns) {
    final content = message.content;
    
    // 消息长度模式
    if (content.length > 100) {
      patterns['详细表达'] = (patterns['详细表达'] ?? 0.0) + 1.0;
    } else if (content.length < 20) {
      patterns['简洁表达'] = (patterns['简洁表达'] ?? 0.0) + 1.0;
    }

    // 问题询问模式
    if (_containsQuestions(content)) {
      patterns['主动询问'] = (patterns['主动询问'] ?? 0.0) + 1.0;
    }

    // 情感表达模式
    if (_containsEmotionalExpression(content)) {
      patterns['情感表达'] = (patterns['情感表达'] ?? 0.0) + 1.0;
    }
  }

  // 分析情感反应
  void _analyzeEmotionalResponses(Message message, Map<String, double> responses) {
    final content = message.content.toLowerCase();
    
    final emotions = {
      '积极': ['开心', '高兴', '快乐', '兴奋', '满足', '幸福', '感谢'],
      '消极': ['难过', '伤心', '沮丧', '失望', '生气', '愤怒', '担心'],
      '中性': ['好的', '知道了', '明白', '了解', '可以'],
    };

    for (final entry in emotions.entries) {
      final emotion = entry.key;
      final keywords = entry.value;
      
      final matches = keywords.where((keyword) => content.contains(keyword)).length;
      if (matches > 0) {
        responses[emotion] = (responses[emotion] ?? 0.0) + matches.toDouble();
      }
    }
  }

  // 计算参与度分数
  double _calculateEngagementScore(List<Message> messages) {
    if (messages.isEmpty) return 0.0;

    final userMessages = messages.where((m) => m.sender == MessageSender.user).toList();
    if (userMessages.isEmpty) return 0.0;

    double score = 0.0;
    
    // 消息频率分数
    score += (userMessages.length / messages.length) * 40;
    
    // 平均消息长度分数
    final avgLength = userMessages.map((m) => m.content.length).reduce((a, b) => a + b) / userMessages.length;
    score += (avgLength / 50).clamp(0, 30);
    
    // 情感表达分数
    final emotionalMessages = userMessages.where((m) => _containsEmotionalExpression(m.content)).length;
    score += (emotionalMessages / userMessages.length) * 20;
    
    // 互动性分数
    final questionMessages = userMessages.where((m) => _containsQuestions(m.content)).length;
    score += (questionMessages / userMessages.length) * 10;

    return score.clamp(0, 100);
  }

  // 获取用户偏好
  Future<List<UserPreference>> getUserPreferences(String characterId) async {
    return await _storageService.getUserPreferences(_defaultUserId, characterId);
  }

  // 获取用户行为分析
  Future<UserBehaviorAnalysis?> getLatestBehaviorAnalysis(String characterId) async {
    return await _storageService.getLatestUserBehaviorAnalysis(_defaultUserId, characterId);
  }

  // 生成个性化建议
  Future<List<String>> generatePersonalizationSuggestions(String characterId) async {
    final preferences = await getUserPreferences(characterId);
    final analysis = await getLatestBehaviorAnalysis(characterId);
    
    final suggestions = <String>[];

    // 基于偏好的建议
    final likes = preferences.where((p) => p.type == PreferenceType.likes).toList();
    if (likes.isNotEmpty) {
      suggestions.add('根据你的喜好，我可以多聊聊${likes.first.content}相关的话题');
    }

    // 基于行为分析的建议
    if (analysis != null) {
      if (analysis.engagementScore < 50) {
        suggestions.add('我注意到你可能不太活跃，要不要换个话题？');
      }
      
      if (analysis.preferredTopics.isNotEmpty) {
        suggestions.add('你似乎对${analysis.preferredTopics.first}很感兴趣，我们可以深入聊聊');
      }
    }

    return suggestions;
  }
}

// Provider
final preferenceLearningServiceProvider = Provider<PreferenceLearningService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return PreferenceLearningService(storageService);
});
