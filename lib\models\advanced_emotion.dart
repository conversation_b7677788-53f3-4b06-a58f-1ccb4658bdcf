import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'advanced_emotion.freezed.dart';
part 'advanced_emotion.g.dart';

@freezed
@HiveType(typeId: 168)
class EmotionVector with _$EmotionVector {
  const factory EmotionVector({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required String userId,
    @HiveField(3) required Map<EmotionDimension, double> dimensions,
    @HiveField(4) required DateTime timestamp,
    @HiveField(5) @Default(1.0) double intensity,
    @HiveField(6) @Default(1.0) double confidence,
    @HiveField(7) @Default({}) Map<String, dynamic> context,
    @HiveField(8) @Default([]) List<String> triggers,
    @HiveField(9) @Default(0.0) double stability,
    @HiveField(10) @Default(EmotionSource.conversation) EmotionSource source,
    @HiveField(11) String? parentEmotionId,
    @HiveField(12) @Default([]) List<String> childEmotionIds,
  }) = _EmotionVector;

  factory EmotionVector.fromJson(Map<String, dynamic> json) => _$EmotionVectorFromJson(json);
}

@HiveType(typeId: 169)
enum EmotionDimension {
  @HiveField(0)
  valence,          // 效价 (正面-负面)
  @HiveField(1)
  arousal,          // 唤醒度 (激活-平静)
  @HiveField(2)
  dominance,        // 支配性 (控制-被控制)
  @HiveField(3)
  approach,         // 趋近性 (接近-回避)
  @HiveField(4)
  certainty,        // 确定性 (确定-不确定)
  @HiveField(5)
  attention,        // 注意力 (专注-分散)
  @HiveField(6)
  responsibility,   // 责任感 (负责-推卸)
  @HiveField(7)
  effort,           // 努力程度 (努力-懈怠)
  @HiveField(8)
  pleasantness,     // 愉悦度 (愉悦-不愉悦)
  @HiveField(9)
  authenticity,     // 真实性 (真实-虚假)
}

@HiveType(typeId: 170)
enum EmotionSource {
  @HiveField(0)
  conversation,     // 对话产生
  @HiveField(1)
  memory,           // 记忆触发
  @HiveField(2)
  interaction,      // 互动产生
  @HiveField(3)
  environment,      // 环境影响
  @HiveField(4)
  internal,         // 内在产生
  @HiveField(5)
  external,         // 外部刺激
  @HiveField(6)
  synthetic,        // 人工合成
  @HiveField(7)
  learned,          // 学习获得
}

@freezed
@HiveType(typeId: 171)
class EmotionPattern with _$EmotionPattern {
  const factory EmotionPattern({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required String description,
    @HiveField(3) required PatternType type,
    @HiveField(4) required List<EmotionSequence> sequences,
    @HiveField(5) @Default([]) List<PatternTrigger> triggers,
    @HiveField(6) @Default([]) List<PatternCondition> conditions,
    @HiveField(7) @Default(1.0) double probability,
    @HiveField(8) @Default(0.0) double minDuration,
    @HiveField(9) @Default(double.infinity) double maxDuration,
    @HiveField(10) @Default({}) Map<String, dynamic> parameters,
    @HiveField(11) required DateTime createdAt,
    @HiveField(12) @Default(0) int usageCount,
    @HiveField(13) @Default(0.0) double successRate,
    @HiveField(14) @Default(true) bool isActive,
  }) = _EmotionPattern;

  factory EmotionPattern.fromJson(Map<String, dynamic> json) => _$EmotionPatternFromJson(json);
}

@HiveType(typeId: 172)
enum PatternType {
  @HiveField(0)
  cyclic,           // 循环模式
  @HiveField(1)
  progressive,      // 渐进模式
  @HiveField(2)
  reactive,         // 反应模式
  @HiveField(3)
  adaptive,         // 适应模式
  @HiveField(4)
  oscillating,      // 振荡模式
  @HiveField(5)
  decay,            // 衰减模式
  @HiveField(6)
  amplification,    // 放大模式
  @HiveField(7)
  stabilization,    // 稳定模式
}

@freezed
@HiveType(typeId: 173)
class EmotionSequence with _$EmotionSequence {
  const factory EmotionSequence({
    @HiveField(0) required int order,
    @HiveField(1) required Map<EmotionDimension, double> targetState,
    @HiveField(2) @Default(1000) int duration,
    @HiveField(3) @Default(TransitionType.linear) TransitionType transition,
    @HiveField(4) @Default([]) List<String> conditions,
    @HiveField(5) @Default(1.0) double probability,
    @HiveField(6) @Default({}) Map<String, dynamic> metadata,
  }) = _EmotionSequence;

  factory EmotionSequence.fromJson(Map<String, dynamic> json) => _$EmotionSequenceFromJson(json);
}

@HiveType(typeId: 174)
enum TransitionType {
  @HiveField(0)
  linear,           // 线性过渡
  @HiveField(1)
  exponential,      // 指数过渡
  @HiveField(2)
  logarithmic,      // 对数过渡
  @HiveField(3)
  sigmoid,          // S型过渡
  @HiveField(4)
  bounce,           // 弹跳过渡
  @HiveField(5)
  elastic,          // 弹性过渡
  @HiveField(6)
  cubic,            // 三次过渡
  @HiveField(7)
  instant,          // 瞬间过渡
}

@freezed
@HiveType(typeId: 175)
class PatternTrigger with _$PatternTrigger {
  const factory PatternTrigger({
    @HiveField(0) required String id,
    @HiveField(1) required TriggerType type,
    @HiveField(2) required Map<String, dynamic> conditions,
    @HiveField(3) @Default(1.0) double sensitivity,
    @HiveField(4) @Default(0) int delay,
    @HiveField(5) String? description,
  }) = _PatternTrigger;

  factory PatternTrigger.fromJson(Map<String, dynamic> json) => _$PatternTriggerFromJson(json);
}

@HiveType(typeId: 176)
enum TriggerType {
  @HiveField(0)
  threshold,        // 阈值触发
  @HiveField(1)
  change,           // 变化触发
  @HiveField(2)
  pattern,          // 模式触发
  @HiveField(3)
  time,             // 时间触发
  @HiveField(4)
  event,            // 事件触发
  @HiveField(5)
  context,          // 上下文触发
}

@freezed
@HiveType(typeId: 177)
class PatternCondition with _$PatternCondition {
  const factory PatternCondition({
    @HiveField(0) required String id,
    @HiveField(1) required ConditionType type,
    @HiveField(2) required String field,
    @HiveField(3) required ConditionOperator operator,
    @HiveField(4) required dynamic value,
    @HiveField(5) @Default(1.0) double weight,
    @HiveField(6) String? description,
  }) = _PatternCondition;

  factory PatternCondition.fromJson(Map<String, dynamic> json) => _$PatternConditionFromJson(json);
}

@HiveType(typeId: 178)
enum ConditionType {
  @HiveField(0)
  emotional,        // 情感条件
  @HiveField(1)
  temporal,         // 时间条件
  @HiveField(2)
  contextual,       // 上下文条件
  @HiveField(3)
  behavioral,       // 行为条件
  @HiveField(4)
  environmental,    // 环境条件
  @HiveField(5)
  relational,       // 关系条件
}

@HiveType(typeId: 179)
enum ConditionOperator {
  @HiveField(0)
  equals,           // 等于
  @HiveField(1)
  notEquals,        // 不等于
  @HiveField(2)
  greaterThan,      // 大于
  @HiveField(3)
  lessThan,         // 小于
  @HiveField(4)
  greaterOrEqual,   // 大于等于
  @HiveField(5)
  lessOrEqual,      // 小于等于
  @HiveField(6)
  contains,         // 包含
  @HiveField(7)
  notContains,      // 不包含
  @HiveField(8)
  inRange,          // 在范围内
  @HiveField(9)
  outOfRange,       // 超出范围
}

@freezed
@HiveType(typeId: 180)
class EmotionRegulation with _$EmotionRegulation {
  const factory EmotionRegulation({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required RegulationType type,
    @HiveField(3) required RegulationStrategy strategy,
    @HiveField(4) required Map<EmotionDimension, double> targetState,
    @HiveField(5) @Default(1.0) double intensity,
    @HiveField(6) @Default(5000) int duration,
    @HiveField(7) @Default([]) List<String> triggers,
    @HiveField(8) @Default({}) Map<String, dynamic> parameters,
    @HiveField(9) required DateTime createdAt,
    @HiveField(10) DateTime? activatedAt,
    @HiveField(11) DateTime? completedAt,
    @HiveField(12) @Default(RegulationStatus.pending) RegulationStatus status,
    @HiveField(13) @Default(0.0) double effectiveness,
  }) = _EmotionRegulation;

  factory EmotionRegulation.fromJson(Map<String, dynamic> json) => _$EmotionRegulationFromJson(json);
}

@HiveType(typeId: 181)
enum RegulationType {
  @HiveField(0)
  suppression,      // 抑制
  @HiveField(1)
  enhancement,      // 增强
  @HiveField(2)
  redirection,      // 重定向
  @HiveField(3)
  stabilization,    // 稳定
  @HiveField(4)
  modulation,       // 调节
  @HiveField(5)
  transformation,   // 转换
}

@HiveType(typeId: 182)
enum RegulationStrategy {
  @HiveField(0)
  cognitive,        // 认知策略
  @HiveField(1)
  behavioral,       // 行为策略
  @HiveField(2)
  physiological,    // 生理策略
  @HiveField(3)
  social,           // 社交策略
  @HiveField(4)
  environmental,    // 环境策略
  @HiveField(5)
  temporal,         // 时间策略
}

@HiveType(typeId: 183)
enum RegulationStatus {
  @HiveField(0)
  pending,          // 等待中
  @HiveField(1)
  active,           // 激活中
  @HiveField(2)
  completed,        // 已完成
  @HiveField(3)
  failed,           // 失败
  @HiveField(4)
  cancelled,        // 已取消
}

@freezed
@HiveType(typeId: 184)
class EmotionAnalytics with _$EmotionAnalytics {
  const factory EmotionAnalytics({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required String userId,
    @HiveField(3) required DateTime date,
    @HiveField(4) @Default({}) Map<EmotionDimension, EmotionStatistics> dimensionStats,
    @HiveField(5) @Default([]) List<EmotionTrend> trends,
    @HiveField(6) @Default([]) List<EmotionCorrelation> correlations,
    @HiveField(7) @Default(0.0) double overallStability,
    @HiveField(8) @Default(0.0) double emotionalComplexity,
    @HiveField(9) @Default(0.0) double adaptabilityScore,
    @HiveField(10) @Default({}) Map<String, dynamic> insights,
    @HiveField(11) @Default([]) List<String> recommendations,
  }) = _EmotionAnalytics;

  factory EmotionAnalytics.fromJson(Map<String, dynamic> json) => _$EmotionAnalyticsFromJson(json);
}

@freezed
@HiveType(typeId: 185)
class EmotionStatistics with _$EmotionStatistics {
  const factory EmotionStatistics({
    @HiveField(0) @Default(0.0) double mean,
    @HiveField(1) @Default(0.0) double median,
    @HiveField(2) @Default(0.0) double standardDeviation,
    @HiveField(3) @Default(0.0) double variance,
    @HiveField(4) @Default(0.0) double minimum,
    @HiveField(5) @Default(0.0) double maximum,
    @HiveField(6) @Default(0.0) double range,
    @HiveField(7) @Default(0.0) double skewness,
    @HiveField(8) @Default(0.0) double kurtosis,
    @HiveField(9) @Default(0) int sampleCount,
  }) = _EmotionStatistics;

  factory EmotionStatistics.fromJson(Map<String, dynamic> json) => _$EmotionStatisticsFromJson(json);
}

@freezed
@HiveType(typeId: 186)
class EmotionTrend with _$EmotionTrend {
  const factory EmotionTrend({
    @HiveField(0) required EmotionDimension dimension,
    @HiveField(1) required TrendDirection direction,
    @HiveField(2) @Default(0.0) double slope,
    @HiveField(3) @Default(0.0) double correlation,
    @HiveField(4) @Default(0.0) double significance,
    @HiveField(5) @Default(TrendStrength.weak) TrendStrength strength,
    @HiveField(6) @Default(0) int duration,
    @HiveField(7) String? description,
  }) = _EmotionTrend;

  factory EmotionTrend.fromJson(Map<String, dynamic> json) => _$EmotionTrendFromJson(json);
}

@HiveType(typeId: 187)
enum TrendDirection {
  @HiveField(0)
  increasing,       // 上升
  @HiveField(1)
  decreasing,       // 下降
  @HiveField(2)
  stable,           // 稳定
  @HiveField(3)
  oscillating,      // 振荡
  @HiveField(4)
  irregular,        // 不规律
}

@HiveType(typeId: 188)
enum TrendStrength {
  @HiveField(0)
  weak,             // 弱
  @HiveField(1)
  moderate,         // 中等
  @HiveField(2)
  strong,           // 强
  @HiveField(3)
  veryStrong,       // 很强
}

@freezed
@HiveType(typeId: 189)
class EmotionCorrelation with _$EmotionCorrelation {
  const factory EmotionCorrelation({
    @HiveField(0) required EmotionDimension dimension1,
    @HiveField(1) required EmotionDimension dimension2,
    @HiveField(2) @Default(0.0) double coefficient,
    @HiveField(3) @Default(0.0) double significance,
    @HiveField(4) @Default(CorrelationType.linear) CorrelationType type,
    @HiveField(5) @Default(CorrelationStrength.none) CorrelationStrength strength,
    @HiveField(6) String? description,
  }) = _EmotionCorrelation;

  factory EmotionCorrelation.fromJson(Map<String, dynamic> json) => _$EmotionCorrelationFromJson(json);
}

@HiveType(typeId: 190)
enum CorrelationType {
  @HiveField(0)
  linear,           // 线性相关
  @HiveField(1)
  nonlinear,        // 非线性相关
  @HiveField(2)
  inverse,          // 反向相关
  @HiveField(3)
  complex,          // 复杂相关
}

@HiveType(typeId: 191)
enum CorrelationStrength {
  @HiveField(0)
  none,             // 无相关
  @HiveField(1)
  weak,             // 弱相关
  @HiveField(2)
  moderate,         // 中等相关
  @HiveField(3)
  strong,           // 强相关
  @HiveField(4)
  veryStrong,       // 很强相关
}

@freezed
@HiveType(typeId: 192)
class EmotionPrediction with _$EmotionPrediction {
  const factory EmotionPrediction({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required DateTime predictedTime,
    @HiveField(3) required Map<EmotionDimension, double> predictedState,
    @HiveField(4) @Default(0.0) double confidence,
    @HiveField(5) @Default(PredictionMethod.statistical) PredictionMethod method,
    @HiveField(6) @Default({}) Map<String, dynamic> factors,
    @HiveField(7) required DateTime createdAt,
    @HiveField(8) DateTime? validatedAt,
    @HiveField(9) @Default(0.0) double accuracy,
    @HiveField(10) @Default({}) Map<String, dynamic> metadata,
  }) = _EmotionPrediction;

  factory EmotionPrediction.fromJson(Map<String, dynamic> json) => _$EmotionPredictionFromJson(json);
}

@HiveType(typeId: 193)
enum PredictionMethod {
  @HiveField(0)
  statistical,      // 统计方法
  @HiveField(1)
  machineLearning,  // 机器学习
  @HiveField(2)
  neuralNetwork,    // 神经网络
  @HiveField(3)
  patternMatching,  // 模式匹配
  @HiveField(4)
  hybrid,           // 混合方法
  @HiveField(5)
  heuristic,        // 启发式
}
