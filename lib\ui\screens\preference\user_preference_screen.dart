import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../models/models.dart';
import '../../../modules/user_management/preference_learning_service.dart';

class UserPreferenceScreen extends ConsumerStatefulWidget {
  final String characterId;

  const UserPreferenceScreen({
    super.key,
    required this.characterId,
  });

  @override
  ConsumerState<UserPreferenceScreen> createState() => _UserPreferenceScreenState();
}

class _UserPreferenceScreenState extends ConsumerState<UserPreferenceScreen> {
  List<UserPreference> _preferences = [];
  UserBehaviorAnalysis? _behaviorAnalysis;
  PreferenceType? _selectedType;
  bool _isLoading = true;
  Character? _character;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final preferenceLearningService = ref.read(preferenceLearningServiceProvider);
    final storageService = ref.read(storageServiceProvider);
    
    final character = await storageService.getCharacter(widget.characterId);
    final preferences = await preferenceLearningService.getUserPreferences(widget.characterId);
    final analysis = await preferenceLearningService.getLatestBehaviorAnalysis(widget.characterId);

    if (mounted) {
      setState(() {
        _character = character;
        _preferences = preferences;
        _behaviorAnalysis = analysis;
        _isLoading = false;
      });
    }
  }

  List<UserPreference> get _filteredPreferences {
    if (_selectedType == null) return _preferences;
    return _preferences.where((pref) => pref.type == _selectedType).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _character == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${_character!.name} 了解的你'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: Column(
        children: [
          // 偏好类型筛选
          _buildTypeFilter(),
          
          // 行为分析概览
          if (_behaviorAnalysis != null) _buildBehaviorAnalysisCard(),
          
          // 偏好列表
          Expanded(
            child: _filteredPreferences.isEmpty
                ? _buildEmptyState()
                : _buildPreferenceList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            FilterChip(
              label: const Text('全部'),
              selected: _selectedType == null,
              onSelected: (selected) {
                setState(() {
                  _selectedType = null;
                });
              },
            ),
            const SizedBox(width: 8),
            ...PreferenceType.values.map((type) {
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(_getPreferenceTypeLabel(type)),
                  selected: _selectedType == type,
                  onSelected: (selected) {
                    setState(() {
                      _selectedType = selected ? type : null;
                    });
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildBehaviorAnalysisCard() {
    final analysis = _behaviorAnalysis!;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.analytics,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '行为分析',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    DateFormat('MM/dd').format(analysis.analysisDate),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              // 参与度分数
              Row(
                children: [
                  const Text('参与度: '),
                  Expanded(
                    child: LinearProgressIndicator(
                      value: analysis.engagementScore / 100,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        _getEngagementColor(analysis.engagementScore),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text('${analysis.engagementScore.toInt()}%'),
                ],
              ),
              const SizedBox(height: 8),
              
              // 偏好话题
              if (analysis.preferredTopics.isNotEmpty) ...[
                const Text('偏好话题: '),
                const SizedBox(height: 4),
                Wrap(
                  spacing: 4,
                  children: analysis.preferredTopics.take(3).map((topic) {
                    return Chip(
                      label: Text(topic),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.psychology_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '还没有了解你的偏好',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '多和${_character!.name}聊天，让TA更了解你！',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreferenceList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredPreferences.length,
      itemBuilder: (context, index) {
        final preference = _filteredPreferences[index];
        return _buildPreferenceCard(preference);
      },
    );
  }

  Widget _buildPreferenceCard(UserPreference preference) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 偏好类型和置信度
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getPreferenceTypeColor(preference.type).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getPreferenceTypeLabel(preference.type),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getPreferenceTypeColor(preference.type),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      size: 16,
                      color: Colors.amber[600],
                    ),
                    const SizedBox(width: 2),
                    Text(
                      '${(preference.confidence * 100).toInt()}%',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.amber[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // 偏好内容
            Text(
              preference.content,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            
            // 发现时间和频率
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '发现于 ${DateFormat('MM/dd').format(preference.discoveredAt)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                if (preference.frequency > 1) ...[
                  Icon(
                    Icons.repeat,
                    size: 14,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${preference.frequency}次',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getPreferenceTypeLabel(PreferenceType type) {
    switch (type) {
      case PreferenceType.likes:
        return '喜欢';
      case PreferenceType.dislikes:
        return '不喜欢';
      case PreferenceType.interests:
        return '兴趣';
      case PreferenceType.habits:
        return '习惯';
      case PreferenceType.values:
        return '价值观';
      case PreferenceType.communication:
        return '沟通';
      case PreferenceType.topics:
        return '话题';
      case PreferenceType.activities:
        return '活动';
    }
  }

  Color _getPreferenceTypeColor(PreferenceType type) {
    switch (type) {
      case PreferenceType.likes:
        return Colors.green;
      case PreferenceType.dislikes:
        return Colors.red;
      case PreferenceType.interests:
        return Colors.blue;
      case PreferenceType.habits:
        return Colors.orange;
      case PreferenceType.values:
        return Colors.purple;
      case PreferenceType.communication:
        return Colors.teal;
      case PreferenceType.topics:
        return Colors.indigo;
      case PreferenceType.activities:
        return Colors.pink;
    }
  }

  Color _getEngagementColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    if (score >= 40) return Colors.yellow;
    return Colors.red;
  }
}
