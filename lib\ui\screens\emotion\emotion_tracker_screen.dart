import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../models/models.dart';
import '../../../modules/emotion_tracking/emotion_service.dart';

class EmotionTrackerScreen extends ConsumerStatefulWidget {
  final String characterId;

  const EmotionTrackerScreen({
    super.key,
    required this.characterId,
  });

  @override
  ConsumerState<EmotionTrackerScreen> createState() => _EmotionTrackerScreenState();
}

class _EmotionTrackerScreenState extends ConsumerState<EmotionTrackerScreen> {
  List<EmotionalState> _emotionalStates = [];
  List<EmotionalTrigger> _triggers = [];
  MoodSummary? _latestSummary;
  bool _isLoading = true;
  Character? _character;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final emotionService = ref.read(emotionServiceProvider);
    final storageService = ref.read(storageServiceProvider);
    
    final character = await storageService.getCharacter(widget.characterId);
    final states = await emotionService.getEmotionalHistory(characterId: widget.characterId);
    final triggers = await emotionService.getEmotionalTriggers(widget.characterId);
    final summary = await emotionService.getLatestMoodSummary(widget.characterId);

    if (mounted) {
      setState(() {
        _character = character;
        _emotionalStates = states;
        _triggers = triggers;
        _latestSummary = summary;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _character == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${_character!.name} 的情感追踪'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 最新情绪摘要
            if (_latestSummary != null) _buildMoodSummaryCard(),
            
            const SizedBox(height: 16),
            
            // 情感触发器
            _buildTriggersSection(),
            
            const SizedBox(height: 16),
            
            // 情感历史
            _buildEmotionalHistorySection(),
          ],
        ),
      ),
    );
  }

  Widget _buildMoodSummaryCard() {
    final summary = _latestSummary!;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.mood,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  '情绪摘要',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const Spacer(),
                Text(
                  DateFormat('MM/dd').format(summary.date),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 主导情感
            Row(
              children: [
                const Text('主导情感: '),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getEmotionColor(summary.dominantEmotion).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getEmotionLabel(summary.dominantEmotion),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getEmotionColor(summary.dominantEmotion),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // 情感效价
            Row(
              children: [
                const Text('情感倾向: '),
                Expanded(
                  child: LinearProgressIndicator(
                    value: (summary.overallValence + 1) / 2, // 转换到0-1范围
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      summary.overallValence >= 0 ? Colors.green : Colors.red,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  summary.overallValence >= 0 ? '积极' : '消极',
                  style: TextStyle(
                    fontSize: 12,
                    color: summary.overallValence >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // 互动次数
            Text('互动次数: ${summary.totalInteractions}'),
            
            // 情感亮点
            if (summary.emotionalHighlights.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text(
                '情感亮点:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 4),
              ...summary.emotionalHighlights.map((highlight) => Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 2),
                child: Row(
                  children: [
                    const Icon(Icons.circle, size: 6, color: Colors.grey),
                    const SizedBox(width: 8),
                    Expanded(child: Text(highlight, style: const TextStyle(fontSize: 12))),
                  ],
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTriggersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '情感触发器',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        
        if (_triggers.isEmpty)
          const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Center(
                child: Text(
                  '还没有发现情感触发器',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          )
        else
          ...(_triggers.take(5).map((trigger) => _buildTriggerCard(trigger))),
      ],
    );
  }

  Widget _buildTriggerCard(EmotionalTrigger trigger) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: _getEmotionColor(trigger.resultingEmotion),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    trigger.trigger,
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    '触发 ${_getEmotionLabel(trigger.resultingEmotion)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${(trigger.strength * 100).toInt()}%',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${trigger.occurrenceCount}次',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmotionalHistorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '情感历史',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        
        if (_emotionalStates.isEmpty)
          const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Center(
                child: Text(
                  '还没有情感记录',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          )
        else
          ...(_emotionalStates.take(10).map((state) => _buildEmotionalStateCard(state))),
      ],
    );
  }

  Widget _buildEmotionalStateCard(EmotionalState state) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (state.dominantEmotion != null) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getEmotionColor(
                        EmotionType.values.firstWhere((e) => e.name == state.dominantEmotion),
                      ).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getEmotionLabel(
                        EmotionType.values.firstWhere((e) => e.name == state.dominantEmotion),
                      ),
                      style: TextStyle(
                        fontSize: 10,
                        color: _getEmotionColor(
                          EmotionType.values.firstWhere((e) => e.name == state.dominantEmotion),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  _getIntensityLabel(state.intensity),
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Text(
                  DateFormat('MM/dd HH:mm').format(state.timestamp),
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
            
            if (state.emotionalKeywords.isNotEmpty) ...[
              const SizedBox(height: 6),
              Wrap(
                spacing: 4,
                children: state.emotionalKeywords.take(3).map((keyword) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      keyword,
                      style: const TextStyle(fontSize: 8),
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getEmotionLabel(EmotionType emotion) {
    switch (emotion) {
      case EmotionType.joy:
        return '快乐';
      case EmotionType.sadness:
        return '悲伤';
      case EmotionType.anger:
        return '愤怒';
      case EmotionType.fear:
        return '恐惧';
      case EmotionType.surprise:
        return '惊讶';
      case EmotionType.disgust:
        return '厌恶';
      case EmotionType.trust:
        return '信任';
      case EmotionType.anticipation:
        return '期待';
      case EmotionType.love:
        return '爱';
      case EmotionType.excitement:
        return '兴奋';
      case EmotionType.anxiety:
        return '焦虑';
      case EmotionType.frustration:
        return '沮丧';
      case EmotionType.contentment:
        return '满足';
      case EmotionType.loneliness:
        return '孤独';
      case EmotionType.gratitude:
        return '感激';
      case EmotionType.confusion:
        return '困惑';
    }
  }

  Color _getEmotionColor(EmotionType emotion) {
    switch (emotion) {
      case EmotionType.joy:
      case EmotionType.love:
      case EmotionType.excitement:
      case EmotionType.contentment:
      case EmotionType.gratitude:
        return Colors.green;
      case EmotionType.sadness:
      case EmotionType.loneliness:
      case EmotionType.frustration:
        return Colors.blue;
      case EmotionType.anger:
      case EmotionType.disgust:
        return Colors.red;
      case EmotionType.fear:
      case EmotionType.anxiety:
        return Colors.orange;
      case EmotionType.surprise:
      case EmotionType.confusion:
        return Colors.purple;
      case EmotionType.trust:
      case EmotionType.anticipation:
        return Colors.teal;
    }
  }

  String _getIntensityLabel(EmotionIntensity intensity) {
    switch (intensity) {
      case EmotionIntensity.low:
        return '轻微';
      case EmotionIntensity.medium:
        return '中等';
      case EmotionIntensity.high:
        return '强烈';
      case EmotionIntensity.extreme:
        return '极强';
    }
  }
}
