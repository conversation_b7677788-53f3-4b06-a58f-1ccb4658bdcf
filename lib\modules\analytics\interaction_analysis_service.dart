import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';

class InteractionAnalysisService {
  final StorageService _storageService;
  final Uuid _uuid = const Uuid();

  static const String _defaultUserId = 'default_user';

  InteractionAnalysisService(this._storageService);

  // 生成互动分析
  Future<InteractionAnalysis> generateAnalysis({
    required String characterId,
    required AnalysisPeriod period,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final now = DateTime.now();
    final analysisStart = startDate ?? _getStartDateForPeriod(period, now);
    final analysisEnd = endDate ?? now;

    // 获取分析期间的数据
    final messages = await _getMessagesInPeriod(characterId, analysisStart, analysisEnd);
    final conversations = await _getConversationsInPeriod(characterId, analysisStart, analysisEnd);
    final emotionalStates = await _getEmotionalStatesInPeriod(characterId, analysisStart, analysisEnd);
    final character = await _storageService.getCharacter(characterId);

    // 计算指标
    final metrics = await _calculateMetrics(messages, conversations, analysisStart, analysisEnd);
    final patterns = await _analyzeCommunicationPatterns(messages, emotionalStates);
    final insights = await _generateRelationshipInsights(characterId, messages, emotionalStates, character);
    final trends = await _analyzeTrends(characterId, period, analysisEnd);
    final recommendations = await _generateRecommendations(metrics, patterns, insights);

    final analysis = InteractionAnalysis(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      analysisDate: now,
      period: period,
      metrics: metrics,
      communicationPatterns: patterns,
      relationshipInsights: insights,
      trends: trends,
      recommendations: recommendations,
      highlights: _generateHighlights(metrics, patterns, insights),
      concerns: _generateConcerns(metrics, patterns, insights),
    );

    await _storageService.saveInteractionAnalysis(analysis);
    return analysis;
  }

  // 获取时间段开始日期
  DateTime _getStartDateForPeriod(AnalysisPeriod period, DateTime endDate) {
    switch (period) {
      case AnalysisPeriod.daily:
        return DateTime(endDate.year, endDate.month, endDate.day);
      case AnalysisPeriod.weekly:
        return endDate.subtract(const Duration(days: 7));
      case AnalysisPeriod.monthly:
        return DateTime(endDate.year, endDate.month - 1, endDate.day);
      case AnalysisPeriod.quarterly:
        return DateTime(endDate.year, endDate.month - 3, endDate.day);
      case AnalysisPeriod.yearly:
        return DateTime(endDate.year - 1, endDate.month, endDate.day);
    }
  }

  // 获取时间段内的消息
  Future<List<Message>> _getMessagesInPeriod(
    String characterId,
    DateTime start,
    DateTime end,
  ) async {
    final conversations = await _storageService.getConversationsByCharacter(characterId);
    final allMessages = <Message>[];

    for (final conversation in conversations) {
      final messages = await _storageService.getMessages(conversation.id);
      final periodMessages = messages.where((msg) =>
          msg.timestamp.isAfter(start) && msg.timestamp.isBefore(end)).toList();
      allMessages.addAll(periodMessages);
    }

    return allMessages;
  }

  // 获取时间段内的对话
  Future<List<Conversation>> _getConversationsInPeriod(
    String characterId,
    DateTime start,
    DateTime end,
  ) async {
    final conversations = await _storageService.getConversationsByCharacter(characterId);
    return conversations.where((conv) =>
        conv.createdAt.isAfter(start) && conv.createdAt.isBefore(end)).toList();
  }

  // 获取时间段内的情感状态
  Future<List<EmotionalState>> _getEmotionalStatesInPeriod(
    String characterId,
    DateTime start,
    DateTime end,
  ) async {
    return await _storageService.getEmotionalStatesInRange(
      _defaultUserId,
      characterId,
      start,
      end,
    );
  }

  // 计算互动指标
  Future<InteractionMetrics> _calculateMetrics(
    List<Message> messages,
    List<Conversation> conversations,
    DateTime start,
    DateTime end,
  ) async {
    final userMessages = messages.where((m) => m.sender == MessageSender.user).toList();
    final characterMessages = messages.where((m) => m.sender == MessageSender.character).toList();

    // 计算平均消息长度
    final avgMessageLength = messages.isNotEmpty
        ? messages.map((m) => m.content.length).reduce((a, b) => a + b) / messages.length
        : 0.0;

    // 计算总聊天时间和平均会话时长
    Duration totalChatTime = Duration.zero;
    Duration avgSessionDuration = Duration.zero;

    if (conversations.isNotEmpty) {
      for (final conv in conversations) {
        final convMessages = messages.where((m) => m.conversationId == conv.id).toList();
        if (convMessages.length >= 2) {
          final sessionDuration = convMessages.last.timestamp.difference(convMessages.first.timestamp);
          totalChatTime += sessionDuration;
        }
      }
      avgSessionDuration = Duration(milliseconds: totalChatTime.inMilliseconds ~/ conversations.length);
    }

    // 计算响应率
    final responseRate = userMessages.isNotEmpty
        ? characterMessages.length / userMessages.length
        : 0.0;

    // 计算平均响应时间
    Duration avgResponseTime = Duration.zero;
    if (userMessages.isNotEmpty && characterMessages.isNotEmpty) {
      final responseTimes = <Duration>[];
      for (int i = 0; i < userMessages.length - 1; i++) {
        final userMsg = userMessages[i];
        final nextCharMsg = characterMessages.where((m) =>
            m.timestamp.isAfter(userMsg.timestamp)).firstOrNull;
        if (nextCharMsg != null) {
          responseTimes.add(nextCharMsg.timestamp.difference(userMsg.timestamp));
        }
      }
      if (responseTimes.isNotEmpty) {
        final totalMs = responseTimes.map((d) => d.inMilliseconds).reduce((a, b) => a + b);
        avgResponseTime = Duration(milliseconds: totalMs ~/ responseTimes.length);
      }
    }

    // 计算每日消息数和聊天时间
    final dailyMessageCounts = <String, int>{};
    final dailyChatTimes = <String, Duration>{};

    for (final message in messages) {
      final dateKey = '${message.timestamp.year}-${message.timestamp.month}-${message.timestamp.day}';
      dailyMessageCounts[dateKey] = (dailyMessageCounts[dateKey] ?? 0) + 1;
    }

    // 计算连续聊天天数
    final chatDays = dailyMessageCounts.keys.map((key) {
      final parts = key.split('-');
      return DateTime(int.parse(parts[0]), int.parse(parts[1]), int.parse(parts[2]));
    }).toList()..sort();

    int longestStreak = 0;
    int currentStreak = 0;

    if (chatDays.isNotEmpty) {
      currentStreak = 1;
      int tempStreak = 1;

      for (int i = 1; i < chatDays.length; i++) {
        if (chatDays[i].difference(chatDays[i-1]).inDays == 1) {
          tempStreak++;
        } else {
          longestStreak = longestStreak > tempStreak ? longestStreak : tempStreak;
          tempStreak = 1;
        }
      }
      longestStreak = longestStreak > tempStreak ? longestStreak : tempStreak;

      // 检查当前连续天数
      final today = DateTime.now();
      final lastChatDay = chatDays.last;
      if (today.difference(lastChatDay).inDays > 1) {
        currentStreak = 0;
      } else {
        currentStreak = tempStreak;
      }
    }

    return InteractionMetrics(
      totalMessages: messages.length,
      userMessages: userMessages.length,
      characterMessages: characterMessages.length,
      averageMessageLength: avgMessageLength,
      totalChatTime: totalChatTime,
      averageSessionDuration: avgSessionDuration,
      sessionCount: conversations.length,
      responseRate: responseRate,
      averageResponseTime: avgResponseTime,
      dailyMessageCounts: dailyMessageCounts,
      dailyChatTimes: dailyChatTimes,
      longestStreak: longestStreak,
      currentStreak: currentStreak,
    );
  }

  // 分析沟通模式
  Future<CommunicationPatterns> _analyzeCommunicationPatterns(
    List<Message> messages,
    List<EmotionalState> emotionalStates,
  ) async {
    final userMessages = messages.where((m) => m.sender == MessageSender.user).toList();

    // 分析话题分布
    final topicDistribution = _analyzeTopicDistribution(userMessages);

    // 分析情感分布
    final emotionDistribution = _analyzeEmotionDistribution(emotionalStates);

    // 分析时间活动模式
    final timeOfDayActivity = _analyzeTimeOfDayActivity(messages);
    final dayOfWeekActivity = _analyzeDayOfWeekActivity(messages);

    // 分析常用词汇和短语
    final frequentWords = _extractFrequentWords(userMessages);
    final frequentPhrases = _extractFrequentPhrases(userMessages);

    // 计算平均情感倾向
    final avgSentiment = emotionalStates.isNotEmpty
        ? emotionalStates.map((e) => e.valence).reduce((a, b) => a + b) / emotionalStates.length
        : 0.0;

    // 确定主导沟通风格
    final dominantStyle = _determineCommunicationStyle(userMessages, emotionalStates);

    // 分析对话主题
    final themes = _analyzeConversationThemes(userMessages);

    return CommunicationPatterns(
      topicDistribution: topicDistribution,
      emotionDistribution: emotionDistribution,
      timeOfDayActivity: timeOfDayActivity,
      dayOfWeekActivity: dayOfWeekActivity,
      frequentWords: frequentWords,
      frequentPhrases: frequentPhrases,
      averageSentiment: avgSentiment,
      dominantStyle: dominantStyle,
      themes: themes,
    );
  }

  // 分析话题分布
  Map<String, double> _analyzeTopicDistribution(List<Message> messages) {
    final topicCounts = <String, int>{};
    final topics = {
      '工作': ['工作', '职业', '公司', '同事', '老板', '项目', '会议'],
      '学习': ['学习', '考试', '课程', '学校', '老师', '作业', '知识'],
      '娱乐': ['电影', '音乐', '游戏', '电视', '综艺', '娱乐', '休闲'],
      '运动': ['运动', '健身', '跑步', '游泳', '篮球', '足球', '锻炼'],
      '旅行': ['旅行', '旅游', '出差', '度假', '景点', '酒店', '机票'],
      '美食': ['吃', '美食', '餐厅', '烹饪', '菜', '饭', '零食'],
      '情感': ['爱情', '友情', '家人', '朋友', '恋人', '感情', '关系'],
      '健康': ['健康', '医生', '医院', '药', '身体', '生病', '治疗'],
    };

    for (final message in messages) {
      final content = message.content.toLowerCase();
      for (final entry in topics.entries) {
        final topic = entry.key;
        final keywords = entry.value;

        final matches = keywords.where((keyword) => content.contains(keyword)).length;
        if (matches > 0) {
          topicCounts[topic] = (topicCounts[topic] ?? 0) + matches;
        }
      }
    }

    final total = topicCounts.values.fold(0, (sum, count) => sum + count);
    if (total == 0) return {};

    return topicCounts.map((topic, count) => MapEntry(topic, count / total));
  }

  // 分析情感分布
  Map<String, double> _analyzeEmotionDistribution(List<EmotionalState> states) {
    if (states.isEmpty) return {};

    final emotionTotals = <String, double>{};

    for (final state in states) {
      state.emotions.forEach((emotion, value) {
        emotionTotals[emotion.name] = (emotionTotals[emotion.name] ?? 0.0) + value;
      });
    }

    final total = emotionTotals.values.fold(0.0, (sum, value) => sum + value);
    if (total == 0) return {};

    return emotionTotals.map((emotion, value) => MapEntry(emotion, value / total));
  }

  // 分析时间活动模式
  Map<String, double> _analyzeTimeOfDayActivity(List<Message> messages) {
    final hourCounts = <int, int>{};

    for (final message in messages) {
      final hour = message.timestamp.hour;
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
    }

    final timeSlots = <String, double>{};
    final total = messages.length;

    if (total > 0) {
      // 早晨 (6-12)
      final morningCount = hourCounts.entries
          .where((e) => e.key >= 6 && e.key < 12)
          .fold(0, (sum, e) => sum + e.value);
      timeSlots['早晨'] = morningCount / total;

      // 下午 (12-18)
      final afternoonCount = hourCounts.entries
          .where((e) => e.key >= 12 && e.key < 18)
          .fold(0, (sum, e) => sum + e.value);
      timeSlots['下午'] = afternoonCount / total;

      // 晚上 (18-24)
      final eveningCount = hourCounts.entries
          .where((e) => e.key >= 18 && e.key < 24)
          .fold(0, (sum, e) => sum + e.value);
      timeSlots['晚上'] = eveningCount / total;

      // 深夜 (0-6)
      final nightCount = hourCounts.entries
          .where((e) => e.key >= 0 && e.key < 6)
          .fold(0, (sum, e) => sum + e.value);
      timeSlots['深夜'] = nightCount / total;
    }

    return timeSlots;
  }

  // 分析星期活动模式
  Map<String, double> _analyzeDayOfWeekActivity(List<Message> messages) {
    final dayCounts = <int, int>{};

    for (final message in messages) {
      final weekday = message.timestamp.weekday;
      dayCounts[weekday] = (dayCounts[weekday] ?? 0) + 1;
    }

    final total = messages.length;
    if (total == 0) return {};

    final dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final dayActivity = <String, double>{};

    for (int i = 1; i <= 7; i++) {
      final count = dayCounts[i] ?? 0;
      dayActivity[dayNames[i - 1]] = count / total;
    }

    return dayActivity;
  }

  // 提取常用词汇
  List<String> _extractFrequentWords(List<Message> messages) {
    final wordCounts = <String, int>{};
    final stopWords = {'的', '了', '是', '我', '你', '他', '她', '它', '在', '有', '和', '与', '或', '但', '而', '就', '都', '也', '还', '又', '再', '很', '最', '更', '太', '非常'};

    for (final message in messages) {
      final words = message.content.split(RegExp(r'[\s，。！？；：""''（）【】《》]'));
      for (final word in words) {
        final cleanWord = word.trim();
        if (cleanWord.isNotEmpty && cleanWord.length > 1 && !stopWords.contains(cleanWord)) {
          wordCounts[cleanWord] = (wordCounts[cleanWord] ?? 0) + 1;
        }
      }
    }

    final sortedWords = wordCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedWords.take(10).map((e) => e.key).toList();
  }

  // 提取常用短语
  List<String> _extractFrequentPhrases(List<Message> messages) {
    final phraseCounts = <String, int>{};

    for (final message in messages) {
      final content = message.content;
      // 简单的短语提取：寻找常见的2-3字组合
      for (int i = 0; i < content.length - 1; i++) {
        if (i + 2 <= content.length) {
          final phrase = content.substring(i, i + 2);
          if (phrase.trim().length == 2) {
            phraseCounts[phrase] = (phraseCounts[phrase] ?? 0) + 1;
          }
        }
      }
    }

    final sortedPhrases = phraseCounts.entries
        .where((e) => e.value > 2) // 至少出现3次
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedPhrases.take(5).map((e) => e.key).toList();
  }

  // 确定沟通风格
  CommunicationStyle _determineCommunicationStyle(
    List<Message> messages,
    List<EmotionalState> emotionalStates,
  ) {
    if (messages.isEmpty) return CommunicationStyle.casual;

    final avgLength = messages.map((m) => m.content.length).reduce((a, b) => a + b) / messages.length;
    final emotionalCount = emotionalStates.length;
    final questionCount = messages.where((m) => m.content.contains('?') || m.content.contains('？')).length;

    // 基于消息特征判断风格
    if (avgLength > 100) {
      return CommunicationStyle.analytical;
    } else if (emotionalCount > messages.length * 0.3) {
      return CommunicationStyle.emotional;
    } else if (questionCount > messages.length * 0.2) {
      return CommunicationStyle.supportive;
    } else {
      return CommunicationStyle.casual;
    }
  }

  // 分析对话主题
  List<ConversationTheme> _analyzeConversationThemes(List<Message> messages) {
    // 简化的主题分析
    final themes = <ConversationTheme>[];
    final topicKeywords = {
      '日常生活': ['生活', '日常', '今天', '昨天', '明天'],
      '工作学习': ['工作', '学习', '项目', '任务', '考试'],
      '情感交流': ['感觉', '心情', '情绪', '开心', '难过'],
      '兴趣爱好': ['喜欢', '爱好', '兴趣', '游戏', '音乐'],
    };

    for (final entry in topicKeywords.entries) {
      final theme = entry.key;
      final keywords = entry.value;

      final relatedMessages = messages.where((m) =>
          keywords.any((keyword) => m.content.contains(keyword))).toList();

      if (relatedMessages.isNotEmpty) {
        final avgSentiment = 0.5; // 简化的情感计算
        themes.add(ConversationTheme(
          theme: theme,
          frequency: relatedMessages.length / messages.length,
          sentiment: avgSentiment,
          keywords: keywords,
          firstMention: relatedMessages.first.timestamp,
          lastMention: relatedMessages.last.timestamp,
        ));
      }
    }

    return themes;
  }

  // 生成关系洞察
  Future<RelationshipInsights> _generateRelationshipInsights(
    String characterId,
    List<Message> messages,
    List<EmotionalState> emotionalStates,
    Character? character,
  ) async {
    // 计算亲密度增长率
    final intimacyGrowthRate = await _calculateIntimacyGrowthRate(characterId);

    // 计算参与度分数
    final engagementScore = _calculateEngagementScore(messages, emotionalStates);

    // 计算信任等级
    final trustLevel = _calculateTrustLevel(messages);

    // 计算情感连接
    final emotionalConnection = _calculateEmotionalConnection(emotionalStates);

    // 获取关系里程碑
    final milestones = await _getRelationshipMilestones(characterId);

    // 分析关系维度
    final relationshipDimensions = _analyzeRelationshipDimensions(messages, emotionalStates);

    // 确定当前关系阶段
    final currentStage = _determineRelationshipStage(character?.intimacyLevel ?? 0);

    // 预测下一阶段
    final predictedNextStage = _predictNextStage(currentStage, intimacyGrowthRate);

    // 生成优势和改进领域
    final strengthAreas = _identifyStrengthAreas(relationshipDimensions);
    final improvementAreas = _identifyImprovementAreas(relationshipDimensions);

    return RelationshipInsights(
      intimacyGrowthRate: intimacyGrowthRate,
      engagementScore: engagementScore,
      trustLevel: trustLevel,
      emotionalConnection: emotionalConnection,
      milestones: milestones,
      relationshipDimensions: relationshipDimensions,
      currentStage: currentStage,
      predictedNextStage: predictedNextStage,
      strengthAreas: strengthAreas,
      improvementAreas: improvementAreas,
    );
  }

  // 计算亲密度增长率
  Future<double> _calculateIntimacyGrowthRate(String characterId) async {
    // 获取最近30天的亲密度变化
    final character = await _storageService.getCharacter(characterId);
    if (character == null) return 0.0;

    // 简化计算：基于当前亲密度估算增长率
    final currentLevel = character.intimacyLevel;
    if (currentLevel < 20) return 0.8; // 初期快速增长
    if (currentLevel < 50) return 0.5; // 中期稳定增长
    if (currentLevel < 80) return 0.3; // 后期缓慢增长
    return 0.1; // 高级阶段很慢
  }

  // 计算参与度分数
  double _calculateEngagementScore(List<Message> messages, List<EmotionalState> states) {
    if (messages.isEmpty) return 0.0;

    final userMessages = messages.where((m) => m.sender == MessageSender.user).toList();
    double score = 0.0;

    // 消息频率分数 (40%)
    score += (userMessages.length / messages.length) * 40;

    // 平均消息长度分数 (30%)
    if (userMessages.isNotEmpty) {
      final avgLength = userMessages.map((m) => m.content.length).reduce((a, b) => a + b) / userMessages.length;
      score += (avgLength / 50).clamp(0, 30);
    }

    // 情感表达分数 (20%)
    if (states.isNotEmpty) {
      final emotionalMessages = states.where((s) => s.emotions.values.any((v) => v > 0.3)).length;
      score += (emotionalMessages / states.length) * 20;
    }

    // 互动性分数 (10%)
    final questionMessages = userMessages.where((m) => m.content.contains('?') || m.content.contains('？')).length;
    if (userMessages.isNotEmpty) {
      score += (questionMessages / userMessages.length) * 10;
    }

    return score.clamp(0, 100);
  }

  // 计算信任等级
  double _calculateTrustLevel(List<Message> messages) {
    if (messages.isEmpty) return 0.0;

    final userMessages = messages.where((m) => m.sender == MessageSender.user).toList();
    double trustScore = 0.0;

    // 个人信息分享
    final personalSharing = userMessages.where((m) =>
        m.content.contains('我的') || m.content.contains('我觉得') || m.content.contains('我认为')).length;
    trustScore += (personalSharing / userMessages.length) * 40;

    // 情感表达
    final emotionalSharing = userMessages.where((m) =>
        m.content.contains('感觉') || m.content.contains('心情') || m.content.contains('情绪')).length;
    trustScore += (emotionalSharing / userMessages.length) * 30;

    // 寻求建议
    final adviceSeeking = userMessages.where((m) =>
        m.content.contains('怎么办') || m.content.contains('建议') || m.content.contains('意见')).length;
    trustScore += (adviceSeeking / userMessages.length) * 30;

    return trustScore.clamp(0, 100) / 100;
  }

  // 计算情感连接
  double _calculateEmotionalConnection(List<EmotionalState> states) {
    if (states.isEmpty) return 0.0;

    // 计算情感多样性
    final emotionTypes = states.expand((s) => s.emotions.keys).toSet().length;
    final diversityScore = (emotionTypes / EmotionType.values.length) * 50;

    // 计算情感强度
    final avgIntensity = states.map((s) => s.emotions.values.isNotEmpty
        ? s.emotions.values.reduce((a, b) => a > b ? a : b)
        : 0.0).reduce((a, b) => a + b) / states.length;
    final intensityScore = avgIntensity * 50;

    return (diversityScore + intensityScore).clamp(0, 100) / 100;
  }

  // 获取关系里程碑
  Future<List<RelationshipMilestone>> _getRelationshipMilestones(String characterId) async {
    // 简化实现：生成一些基本里程碑
    final milestones = <RelationshipMilestone>[];
    final character = await _storageService.getCharacter(characterId);

    if (character != null) {
      // 第一条消息里程碑
      milestones.add(RelationshipMilestone(
        id: _uuid.v4(),
        title: '初次相遇',
        description: '你们的第一次对话',
        achievedAt: character.createdAt,
        type: MilestoneType.firstMessage,
        significance: 0.8,
      ));

      // 亲密度里程碑
      if (character.intimacyLevel >= 20) {
        milestones.add(RelationshipMilestone(
          id: _uuid.v4(),
          title: '成为朋友',
          description: '你们的关系更进一步',
          achievedAt: character.createdAt.add(const Duration(days: 7)),
          type: MilestoneType.intimacyLevel,
          significance: 0.9,
        ));
      }
    }

    return milestones;
  }

  // 分析关系维度
  Map<String, double> _analyzeRelationshipDimensions(
    List<Message> messages,
    List<EmotionalState> states,
  ) {
    final dimensions = <String, double>{};

    if (messages.isNotEmpty) {
      final userMessages = messages.where((m) => m.sender == MessageSender.user).toList();

      // 沟通频率
      dimensions['沟通频率'] = (messages.length / 30).clamp(0, 1); // 假设30天期间

      // 情感表达
      dimensions['情感表达'] = states.isNotEmpty
          ? (states.length / userMessages.length).clamp(0, 1)
          : 0.0;

      // 互动深度
      final avgLength = userMessages.isNotEmpty
          ? userMessages.map((m) => m.content.length).reduce((a, b) => a + b) / userMessages.length
          : 0.0;
      dimensions['互动深度'] = (avgLength / 100).clamp(0, 1);

      // 信任程度
      dimensions['信任程度'] = _calculateTrustLevel(messages);

      // 情感连接
      dimensions['情感连接'] = _calculateEmotionalConnection(states);
    }

    return dimensions;
  }

  // 确定关系阶段
  RelationshipStage _determineRelationshipStage(int intimacyLevel) {
    if (intimacyLevel < 10) return RelationshipStage.stranger;
    if (intimacyLevel < 30) return RelationshipStage.acquaintance;
    if (intimacyLevel < 50) return RelationshipStage.friend;
    if (intimacyLevel < 70) return RelationshipStage.closeFriend;
    if (intimacyLevel < 90) return RelationshipStage.bestFriend;
    return RelationshipStage.companion;
  }

  // 预测下一阶段
  RelationshipStage _predictNextStage(RelationshipStage current, double growthRate) {
    if (growthRate < 0.1) return current; // 增长太慢，保持当前阶段

    final stages = RelationshipStage.values;
    final currentIndex = stages.indexOf(current);
    if (currentIndex < stages.length - 1) {
      return stages[currentIndex + 1];
    }
    return current;
  }

  // 识别优势领域
  List<String> _identifyStrengthAreas(Map<String, double> dimensions) {
    return dimensions.entries
        .where((e) => e.value > 0.7)
        .map((e) => e.key)
        .toList();
  }

  // 识别改进领域
  List<String> _identifyImprovementAreas(Map<String, double> dimensions) {
    return dimensions.entries
        .where((e) => e.value < 0.4)
        .map((e) => e.key)
        .toList();
  }

  // 分析趋势
  Future<List<InteractionTrend>> _analyzeTrends(
    String characterId,
    AnalysisPeriod period,
    DateTime endDate,
  ) async {
    final trends = <InteractionTrend>[];

    // 简化的趋势分析
    trends.add(InteractionTrend(
      metric: '消息频率',
      direction: TrendDirection.increasing,
      changePercentage: 15.0,
      dataPoints: [
        DataPoint(timestamp: endDate.subtract(const Duration(days: 7)), value: 10),
        DataPoint(timestamp: endDate.subtract(const Duration(days: 3)), value: 12),
        DataPoint(timestamp: endDate, value: 15),
      ],
      description: '最近消息频率呈上升趋势',
      significance: TrendSignificance.medium,
    ));

    return trends;
  }

  // 生成建议
  Future<List<String>> _generateRecommendations(
    InteractionMetrics metrics,
    CommunicationPatterns patterns,
    RelationshipInsights insights,
  ) async {
    final recommendations = <String>[];

    // 基于参与度的建议
    if (insights.engagementScore < 50) {
      recommendations.add('尝试更频繁地与${await _getCharacterName(insights.toString())}交流');
    }

    // 基于情感连接的建议
    if (insights.emotionalConnection < 0.5) {
      recommendations.add('分享更多个人感受和想法');
    }

    // 基于沟通模式的建议
    if (patterns.averageSentiment < 0) {
      recommendations.add('尝试聊一些更积极的话题');
    }

    return recommendations;
  }

  // 生成亮点
  List<String> _generateHighlights(
    InteractionMetrics metrics,
    CommunicationPatterns patterns,
    RelationshipInsights insights,
  ) {
    final highlights = <String>[];

    if (metrics.currentStreak > 3) {
      highlights.add('连续${metrics.currentStreak}天保持对话');
    }

    if (insights.engagementScore > 80) {
      highlights.add('互动参与度很高');
    }

    if (patterns.averageSentiment > 0.5) {
      highlights.add('整体情绪积极向上');
    }

    return highlights;
  }

  // 生成关注点
  List<String> _generateConcerns(
    InteractionMetrics metrics,
    CommunicationPatterns patterns,
    RelationshipInsights insights,
  ) {
    final concerns = <String>[];

    if (metrics.currentStreak == 0) {
      concerns.add('最近没有进行对话');
    }

    if (insights.engagementScore < 30) {
      concerns.add('互动参与度较低');
    }

    if (patterns.averageSentiment < -0.3) {
      concerns.add('情绪偏向消极');
    }

    return concerns;
  }

  // 获取角色名称
  Future<String> _getCharacterName(String characterId) async {
    final character = await _storageService.getCharacter(characterId);
    return character?.name ?? '角色';
  }

  // 获取分析历史
  Future<List<InteractionAnalysis>> getAnalysisHistory({
    required String characterId,
    int limit = 10,
  }) async {
    return await _storageService.getInteractionAnalyses(_defaultUserId, characterId, limit: limit);
  }

  // 生成报告
  Future<InteractionReport> generateReport({
    required String characterId,
    required ReportType type,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final period = _getAnalysisPeriodForReportType(type);
    final analysis = await generateAnalysis(
      characterId: characterId,
      period: period,
      startDate: startDate,
      endDate: endDate,
    );

    final character = await _storageService.getCharacter(characterId);
    final characterName = character?.name ?? '角色';

    final report = InteractionReport(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      generatedAt: DateTime.now(),
      type: type,
      title: '${_getReportTypeLabel(type)} - $characterName',
      summary: _generateReportSummary(analysis),
      sections: _generateReportSections(analysis),
      keyFindings: analysis.highlights,
      actionItems: analysis.recommendations,
    );

    await _storageService.saveInteractionReport(report);
    return report;
  }

  // 获取报告类型对应的分析周期
  AnalysisPeriod _getAnalysisPeriodForReportType(ReportType type) {
    switch (type) {
      case ReportType.weekly:
        return AnalysisPeriod.weekly;
      case ReportType.monthly:
        return AnalysisPeriod.monthly;
      case ReportType.relationship:
        return AnalysisPeriod.monthly;
      case ReportType.emotional:
        return AnalysisPeriod.weekly;
      case ReportType.communication:
        return AnalysisPeriod.weekly;
    }
  }

  // 获取报告类型标签
  String _getReportTypeLabel(ReportType type) {
    switch (type) {
      case ReportType.weekly:
        return '周报';
      case ReportType.monthly:
        return '月报';
      case ReportType.relationship:
        return '关系报告';
      case ReportType.emotional:
        return '情感报告';
      case ReportType.communication:
        return '沟通报告';
    }
  }

  // 生成报告摘要
  String _generateReportSummary(InteractionAnalysis analysis) {
    return '本期共进行了${analysis.metrics.totalMessages}条消息的交流，'
           '参与度分数为${analysis.relationshipInsights.engagementScore.toInt()}分，'
           '整体表现${analysis.relationshipInsights.engagementScore > 70 ? "优秀" : "良好"}。';
  }

  // 生成报告章节
  List<ReportSection> _generateReportSections(InteractionAnalysis analysis) {
    return [
      ReportSection(
        title: '互动概览',
        content: '本期互动数据统计和基本指标',
        type: SectionType.overview,
        data: {
          'totalMessages': analysis.metrics.totalMessages,
          'engagementScore': analysis.relationshipInsights.engagementScore,
        },
      ),
      ReportSection(
        title: '沟通模式',
        content: '分析沟通习惯和偏好',
        type: SectionType.insights,
        data: {
          'dominantStyle': analysis.communicationPatterns.dominantStyle.name,
          'averageSentiment': analysis.communicationPatterns.averageSentiment,
        },
      ),
      ReportSection(
        title: '关系洞察',
        content: '关系发展状况和建议',
        type: SectionType.recommendations,
        highlights: analysis.recommendations,
      ),
    ];
  }
}

// Provider
final interactionAnalysisServiceProvider = Provider<InteractionAnalysisService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return InteractionAnalysisService(storageService);
});