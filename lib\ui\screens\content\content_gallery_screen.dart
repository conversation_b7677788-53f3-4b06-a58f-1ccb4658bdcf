import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../models/models.dart';
import '../../../modules/content_unlocking/content_unlocking_service.dart';
import '../../widgets/enhanced_loading.dart';
import '../../widgets/enhanced_card.dart';
import '../../widgets/enhanced_button.dart';

class ContentGalleryScreen extends ConsumerStatefulWidget {
  final String characterId;

  const ContentGalleryScreen({
    super.key,
    required this.characterId,
  });

  @override
  ConsumerState<ContentGalleryScreen> createState() => _ContentGalleryScreenState();
}

class _ContentGalleryScreenState extends ConsumerState<ContentGalleryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<UnlockableContent> _allContent = [];
  List<UnlockableContent> _unlockedContent = [];
  List<UnlockableContent> _lockedContent = [];
  List<ContentRecommendation> _recommendations = [];
  Character? _character;
  bool _isLoading = true;
  
  ContentFilter _filter = const ContentFilter();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    final storageService = ref.read(storageServiceProvider);
    final contentService = ref.read(contentUnlockingServiceProvider);
    
    final character = await storageService.getCharacter(widget.characterId);
    final allContent = await contentService.getContent(characterId: widget.characterId);
    final recommendations = await contentService.generateRecommendations(widget.characterId);
    
    // 分类内容
    final unlocks = await storageService.getContentUnlocks('default_user', widget.characterId);
    final unlockedIds = unlocks.map((u) => u.contentId).toSet();
    
    final unlockedContent = allContent.where((c) => unlockedIds.contains(c.id)).toList();
    final lockedContent = allContent.where((c) => !unlockedIds.contains(c.id)).toList();

    if (mounted) {
      setState(() {
        _character = character;
        _allContent = allContent;
        _unlockedContent = unlockedContent;
        _lockedContent = lockedContent;
        _recommendations = recommendations;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _character == null) {
      return const Scaffold(
        body: Center(
          child: EnhancedLoading(
            message: '正在加载内容画廊...',
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${_character!.name} 的内容画廊'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: const Icon(Icons.apps),
              text: '全部 (${_allContent.length})',
            ),
            Tab(
              icon: const Icon(Icons.lock_open),
              text: '已解锁 (${_unlockedContent.length})',
            ),
            Tab(
              icon: const Icon(Icons.lock),
              text: '未解锁 (${_lockedContent.length})',
            ),
            Tab(
              icon: Badge(
                isLabelVisible: _recommendations.isNotEmpty,
                label: Text('${_recommendations.length}'),
                child: const Icon(Icons.recommend),
              ),
              text: '推荐',
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildContentGrid(_allContent),
          _buildContentGrid(_unlockedContent),
          _buildContentGrid(_lockedContent),
          _buildRecommendationsTab(),
        ],
      ),
    );
  }

  Widget _buildContentGrid(List<UnlockableContent> content) {
    if (content.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.photo_library, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('暂无内容', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: content.length,
      itemBuilder: (context, index) {
        final item = content[index];
        return _buildContentCard(item);
      },
    );
  }

  Widget _buildContentCard(UnlockableContent content) {
    final isUnlocked = _unlockedContent.any((c) => c.id == content.id);
    
    return EnhancedCard(
      onTap: isUnlocked ? () => _viewContent(content) : () => _showUnlockProgress(content),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 内容预览图
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                image: content.imageUrl != null
                    ? DecorationImage(
                        image: NetworkImage(content.imageUrl!),
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
              child: Stack(
                children: [
                  if (content.imageUrl == null)
                    Center(
                      child: Icon(
                        _getContentTypeIcon(content.type),
                        size: 32,
                        color: Colors.grey[400],
                      ),
                    ),
                  
                  // 解锁状态覆盖层
                  if (!isUnlocked)
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                      ),
                      child: const Center(
                        child: Icon(
                          Icons.lock,
                          size: 32,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  
                  // 稀有度标识
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getRarityColor(content.rarity),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _getRarityLabel(content.rarity),
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  
                  // 内容类型标识
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _getContentTypeLabel(content.type),
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 内容信息
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    content.title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  
                  Text(
                    content.description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Spacer(),
                  
                  // 解锁进度或查看次数
                  if (isUnlocked)
                    Row(
                      children: [
                        Icon(Icons.visibility, size: 12, color: Colors.grey[500]),
                        const SizedBox(width: 4),
                        Text(
                          '${content.viewCount}次',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[500],
                          ),
                        ),
                        const Spacer(),
                        if (content.userRating > 0)
                          Row(
                            children: [
                              Icon(Icons.star, size: 12, color: Colors.amber),
                              const SizedBox(width: 2),
                              Text(
                                content.userRating.toStringAsFixed(1),
                                style: const TextStyle(fontSize: 10),
                              ),
                            ],
                          ),
                      ],
                    )
                  else
                    FutureBuilder<UnlockProgress?>(
                      future: ref.read(storageServiceProvider).getUnlockProgress(
                        'default_user',
                        widget.characterId,
                        content.id,
                      ),
                      builder: (context, snapshot) {
                        final progress = snapshot.data?.overallProgress ?? 0.0;
                        return Column(
                          children: [
                            Row(
                              children: [
                                Text(
                                  '${(progress * 100).toInt()}%',
                                  style: const TextStyle(fontSize: 10),
                                ),
                                const Spacer(),
                                Text(
                                  '解锁进度',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            LinearProgressIndicator(
                              value: progress,
                              backgroundColor: Colors.grey[300],
                              valueColor: AlwaysStoppedAnimation<Color>(
                                progress >= 0.8 ? Colors.green : Theme.of(context).primaryColor,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationsTab() {
    if (_recommendations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.recommend_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('暂无推荐内容', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _recommendations.length,
      itemBuilder: (context, index) {
        final recommendation = _recommendations[index];
        return _buildRecommendationCard(recommendation);
      },
    );
  }

  Widget _buildRecommendationCard(ContentRecommendation recommendation) {
    return FutureBuilder<UnlockableContent?>(
      future: ref.read(storageServiceProvider).getUnlockableContent(recommendation.contentId),
      builder: (context, snapshot) {
        final content = snapshot.data;
        if (content == null) {
          return const SizedBox.shrink();
        }

        return EnhancedCard(
          margin: const EdgeInsets.only(bottom: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getRecommendationIcon(recommendation.reason),
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      content.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getConfidenceColor(recommendation.confidence).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${(recommendation.confidence * 100).toInt()}%',
                      style: TextStyle(
                        fontSize: 10,
                        color: _getConfidenceColor(recommendation.confidence),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              
              Text(
                recommendation.description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 12),
              
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _viewContent(content),
                      child: const Text('查看详情'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  OutlinedButton(
                    onPressed: () => _dismissRecommendation(recommendation),
                    child: const Text('忽略'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _viewContent(UnlockableContent content) {
    // 记录查看
    ref.read(contentUnlockingServiceProvider).viewContent(content.id, widget.characterId);
    
    // 导航到内容详情页
    context.go('/content-detail/${content.id}');
  }

  void _showUnlockProgress(UnlockableContent content) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              content.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Text(
              content.description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),
            
            FutureBuilder<UnlockProgress?>(
              future: ref.read(storageServiceProvider).getUnlockProgress(
                'default_user',
                widget.characterId,
                content.id,
              ),
              builder: (context, snapshot) {
                final progress = snapshot.data;
                if (progress == null) {
                  return const Text('暂无解锁进度');
                }

                return Column(
                  children: [
                    Text(
                      '解锁进度: ${(progress.overallProgress * 100).toInt()}%',
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(value: progress.overallProgress),
                    const SizedBox(height: 16),
                    
                    if (progress.pendingConditions.isNotEmpty) ...[
                      const Text(
                        '待完成条件:',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      ...progress.pendingConditions.map((conditionId) => 
                        Text('• 条件 $conditionId')),
                    ],
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterDialog() {
    // TODO: 实现过滤器对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('过滤功能即将推出')),
    );
  }

  void _dismissRecommendation(ContentRecommendation recommendation) {
    setState(() {
      _recommendations.removeWhere((r) => r.id == recommendation.id);
    });
  }

  IconData _getContentTypeIcon(ContentType type) {
    switch (type) {
      case ContentType.story:
        return Icons.book;
      case ContentType.memory:
        return Icons.psychology;
      case ContentType.dialogue:
        return Icons.chat;
      case ContentType.image:
        return Icons.image;
      case ContentType.audio:
        return Icons.audiotrack;
      case ContentType.video:
        return Icons.videocam;
      case ContentType.background:
        return Icons.landscape;
      case ContentType.achievement:
        return Icons.emoji_events;
      case ContentType.outfit:
        return Icons.checkroom;
      case ContentType.accessory:
        return Icons.diamond;
      case ContentType.scene:
        return Icons.theater_comedy;
      case ContentType.emotion:
        return Icons.sentiment_satisfied;
    }
  }

  String _getContentTypeLabel(ContentType type) {
    switch (type) {
      case ContentType.story:
        return '故事';
      case ContentType.memory:
        return '记忆';
      case ContentType.dialogue:
        return '对话';
      case ContentType.image:
        return '图片';
      case ContentType.audio:
        return '音频';
      case ContentType.video:
        return '视频';
      case ContentType.background:
        return '背景';
      case ContentType.achievement:
        return '成就';
      case ContentType.outfit:
        return '服装';
      case ContentType.accessory:
        return '配饰';
      case ContentType.scene:
        return '场景';
      case ContentType.emotion:
        return '情感';
    }
  }

  String _getRarityLabel(ContentRarity rarity) {
    switch (rarity) {
      case ContentRarity.common:
        return '普通';
      case ContentRarity.uncommon:
        return '不常见';
      case ContentRarity.rare:
        return '稀有';
      case ContentRarity.epic:
        return '史诗';
      case ContentRarity.legendary:
        return '传说';
    }
  }

  Color _getRarityColor(ContentRarity rarity) {
    switch (rarity) {
      case ContentRarity.common:
        return Colors.grey;
      case ContentRarity.uncommon:
        return Colors.green;
      case ContentRarity.rare:
        return Colors.blue;
      case ContentRarity.epic:
        return Colors.purple;
      case ContentRarity.legendary:
        return Colors.orange;
    }
  }

  IconData _getRecommendationIcon(RecommendationReason reason) {
    switch (reason) {
      case RecommendationReason.nearUnlock:
        return Icons.lock_open;
      case RecommendationReason.similarContent:
        return Icons.similar;
      case RecommendationReason.userPreference:
        return Icons.favorite;
      case RecommendationReason.trending:
        return Icons.trending_up;
      case RecommendationReason.seasonal:
        return Icons.calendar_today;
      case RecommendationReason.completion:
        return Icons.check_circle;
    }
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return Colors.green;
    if (confidence >= 0.6) return Colors.orange;
    return Colors.red;
  }
}
