import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../modules/time_awareness/time_provider.dart';
import '../widgets/enhanced_button.dart';
import '../widgets/enhanced_card.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timeState = ref.watch(timeAwarenessProvider);
    final holidayGreeting = ref.watch(holidayGreetingProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('AIX Companion'),
        actions: [
          IconButton(
            icon: const Icon(Icons.access_time),
            onPressed: () {
              context.go('/time-settings');
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: Navigate to settings
            },
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 节日问候
            if (holidayGreeting != null) ...[
              Padding(
                padding: const EdgeInsets.all(16),
                child: GradientCard(
                  colors: [Colors.orange[300]!, Colors.orange[100]!],
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.celebration,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        holidayGreeting,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],

            const Icon(
              Icons.psychology,
              size: 100,
              color: Color(0xFF6B73FF),
            ),
            const SizedBox(height: 20),
            const Text(
              '欢迎来到 AIX Companion',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              '选择你的AI伴侣开始对话',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 20),

            // 时间感知状态
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: EnhancedCard(
                color: timeState.isActive ? Colors.green[50] : Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                enableHover: false,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      timeState.isActive ? Icons.access_time : Icons.access_time_outlined,
                      size: 16,
                      color: timeState.isActive ? Colors.green : Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '智能问候: ${timeState.isActive ? "已启用" : "已禁用"}',
                      style: TextStyle(
                        fontSize: 12,
                        color: timeState.isActive ? Colors.green[700] : Colors.grey[600],
                      ),
                    ),
                    if (timeState.currentTimePeriod.isNotEmpty) ...[
                      const SizedBox(width: 8),
                      Text(
                        '(${timeState.currentTimePeriod})',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButtonEnhanced(
            onPressed: () {
              context.go('/role-management');
            },
            icon: Icons.manage_accounts,
            tooltip: '角色管理',
            mini: true,
          ),
          const SizedBox(height: 8),
          FloatingActionButtonEnhanced(
            onPressed: () {
              context.go('/group-list');
            },
            icon: Icons.group,
            tooltip: '群聊',
            mini: true,
          ),
          const SizedBox(height: 16),
          FloatingActionButtonEnhanced(
            onPressed: () {
              context.go('/character-selection');
            },
            icon: Icons.psychology,
            tooltip: '选择角色',
          ),
        ],
      ),
    );
  }
}
