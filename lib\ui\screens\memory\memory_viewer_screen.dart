import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../models/models.dart';
import '../../../modules/memory_system/memory_service.dart';

class MemoryViewerScreen extends ConsumerStatefulWidget {
  final String characterId;

  const MemoryViewerScreen({
    super.key,
    required this.characterId,
  });

  @override
  ConsumerState<MemoryViewerScreen> createState() => _MemoryViewerScreenState();
}

class _MemoryViewerScreenState extends ConsumerState<MemoryViewerScreen> {
  static const String _defaultUserId = 'default_user';
  List<Memory> _memories = [];
  MemoryType? _selectedType;
  bool _isLoading = true;
  Character? _character;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final memoryService = ref.read(memoryServiceProvider);
    final storageService = ref.read(storageServiceProvider);
    
    final character = await storageService.getCharacter(widget.characterId);
    final memories = await memoryService.getCharacterMemories(
      widget.characterId,
      _defaultUserId,
    );

    if (mounted) {
      setState(() {
        _character = character;
        _memories = memories;
        _isLoading = false;
      });
    }
  }

  List<Memory> get _filteredMemories {
    if (_selectedType == null) return _memories;
    return _memories.where((memory) => memory.type == _selectedType).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _character == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${_character!.name} 的记忆'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.category),
            onPressed: () {
              context.go('/memory-cluster/${widget.characterId}');
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 记忆类型筛选
          _buildTypeFilter(),
          
          // 记忆统计
          _buildMemoryStats(),
          
          // 记忆列表
          Expanded(
            child: _filteredMemories.isEmpty
                ? _buildEmptyState()
                : _buildMemoryList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            FilterChip(
              label: const Text('全部'),
              selected: _selectedType == null,
              onSelected: (selected) {
                setState(() {
                  _selectedType = null;
                });
              },
            ),
            const SizedBox(width: 8),
            ...MemoryType.values.map((type) {
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(_getMemoryTypeLabel(type)),
                  selected: _selectedType == type,
                  onSelected: (selected) {
                    setState(() {
                      _selectedType = selected ? type : null;
                    });
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildMemoryStats() {
    final totalMemories = _memories.length;
    final typeStats = <MemoryType, int>{};
    
    for (final memory in _memories) {
      typeStats[memory.type] = (typeStats[memory.type] ?? 0) + 1;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '记忆统计',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Text('总记忆数: $totalMemories'),
              if (typeStats.isNotEmpty) ...[
                const SizedBox(height: 4),
                Wrap(
                  spacing: 8,
                  children: typeStats.entries.map((entry) {
                    return Chip(
                      label: Text('${_getMemoryTypeLabel(entry.key)}: ${entry.value}'),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.psychology_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '还没有记忆',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '开始和${_character!.name}聊天来创建记忆吧！',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemoryList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredMemories.length,
      itemBuilder: (context, index) {
        final memory = _filteredMemories[index];
        return _buildMemoryCard(memory);
      },
    );
  }

  Widget _buildMemoryCard(Memory memory) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 记忆类型和时间
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getMemoryTypeColor(memory.type).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getMemoryTypeLabel(memory.type),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getMemoryTypeColor(memory.type),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Text(
                  DateFormat('MM/dd HH:mm').format(memory.timestamp),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // 记忆摘要
            Text(
              memory.summary,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            
            // 记忆内容
            Text(
              memory.content,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
            
            // 标签和重要性
            if (memory.tags.isNotEmpty || memory.importance > 1.0) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  if (memory.tags.isNotEmpty) ...[
                    Expanded(
                      child: Wrap(
                        spacing: 4,
                        children: memory.tags.map((tag) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              tag,
                              style: const TextStyle(fontSize: 10),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                  if (memory.importance > 1.0) ...[
                    const SizedBox(width: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: 16,
                          color: Colors.amber[600],
                        ),
                        const SizedBox(width: 2),
                        Text(
                          memory.importance.toStringAsFixed(1),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.amber[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ],
            
            // 访问统计
            if (memory.accessCount > 0) ...[
              const SizedBox(height: 8),
              Text(
                '访问次数: ${memory.accessCount}',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getMemoryTypeLabel(MemoryType type) {
    switch (type) {
      case MemoryType.conversation:
        return '对话';
      case MemoryType.personal:
        return '个人信息';
      case MemoryType.preference:
        return '偏好';
      case MemoryType.emotion:
        return '情感';
      case MemoryType.event:
        return '事件';
      case MemoryType.relationship:
        return '关系';
      case MemoryType.habit:
        return '习惯';
      case MemoryType.goal:
        return '目标';
    }
  }

  Color _getMemoryTypeColor(MemoryType type) {
    switch (type) {
      case MemoryType.conversation:
        return Colors.blue;
      case MemoryType.personal:
        return Colors.green;
      case MemoryType.preference:
        return Colors.purple;
      case MemoryType.emotion:
        return Colors.red;
      case MemoryType.event:
        return Colors.orange;
      case MemoryType.relationship:
        return Colors.pink;
      case MemoryType.habit:
        return Colors.teal;
      case MemoryType.goal:
        return Colors.indigo;
    }
  }
}
