import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class CharacterSelectionScreen extends StatelessWidget {
  const CharacterSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('选择你的AI伴侣'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/home'),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.8,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: _characters.length,
          itemBuilder: (context, index) {
            final character = _characters[index];
            return CharacterCard(
              character: character,
              onTap: () {
                context.go('/chat/${character.id}');
              },
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Navigate to character creation
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('自定义角色功能即将推出')),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}

class CharacterCard extends StatelessWidget {
  final Character character;
  final VoidCallback onTap;

  const CharacterCard({
    super.key,
    required this.character,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Avatar
              Center(
                child: CircleAvatar(
                  radius: 40,
                  backgroundColor: character.color,
                  child: Text(
                    character.name[0],
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              
              // Name
              Text(
                character.name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              
              // Description
              Text(
                character.description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const Spacer(),
              
              // Personality tags
              Wrap(
                spacing: 4,
                children: character.traits.take(2).map((trait) {
                  return Chip(
                    label: Text(
                      trait,
                      style: const TextStyle(fontSize: 10),
                    ),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Character {
  final String id;
  final String name;
  final String description;
  final List<String> traits;
  final Color color;

  Character({
    required this.id,
    required this.name,
    required this.description,
    required this.traits,
    required this.color,
  });
}

final List<Character> _characters = [
  Character(
    id: '1',
    name: '小雨',
    description: '温柔体贴的邻家女孩，喜欢文学和音乐',
    traits: ['温柔', '文艺', '善解人意'],
    color: Colors.pink,
  ),
  Character(
    id: '2',
    name: '阿凯',
    description: '阳光开朗的运动男孩，充满正能量',
    traits: ['阳光', '运动', '幽默'],
    color: Colors.blue,
  ),
  Character(
    id: '3',
    name: '小雪',
    description: '知性优雅的职场女性，理性而独立',
    traits: ['知性', '独立', '理性'],
    color: Colors.purple,
  ),
  Character(
    id: '4',
    name: '小明',
    description: '技术宅男，对科技和游戏充满热情',
    traits: ['技术', '游戏', '创新'],
    color: Colors.green,
  ),
];
