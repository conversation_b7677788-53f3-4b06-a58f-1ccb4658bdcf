import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../models/models.dart';
import '../../../services/storage_service.dart';
import '../../widgets/intimacy_indicator.dart';
import '../../widgets/enhanced_loading.dart';
import '../../widgets/enhanced_card.dart';
import '../../widgets/enhanced_button.dart';

class CharacterSelectionScreen extends ConsumerStatefulWidget {
  const CharacterSelectionScreen({super.key});

  @override
  ConsumerState<CharacterSelectionScreen> createState() => _CharacterSelectionScreenState();
}

class _CharacterSelectionScreenState extends ConsumerState<CharacterSelectionScreen> {
  List<Character> _characters = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCharacters();
  }

  Future<void> _loadCharacters() async {
    final storageService = ref.read(storageServiceProvider);
    final characters = await storageService.getAllCharacters();
    if (mounted) {
      setState(() {
        _characters = characters;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('选择你的AI伴侣'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/home'),
        ),
      ),
      body: _isLoading
          ? const Center(
              child: EnhancedLoading(
                message: '正在加载角色...',
              ),
            )
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.75,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: _characters.length,
                itemBuilder: (context, index) {
                  final character = _characters[index];
                  return CharacterCard(
                    character: character,
                    onTap: () {
                      context.go('/chat/${character.id}');
                    },
                  );
                },
              ),
            ),
      floatingActionButton: FloatingActionButtonEnhanced(
        onPressed: () {
          // TODO: Navigate to character creation
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('自定义角色功能即将推出')),
          );
        },
        icon: Icons.add,
        tooltip: '创建新角色',
      ),
    );
  }
}

class CharacterCard extends StatelessWidget {
  final Character character;
  final VoidCallback onTap;

  const CharacterCard({
    super.key,
    required this.character,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SlideInAnimation(
      delay: Duration(milliseconds: 100),
      child: EnhancedCard(
        onTap: onTap,
        elevation: 6,
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getCharacterColor(character.id).withOpacity(0.1),
            _getCharacterColor(character.id).withOpacity(0.05),
          ],
        ),
        child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Avatar with status indicator
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 35,
                      backgroundColor: _getCharacterColor(character.id),
                      child: Text(
                        character.name[0],
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    if (character.isActive)
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),

                // Name and basic info
                Text(
                  character.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '${character.age}岁 · ${character.occupation}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // Description
                Expanded(
                  child: Text(
                    character.description,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[700],
                      height: 1.3,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 8),

                // Personality tags
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  alignment: WrapAlignment.center,
                  children: character.traits.take(3).map((trait) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getCharacterColor(character.id).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        trait,
                        style: TextStyle(
                          fontSize: 10,
                          color: _getCharacterColor(character.id),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 8),

                // Intimacy level indicator
                if (character.intimacyLevel > 0)
                  IntimacyIndicator(
                    intimacyLevel: character.intimacyLevel,
                    showLabel: false,
                    showProgress: false,
                    size: 16,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getCharacterColor(String characterId) {
    switch (characterId) {
      case '1':
        return Colors.pink;
      case '2':
        return Colors.blue;
      case '3':
        return Colors.purple;
      case '4':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}


