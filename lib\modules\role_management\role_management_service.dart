import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';

class RoleManagementService {
  final StorageService _storageService;
  final Uuid _uuid = const Uuid();
  
  static const String _defaultUserId = 'default_user';

  RoleManagementService(this._storageService);

  // 创建角色模板
  Future<RoleTemplate> createRoleTemplate({
    required String name,
    required String description,
    required String category,
    required RoleArchetype archetype,
    required List<String> traits,
    required List<String> interests,
    required Map<String, String> defaultAttributes,
    required String systemPrompt,
    required String greetingTemplate,
    List<String> tags = const [],
    bool isCustom = true,
    bool isPublic = false,
    String? createdBy,
  }) async {
    final template = RoleTemplate(
      id: _uuid.v4(),
      name: name,
      description: description,
      category: category,
      archetype: archetype,
      traits: traits,
      interests: interests,
      defaultAttributes: defaultAttributes,
      systemPrompt: systemPrompt,
      greetingTemplate: greetingTemplate,
      tags: tags,
      isCustom: isCustom,
      isPublic: isPublic,
      createdAt: DateTime.now(),
      createdBy: createdBy ?? _defaultUserId,
    );

    await _storageService.saveRoleTemplate(template);
    return template;
  }

  // 从模板创建角色
  Future<Character> createCharacterFromTemplate({
    required String templateId,
    String? customName,
    Map<String, String>? customAttributes,
  }) async {
    final template = await _storageService.getRoleTemplate(templateId);
    if (template == null) {
      throw Exception('角色模板不存在');
    }

    // 合并默认属性和自定义属性
    final attributes = Map<String, String>.from(template.defaultAttributes);
    if (customAttributes != null) {
      attributes.addAll(customAttributes);
    }

    final character = Character(
      id: _uuid.v4(),
      name: customName ?? template.name,
      age: int.tryParse(attributes['age'] ?? '25') ?? 25,
      occupation: attributes['occupation'] ?? '未知',
      description: template.description,
      traits: List.from(template.traits),
      interests: List.from(template.interests),
      intimacyLevel: 0,
      isActive: true,
      createdAt: DateTime.now(),
      systemPrompt: template.systemPrompt,
      greetingMessage: template.greetingTemplate,
      metadata: {
        'templateId': templateId,
        'archetype': template.archetype.name,
        'category': template.category,
        'customAttributes': customAttributes ?? {},
      },
    );

    await _storageService.saveCharacter(character);
    return character;
  }

  // 创建角色集合
  Future<RoleCollection> createRoleCollection({
    required String name,
    required String description,
    required List<String> characterIds,
    List<String> tags = const [],
    bool isDefault = false,
    bool isShared = false,
  }) async {
    final collection = RoleCollection(
      id: _uuid.v4(),
      name: name,
      description: description,
      userId: _defaultUserId,
      characterIds: characterIds,
      tags: tags,
      isDefault: isDefault,
      isShared: isShared,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _storageService.saveRoleCollection(collection);
    return collection;
  }

  // 添加角色到集合
  Future<void> addCharacterToCollection(String collectionId, String characterId) async {
    final collection = await _storageService.getRoleCollection(collectionId);
    if (collection == null) return;

    if (!collection.characterIds.contains(characterId)) {
      final updatedCollection = collection.copyWith(
        characterIds: [...collection.characterIds, characterId],
        updatedAt: DateTime.now(),
      );
      await _storageService.saveRoleCollection(updatedCollection);
    }
  }

  // 从集合移除角色
  Future<void> removeCharacterFromCollection(String collectionId, String characterId) async {
    final collection = await _storageService.getRoleCollection(collectionId);
    if (collection == null) return;

    final updatedCharacterIds = collection.characterIds.where((id) => id != characterId).toList();
    final updatedCollection = collection.copyWith(
      characterIds: updatedCharacterIds,
      updatedAt: DateTime.now(),
    );
    await _storageService.saveRoleCollection(updatedCollection);
  }

  // 创建角色互动规则
  Future<RoleInteractionRule> createInteractionRule({
    required String fromCharacterId,
    required String toCharacterId,
    required InteractionType type,
    required double strength,
    required List<String> contexts,
    required Map<String, String> responses,
    Map<String, dynamic>? conditions,
  }) async {
    final rule = RoleInteractionRule(
      id: _uuid.v4(),
      fromCharacterId: fromCharacterId,
      toCharacterId: toCharacterId,
      type: type,
      strength: strength,
      contexts: contexts,
      responses: responses,
      createdAt: DateTime.now(),
      conditions: conditions,
    );

    await _storageService.saveRoleInteractionRule(rule);
    return rule;
  }

  // 获取角色间互动规则
  Future<List<RoleInteractionRule>> getInteractionRules({
    String? fromCharacterId,
    String? toCharacterId,
  }) async {
    return await _storageService.getRoleInteractionRules(
      fromCharacterId: fromCharacterId,
      toCharacterId: toCharacterId,
    );
  }

  // 创建角色日程
  Future<RoleSchedule> createRoleSchedule({
    required String characterId,
    required ScheduleType type,
    required DateTime startTime,
    DateTime? endTime,
    required List<int> weekdays,
    required String activity,
    required String description,
    int priority = 0,
    Map<String, dynamic>? settings,
  }) async {
    final schedule = RoleSchedule(
      id: _uuid.v4(),
      characterId: characterId,
      userId: _defaultUserId,
      type: type,
      startTime: startTime,
      endTime: endTime,
      weekdays: weekdays,
      activity: activity,
      description: description,
      priority: priority,
      settings: settings,
    );

    await _storageService.saveRoleSchedule(schedule);
    return schedule;
  }

  // 获取角色日程
  Future<List<RoleSchedule>> getRoleSchedules(String characterId) async {
    return await _storageService.getRoleSchedules(characterId);
  }

  // 获取活跃的日程
  Future<List<RoleSchedule>> getActiveSchedules(String characterId) async {
    final schedules = await getRoleSchedules(characterId);
    final now = DateTime.now();
    final currentWeekday = now.weekday;

    return schedules.where((schedule) {
      if (!schedule.isActive) return false;
      if (!schedule.weekdays.contains(currentWeekday)) return false;
      
      // 检查时间范围
      final startTime = schedule.startTime;
      final endTime = schedule.endTime;
      
      if (endTime != null) {
        return now.isAfter(startTime) && now.isBefore(endTime);
      } else {
        // 如果没有结束时间，检查是否在同一小时内
        return now.hour == startTime.hour;
      }
    }).toList()
      ..sort((a, b) => b.priority.compareTo(a.priority));
  }

  // 应用角色自定义
  Future<RoleCustomization> applyCustomization({
    required String characterId,
    required CustomizationType type,
    required String property,
    required dynamic value,
    String? reason,
  }) async {
    // 获取当前角色
    final character = await _storageService.getCharacter(characterId);
    if (character == null) {
      throw Exception('角色不存在');
    }

    // 获取当前值
    dynamic previousValue;
    switch (type) {
      case CustomizationType.personality:
        if (property == 'traits') {
          previousValue = character.traits;
        }
        break;
      case CustomizationType.interests:
        if (property == 'interests') {
          previousValue = character.interests;
        }
        break;
      case CustomizationType.appearance:
        previousValue = character.metadata?[property];
        break;
      default:
        previousValue = character.metadata?[property];
    }

    // 创建自定义记录
    final customization = RoleCustomization(
      id: _uuid.v4(),
      characterId: characterId,
      userId: _defaultUserId,
      type: type,
      property: property,
      value: value,
      previousValue: previousValue,
      appliedAt: DateTime.now(),
      reason: reason,
    );

    // 应用更改到角色
    Character updatedCharacter = character;
    switch (type) {
      case CustomizationType.personality:
        if (property == 'traits' && value is List<String>) {
          updatedCharacter = character.copyWith(traits: value);
        }
        break;
      case CustomizationType.interests:
        if (property == 'interests' && value is List<String>) {
          updatedCharacter = character.copyWith(interests: value);
        }
        break;
      case CustomizationType.appearance:
      case CustomizationType.behavior:
      case CustomizationType.speech:
      case CustomizationType.background:
      case CustomizationType.skills:
        final metadata = Map<String, dynamic>.from(character.metadata ?? {});
        metadata[property] = value;
        updatedCharacter = character.copyWith(metadata: metadata);
        break;
    }

    await _storageService.saveCharacter(updatedCharacter);
    await _storageService.saveRoleCustomization(customization);
    
    return customization;
  }

  // 获取角色自定义历史
  Future<List<RoleCustomization>> getCustomizationHistory(String characterId) async {
    return await _storageService.getRoleCustomizations(characterId);
  }

  // 撤销自定义
  Future<void> revertCustomization(String customizationId) async {
    final customization = await _storageService.getRoleCustomization(customizationId);
    if (customization == null || !customization.isActive) return;

    // 恢复到之前的值
    if (customization.previousValue != null) {
      await applyCustomization(
        characterId: customization.characterId,
        type: customization.type,
        property: customization.property,
        value: customization.previousValue,
        reason: '撤销自定义',
      );
    }

    // 标记为非活跃
    final updatedCustomization = customization.copyWith(isActive: false);
    await _storageService.saveRoleCustomization(updatedCustomization);
  }

  // 检测角色冲突
  Future<List<RoleConflict>> detectRoleConflicts(List<String> characterIds) async {
    final conflicts = <RoleConflict>[];
    
    for (int i = 0; i < characterIds.length; i++) {
      for (int j = i + 1; j < characterIds.length; j++) {
        final char1 = await _storageService.getCharacter(characterIds[i]);
        final char2 = await _storageService.getCharacter(characterIds[j]);
        
        if (char1 == null || char2 == null) continue;

        // 检测性格冲突
        final personalityConflict = _detectPersonalityConflict(char1, char2);
        if (personalityConflict != null) {
          conflicts.add(personalityConflict);
        }

        // 检测角色重叠
        final roleOverlap = _detectRoleOverlap(char1, char2);
        if (roleOverlap != null) {
          conflicts.add(roleOverlap);
        }
      }
    }

    // 保存检测到的冲突
    for (final conflict in conflicts) {
      await _storageService.saveRoleConflict(conflict);
    }

    return conflicts;
  }

  // 检测性格冲突
  RoleConflict? _detectPersonalityConflict(Character char1, Character char2) {
    final conflictingTraits = {
      '内向': '外向',
      '严肃': '幽默',
      '理性': '感性',
      '保守': '开放',
      '独立': '依赖',
    };

    for (final trait1 in char1.traits) {
      final conflictTrait = conflictingTraits[trait1];
      if (conflictTrait != null && char2.traits.contains(conflictTrait)) {
        return RoleConflict(
          id: _uuid.v4(),
          characterIds: [char1.id, char2.id],
          userId: _defaultUserId,
          type: ConflictType.personalityClash,
          description: '${char1.name}的"$trait1"特质与${char2.name}的"$conflictTrait"特质存在冲突',
          severity: ConflictSeverity.medium,
          detectedAt: DateTime.now(),
          resolutionOptions: [
            '调整${char1.name}的性格特质',
            '调整${char2.name}的性格特质',
            '创建互补关系',
            '忽略此冲突',
          ],
        );
      }
    }

    return null;
  }

  // 检测角色重叠
  RoleConflict? _detectRoleOverlap(Character char1, Character char2) {
    // 检查兴趣重叠度
    final commonInterests = char1.interests.where((interest) => 
        char2.interests.contains(interest)).toList();
    
    // 检查特质重叠度
    final commonTraits = char1.traits.where((trait) => 
        char2.traits.contains(trait)).toList();

    final overlapScore = (commonInterests.length + commonTraits.length) / 
                        (char1.interests.length + char1.traits.length);

    if (overlapScore > 0.7) {
      return RoleConflict(
        id: _uuid.v4(),
        characterIds: [char1.id, char2.id],
        userId: _defaultUserId,
        type: ConflictType.roleOverlap,
        description: '${char1.name}和${char2.name}的角色定位过于相似（重叠度${(overlapScore * 100).toInt()}%）',
        severity: overlapScore > 0.8 ? ConflictSeverity.high : ConflictSeverity.medium,
        detectedAt: DateTime.now(),
        resolutionOptions: [
          '差异化${char1.name}的特征',
          '差异化${char2.name}的特征',
          '合并为一个角色',
          '保持现状',
        ],
      );
    }

    return null;
  }

  // 生成角色推荐
  Future<List<RoleRecommendation>> generateRecommendations() async {
    final recommendations = <RoleRecommendation>[];
    
    // 分析用户的角色使用模式
    final characters = await _storageService.getAllCharacters();
    final userPreferences = await _analyzeUserPreferences(characters);
    
    // 推荐新角色
    final newRoleRec = await _recommendNewRole(userPreferences);
    if (newRoleRec != null) {
      recommendations.add(newRoleRec);
    }

    // 推荐角色改进
    for (final character in characters) {
      final improvementRec = await _recommendRoleImprovement(character);
      if (improvementRec != null) {
        recommendations.add(improvementRec);
      }
    }

    // 保存推荐
    for (final rec in recommendations) {
      await _storageService.saveRoleRecommendation(rec);
    }

    return recommendations;
  }

  // 分析用户偏好
  Future<Map<String, dynamic>> _analyzeUserPreferences(List<Character> characters) async {
    final preferences = <String, dynamic>{};
    
    // 分析最常用的特质
    final traitCounts = <String, int>{};
    for (final char in characters) {
      for (final trait in char.traits) {
        traitCounts[trait] = (traitCounts[trait] ?? 0) + 1;
      }
    }
    preferences['popularTraits'] = traitCounts.entries
        .where((e) => e.value > 1)
        .map((e) => e.key)
        .toList();

    // 分析最常用的兴趣
    final interestCounts = <String, int>{};
    for (final char in characters) {
      for (final interest in char.interests) {
        interestCounts[interest] = (interestCounts[interest] ?? 0) + 1;
      }
    }
    preferences['popularInterests'] = interestCounts.entries
        .where((e) => e.value > 1)
        .map((e) => e.key)
        .toList();

    // 分析角色原型偏好
    final archetypeCounts = <String, int>{};
    for (final char in characters) {
      final archetype = char.metadata?['archetype'] as String?;
      if (archetype != null) {
        archetypeCounts[archetype] = (archetypeCounts[archetype] ?? 0) + 1;
      }
    }
    preferences['preferredArchetypes'] = archetypeCounts.entries
        .map((e) => e.key)
        .toList();

    return preferences;
  }

  // 推荐新角色
  Future<RoleRecommendation?> _recommendNewRole(Map<String, dynamic> preferences) async {
    final popularTraits = preferences['popularTraits'] as List<String>? ?? [];
    final popularInterests = preferences['popularInterests'] as List<String>? ?? [];
    
    if (popularTraits.isEmpty && popularInterests.isEmpty) return null;

    return RoleRecommendation(
      id: _uuid.v4(),
      userId: _defaultUserId,
      type: RecommendationType.newRole,
      title: '推荐新角色',
      description: '基于您的偏好，我们推荐创建一个具有${popularTraits.take(2).join('、')}特质的角色',
      confidence: 0.8,
      data: {
        'suggestedTraits': popularTraits.take(3).toList(),
        'suggestedInterests': popularInterests.take(3).toList(),
        'archetype': 'companion',
      },
      generatedAt: DateTime.now(),
    );
  }

  // 推荐角色改进
  Future<RoleRecommendation?> _recommendRoleImprovement(Character character) async {
    // 检查角色是否需要更多特质
    if (character.traits.length < 3) {
      return RoleRecommendation(
        id: _uuid.v4(),
        userId: _defaultUserId,
        type: RecommendationType.roleImprovement,
        title: '丰富${character.name}的性格',
        description: '${character.name}的性格特质较少，建议添加更多特质来丰富角色',
        confidence: 0.7,
        data: {
          'characterId': character.id,
          'currentTraits': character.traits,
          'suggestedTraits': ['温柔', '体贴', '聪明'],
        },
        generatedAt: DateTime.now(),
      );
    }

    return null;
  }

  // 获取所有角色模板
  Future<List<RoleTemplate>> getAllRoleTemplates() async {
    return await _storageService.getAllRoleTemplates();
  }

  // 获取角色集合
  Future<List<RoleCollection>> getRoleCollections() async {
    return await _storageService.getRoleCollections(_defaultUserId);
  }

  // 获取角色推荐
  Future<List<RoleRecommendation>> getRoleRecommendations() async {
    return await _storageService.getRoleRecommendations(_defaultUserId);
  }

  // 获取角色冲突
  Future<List<RoleConflict>> getRoleConflicts() async {
    return await _storageService.getRoleConflicts(_defaultUserId);
  }

  // 解决角色冲突
  Future<void> resolveConflict(String conflictId, String resolution) async {
    final conflict = await _storageService.getRoleConflict(conflictId);
    if (conflict == null) return;

    final resolvedConflict = conflict.copyWith(
      status: ConflictStatus.resolved,
      chosenResolution: resolution,
      resolvedAt: DateTime.now(),
    );

    await _storageService.saveRoleConflict(resolvedConflict);
  }
}

// Provider
final roleManagementServiceProvider = Provider<RoleManagementService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return RoleManagementService(storageService);
});
