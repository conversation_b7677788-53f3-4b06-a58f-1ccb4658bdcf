import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../modules/performance/performance_optimization_service.dart';
import '../../widgets/enhanced_loading.dart';
import '../../widgets/enhanced_card.dart';
import '../../widgets/enhanced_button.dart';

class PerformanceMonitorScreen extends ConsumerStatefulWidget {
  const PerformanceMonitorScreen({super.key});

  @override
  ConsumerState<PerformanceMonitorScreen> createState() => _PerformanceMonitorScreenState();
}

class _PerformanceMonitorScreenState extends ConsumerState<PerformanceMonitorScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  PerformanceReport? _report;
  List<MemoryUsageSnapshot> _memorySnapshots = [];
  bool _isLoading = true;
  bool _isOptimizing = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    final performanceService = ref.read(performanceOptimizationServiceProvider);
    
    final report = performanceService.generateReport();
    final snapshots = performanceService.getMemorySnapshots();

    if (mounted) {
      setState(() {
        _report = report;
        _memorySnapshots = snapshots;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: EnhancedLoading(
            message: '正在加载性能监控...',
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('性能监控'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: '概览'),
            Tab(icon: Icon(Icons.memory), text: '内存'),
            Tab(icon: Icon(Icons.speed), text: '指标'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildMemoryTab(),
          _buildMetricsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _isOptimizing ? null : _performOptimization,
        icon: _isOptimizing 
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.tune),
        label: Text(_isOptimizing ? '优化中...' : '性能优化'),
      ),
    );
  }

  Widget _buildOverviewTab() {
    if (_report == null) {
      return const Center(child: Text('暂无性能数据'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 性能概览卡片
          _buildPerformanceOverviewCard(),
          const SizedBox(height: 16),
          
          // 推荐建议
          if (_report!.recommendations.isNotEmpty) ...[
            _buildRecommendationsCard(),
            const SizedBox(height: 16),
          ],
          
          // 快速操作
          _buildQuickActionsCard(),
        ],
      ),
    );
  }

  Widget _buildPerformanceOverviewCard() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.dashboard,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                '性能概览',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const Spacer(),
              Text(
                DateFormat('HH:mm:ss').format(_report!.generatedAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 关键指标
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  '缓存命中率',
                  '${(_report!.cacheHitRate * 100).toInt()}%',
                  Icons.cached,
                  _getCacheHitRateColor(_report!.cacheHitRate),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  '平均查询时间',
                  '${_report!.averageQueryTime.toInt()}ms',
                  Icons.timer,
                  _getQueryTimeColor(_report!.averageQueryTime),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  '内存使用',
                  _formatBytes(_report!.currentMemoryUsage),
                  Icons.memory,
                  _getMemoryUsageColor(_report!.currentMemoryUsage),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  '活跃对象',
                  '${_report!.activeObjectCount}',
                  Icons.widgets,
                  Colors.blue,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationsCard() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb,
                color: Colors.amber[700],
              ),
              const SizedBox(width: 8),
              Text(
                '优化建议',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.amber[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          ..._report!.recommendations.map((recommendation) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.arrow_right,
                  size: 16,
                  color: Colors.amber[700],
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    recommendation,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '快速操作',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _clearCache,
                  icon: const Icon(Icons.clear_all, size: 16),
                  label: const Text('清理缓存'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _performMemoryCleanup,
                  icon: const Icon(Icons.cleaning_services, size: 16),
                  label: const Text('内存清理'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMemoryTab() {
    if (_memorySnapshots.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.memory, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('暂无内存数据', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 内存使用趋势图（简化版）
          _buildMemoryTrendCard(),
          const SizedBox(height: 16),
          
          // 内存快照列表
          _buildMemorySnapshotsList(),
        ],
      ),
    );
  }

  Widget _buildMemoryTrendCard() {
    final latest = _memorySnapshots.last;
    
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '内存使用趋势',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildMemoryMetric(
                  '当前使用',
                  _formatBytes(latest.heapUsage),
                  Icons.memory,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMemoryMetric(
                  '缓存项目',
                  '${latest.cacheSize}',
                  Icons.cached,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildMemoryMetric(
                  '活跃对象',
                  '${latest.activeObjects}',
                  Icons.widgets,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMemoryMetric(
                  '快照数量',
                  '${_memorySnapshots.length}',
                  Icons.camera_alt,
                  Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMemoryMetric(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemorySnapshotsList() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '内存快照历史',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          ...(_memorySnapshots.reversed.take(10).map((snapshot) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Text(
                  DateFormat('HH:mm:ss').format(snapshot.timestamp),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    _formatBytes(snapshot.heapUsage),
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                Text(
                  '${snapshot.cacheSize}项',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ))),
        ],
      ),
    );
  }

  Widget _buildMetricsTab() {
    if (_report?.metrics.isEmpty ?? true) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.speed, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('暂无性能指标', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _report!.metrics.length,
      itemBuilder: (context, index) {
        final entry = _report!.metrics.entries.elementAt(index);
        final metric = entry.value;
        return _buildMetricDetailCard(entry.key, metric);
      },
    );
  }

  Widget _buildMetricDetailCard(String name, PerformanceMetric metric) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            metric.name,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildMetricStat('总计', metric.total.toStringAsFixed(1)),
              ),
              Expanded(
                child: _buildMetricStat('平均', metric.average.toStringAsFixed(1)),
              ),
              Expanded(
                child: _buildMetricStat('最大', metric.max.toStringAsFixed(1)),
              ),
              Expanded(
                child: _buildMetricStat('次数', '${metric.count}'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Future<void> _performOptimization() async {
    setState(() {
      _isOptimizing = true;
    });

    try {
      final performanceService = ref.read(performanceOptimizationServiceProvider);
      
      // 执行各种优化操作
      performanceService.clearCache();
      await Future.delayed(const Duration(seconds: 1)); // 模拟优化过程
      
      // 重新加载数据
      await _loadData();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('性能优化完成')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('优化失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isOptimizing = false;
      });
    }
  }

  void _clearCache() {
    final performanceService = ref.read(performanceOptimizationServiceProvider);
    performanceService.clearCache();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('缓存已清理')),
    );
    
    _loadData();
  }

  void _performMemoryCleanup() {
    // 触发内存清理
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('内存清理完成')),
    );
    
    _loadData();
  }

  String _formatBytes(double bytes) {
    if (bytes < 1024) return '${bytes.toInt()}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  Color _getCacheHitRateColor(double rate) {
    if (rate >= 0.8) return Colors.green;
    if (rate >= 0.6) return Colors.orange;
    return Colors.red;
  }

  Color _getQueryTimeColor(double time) {
    if (time <= 50) return Colors.green;
    if (time <= 100) return Colors.orange;
    return Colors.red;
  }

  Color _getMemoryUsageColor(double usage) {
    const threshold = 30 * 1024 * 1024; // 30MB
    if (usage <= threshold * 0.7) return Colors.green;
    if (usage <= threshold) return Colors.orange;
    return Colors.red;
  }
}
