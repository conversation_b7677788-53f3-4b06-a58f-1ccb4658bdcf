import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'message.freezed.dart';
part 'message.g.dart';

@freezed
@HiveType(typeId: 0)
class Message with _$Message {
  const factory Message({
    @HiveField(0) required String id,
    @HiveField(1) required String conversationId,
    @HiveField(2) required String content,
    @HiveField(3) required MessageType type,
    @HiveField(4) required MessageSender sender,
    @HiveField(5) required DateTime timestamp,
    @HiveField(6) @Default(MessageStatus.sent) MessageStatus status,
    @HiveField(7) String? characterId,
    @HiveField(8) Map<String, dynamic>? metadata,
  }) = _Message;

  factory Message.fromJson(Map<String, dynamic> json) => _$MessageFromJson(json);
}

@HiveType(typeId: 1)
enum MessageType {
  @HiveField(0)
  text,
  @HiveField(1)
  image,
  @HiveField(2)
  audio,
  @HiveField(3)
  system,
  @HiveField(4)
  greeting,
}

@HiveType(typeId: 2)
enum MessageSender {
  @HiveField(0)
  user,
  @HiveField(1)
  character,
  @HiveField(2)
  system,
}

@HiveType(typeId: 3)
enum MessageStatus {
  @HiveField(0)
  sending,
  @HiveField(1)
  sent,
  @HiveField(2)
  delivered,
  @HiveField(3)
  read,
  @HiveField(4)
  failed,
}
