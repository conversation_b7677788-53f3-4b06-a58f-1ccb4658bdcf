import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/models.dart';

class StorageService {
  static const String _messagesBox = 'messages';
  static const String _conversationsBox = 'conversations';
  static const String _charactersBox = 'characters';
  static const String _memoriesBox = 'memories';
  static const String _memoryClustersBox = 'memory_clusters';
  static const String _userProfilesBox = 'user_profiles';
  static const String _userPreferencesBox = 'user_preferences';
  static const String _preferencePatternsBox = 'preference_patterns';
  static const String _behaviorAnalysisBox = 'behavior_analysis';
  static const String _conversationInsightsBox = 'conversation_insights';
  static const String _groupConversationsBox = 'group_conversations';
  static const String _groupMessagesBox = 'group_messages';
  static const String _characterRelationshipsBox = 'character_relationships';
  static const String _groupInteractionEventsBox = 'group_interaction_events';
  static const String _conversationDynamicsBox = 'conversation_dynamics';
  static const String _emotionalStatesBox = 'emotional_states';
  static const String _emotionalPatternsBox = 'emotional_patterns';
  static const String _emotionalInsightsBox = 'emotional_insights';
  static const String _moodSummariesBox = 'mood_summaries';
  static const String _emotionalTriggersBox = 'emotional_triggers';

  late Box<Message> _messageBox;
  late Box<Conversation> _conversationBox;
  late Box<Character> _characterBox;
  late Box<Memory> _memoryBox;
  late Box<MemoryCluster> _memoryClusterBox;
  late Box<UserProfile> _userProfileBox;
  late Box<UserPreference> _userPreferenceBox;
  late Box<PreferencePattern> _preferencePatternBox;
  late Box<UserBehaviorAnalysis> _behaviorAnalysisBox;
  late Box<ConversationInsight> _conversationInsightBox;
  late Box<GroupConversation> _groupConversationBox;
  late Box<GroupMessage> _groupMessageBox;
  late Box<CharacterRelationship> _characterRelationshipBox;
  late Box<GroupInteractionEvent> _groupInteractionEventBox;
  late Box<ConversationDynamics> _conversationDynamicsBox;
  late Box<EmotionalState> _emotionalStateBox;
  late Box<EmotionalPattern> _emotionalPatternBox;
  late Box<EmotionalInsight> _emotionalInsightBox;
  late Box<MoodSummary> _moodSummaryBox;
  late Box<EmotionalTrigger> _emotionalTriggerBox;

  // 初始化存储
  Future<void> initialize() async {
    await Hive.initFlutter();
    
    // 注册适配器
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(MessageAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(MessageTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(MessageSenderAdapter());
    }
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(MessageStatusAdapter());
    }
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(CharacterAdapter());
    }
    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(PersonalityTraitsAdapter());
    }
    if (!Hive.isAdapterRegistered(6)) {
      Hive.registerAdapter(CharacterGenderAdapter());
    }
    if (!Hive.isAdapterRegistered(7)) {
      Hive.registerAdapter(ConversationAdapter());
    }
    if (!Hive.isAdapterRegistered(8)) {
      Hive.registerAdapter(ConversationSummaryAdapter());
    }
    if (!Hive.isAdapterRegistered(9)) {
      Hive.registerAdapter(MemoryAdapter());
    }
    if (!Hive.isAdapterRegistered(10)) {
      Hive.registerAdapter(MemoryTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(11)) {
      Hive.registerAdapter(MemoryClusterAdapter());
    }
    if (!Hive.isAdapterRegistered(12)) {
      Hive.registerAdapter(UserProfileAdapter());
    }
    if (!Hive.isAdapterRegistered(13)) {
      Hive.registerAdapter(UserPreferenceAdapter());
    }
    if (!Hive.isAdapterRegistered(14)) {
      Hive.registerAdapter(PreferenceTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(15)) {
      Hive.registerAdapter(PreferencePatternAdapter());
    }
    if (!Hive.isAdapterRegistered(16)) {
      Hive.registerAdapter(UserBehaviorAnalysisAdapter());
    }
    if (!Hive.isAdapterRegistered(17)) {
      Hive.registerAdapter(ConversationInsightAdapter());
    }
    if (!Hive.isAdapterRegistered(18)) {
      Hive.registerAdapter(InsightTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(19)) {
      Hive.registerAdapter(GroupConversationAdapter());
    }
    if (!Hive.isAdapterRegistered(20)) {
      Hive.registerAdapter(GroupConversationTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(21)) {
      Hive.registerAdapter(GroupMessageAdapter());
    }
    if (!Hive.isAdapterRegistered(22)) {
      Hive.registerAdapter(CharacterRelationshipAdapter());
    }
    if (!Hive.isAdapterRegistered(23)) {
      Hive.registerAdapter(RelationshipTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(24)) {
      Hive.registerAdapter(GroupInteractionEventAdapter());
    }
    if (!Hive.isAdapterRegistered(25)) {
      Hive.registerAdapter(InteractionEventTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(26)) {
      Hive.registerAdapter(ConversationDynamicsAdapter());
    }
    if (!Hive.isAdapterRegistered(27)) {
      Hive.registerAdapter(EmotionalStateAdapter());
    }
    if (!Hive.isAdapterRegistered(28)) {
      Hive.registerAdapter(EmotionTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(29)) {
      Hive.registerAdapter(EmotionIntensityAdapter());
    }
    if (!Hive.isAdapterRegistered(30)) {
      Hive.registerAdapter(EmotionalPatternAdapter());
    }
    if (!Hive.isAdapterRegistered(31)) {
      Hive.registerAdapter(PatternTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(32)) {
      Hive.registerAdapter(EmotionalInsightAdapter());
    }
    if (!Hive.isAdapterRegistered(33)) {
      Hive.registerAdapter(InsightCategoryAdapter());
    }
    if (!Hive.isAdapterRegistered(34)) {
      Hive.registerAdapter(MoodSummaryAdapter());
    }
    if (!Hive.isAdapterRegistered(35)) {
      Hive.registerAdapter(EmotionalTriggerAdapter());
    }
    if (!Hive.isAdapterRegistered(36)) {
      Hive.registerAdapter(TriggerTypeAdapter());
    }

    // 打开数据库
    _messageBox = await Hive.openBox<Message>(_messagesBox);
    _conversationBox = await Hive.openBox<Conversation>(_conversationsBox);
    _characterBox = await Hive.openBox<Character>(_charactersBox);
    _memoryBox = await Hive.openBox<Memory>(_memoriesBox);
    _memoryClusterBox = await Hive.openBox<MemoryCluster>(_memoryClustersBox);
    _userProfileBox = await Hive.openBox<UserProfile>(_userProfilesBox);
    _userPreferenceBox = await Hive.openBox<UserPreference>(_userPreferencesBox);
    _preferencePatternBox = await Hive.openBox<PreferencePattern>(_preferencePatternsBox);
    _behaviorAnalysisBox = await Hive.openBox<UserBehaviorAnalysis>(_behaviorAnalysisBox);
    _conversationInsightBox = await Hive.openBox<ConversationInsight>(_conversationInsightsBox);
    _groupConversationBox = await Hive.openBox<GroupConversation>(_groupConversationsBox);
    _groupMessageBox = await Hive.openBox<GroupMessage>(_groupMessagesBox);
    _characterRelationshipBox = await Hive.openBox<CharacterRelationship>(_characterRelationshipsBox);
    _groupInteractionEventBox = await Hive.openBox<GroupInteractionEvent>(_groupInteractionEventsBox);
    _conversationDynamicsBox = await Hive.openBox<ConversationDynamics>(_conversationDynamicsBox);
    _emotionalStateBox = await Hive.openBox<EmotionalState>(_emotionalStatesBox);
    _emotionalPatternBox = await Hive.openBox<EmotionalPattern>(_emotionalPatternsBox);
    _emotionalInsightBox = await Hive.openBox<EmotionalInsight>(_emotionalInsightsBox);
    _moodSummaryBox = await Hive.openBox<MoodSummary>(_moodSummariesBox);
    _emotionalTriggerBox = await Hive.openBox<EmotionalTrigger>(_emotionalTriggersBox);

    // 初始化默认角色
    await _initializeDefaultCharacters();
  }

  // 初始化默认角色
  Future<void> _initializeDefaultCharacters() async {
    if (_characterBox.isEmpty) {
      final defaultCharacters = [
        Character(
          id: '1',
          name: '小雨',
          description: '温柔体贴的邻家女孩，喜欢文学和音乐',
          avatarUrl: '',
          personality: const PersonalityTraits(
            extroversion: 0.3,
            agreeableness: 0.8,
            conscientiousness: 0.7,
            neuroticism: 0.4,
            openness: 0.6,
            humor: 0.5,
            empathy: 0.9,
            creativity: 0.7,
          ),
          interests: ['文学', '音乐', '绘画', '咖啡'],
          gender: CharacterGender.female,
          age: 22,
          occupation: '大学生',
          traits: ['温柔', '文艺', '善解人意'],
          speakingStyle: 'gentle',
        ),
        Character(
          id: '2',
          name: '阿凯',
          description: '阳光开朗的运动男孩，充满正能量',
          avatarUrl: '',
          personality: const PersonalityTraits(
            extroversion: 0.8,
            agreeableness: 0.7,
            conscientiousness: 0.6,
            neuroticism: 0.2,
            openness: 0.7,
            humor: 0.8,
            empathy: 0.6,
            creativity: 0.5,
          ),
          interests: ['运动', '健身', '旅行', '音乐'],
          gender: CharacterGender.male,
          age: 25,
          occupation: '健身教练',
          traits: ['阳光', '运动', '幽默'],
          speakingStyle: 'energetic',
        ),
        Character(
          id: '3',
          name: '小雪',
          description: '知性优雅的职场女性，理性而独立',
          avatarUrl: '',
          personality: const PersonalityTraits(
            extroversion: 0.5,
            agreeableness: 0.6,
            conscientiousness: 0.9,
            neuroticism: 0.3,
            openness: 0.8,
            humor: 0.4,
            empathy: 0.7,
            creativity: 0.6,
          ),
          interests: ['商业', '投资', '读书', '红酒'],
          gender: CharacterGender.female,
          age: 28,
          occupation: '产品经理',
          traits: ['知性', '独立', '理性'],
          speakingStyle: 'professional',
        ),
        Character(
          id: '4',
          name: '小明',
          description: '技术宅男，对科技和游戏充满热情',
          avatarUrl: '',
          personality: const PersonalityTraits(
            extroversion: 0.4,
            agreeableness: 0.6,
            conscientiousness: 0.8,
            neuroticism: 0.3,
            openness: 0.9,
            humor: 0.6,
            empathy: 0.5,
            creativity: 0.8,
          ),
          interests: ['编程', '游戏', '科技', '动漫'],
          gender: CharacterGender.male,
          age: 24,
          occupation: '软件工程师',
          traits: ['技术', '游戏', '创新'],
          speakingStyle: 'technical',
        ),
      ];

      for (final character in defaultCharacters) {
        await _characterBox.put(character.id, character);
      }
    }
  }

  // 消息相关操作
  Future<void> saveMessage(Message message) async {
    await _messageBox.put(message.id, message);
  }

  Future<Message?> getMessage(String messageId) async {
    return _messageBox.get(messageId);
  }

  Future<List<Message>> getConversationMessages(String conversationId) async {
    return _messageBox.values
        .where((message) => message.conversationId == conversationId)
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  Future<void> deleteMessage(String messageId) async {
    await _messageBox.delete(messageId);
  }

  // 对话相关操作
  Future<void> saveConversation(Conversation conversation) async {
    await _conversationBox.put(conversation.id, conversation);
  }

  Future<Conversation?> getConversation(String conversationId) async {
    return _conversationBox.get(conversationId);
  }

  Future<List<Conversation>> getCharacterConversations(String characterId) async {
    return _conversationBox.values
        .where((conversation) => conversation.characterId == characterId)
        .toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<List<Conversation>> getAllConversations() async {
    return _conversationBox.values.toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<void> deleteConversation(String conversationId) async {
    // 删除对话相关的所有消息
    final messages = await getConversationMessages(conversationId);
    for (final message in messages) {
      await deleteMessage(message.id);
    }
    // 删除对话
    await _conversationBox.delete(conversationId);
  }

  Future<void> clearConversationMessages(String conversationId) async {
    final messages = await getConversationMessages(conversationId);
    for (final message in messages) {
      await deleteMessage(message.id);
    }
    
    // 重置对话状态
    final conversation = await getConversation(conversationId);
    if (conversation != null) {
      final clearedConversation = conversation.copyWith(
        messageIds: [],
        messageCount: 0,
        lastMessageId: null,
        lastMessagePreview: null,
        unreadCount: 0,
        updatedAt: DateTime.now(),
      );
      await saveConversation(clearedConversation);
    }
  }

  // 角色相关操作
  Future<void> saveCharacter(Character character) async {
    await _characterBox.put(character.id, character);
  }

  Future<Character?> getCharacter(String characterId) async {
    return _characterBox.get(characterId);
  }

  Future<List<Character>> getAllCharacters() async {
    return _characterBox.values.toList();
  }

  Future<void> deleteCharacter(String characterId) async {
    await _characterBox.delete(characterId);
  }

  // 记忆相关操作
  Future<void> saveMemory(Memory memory) async {
    await _memoryBox.put(memory.id, memory);
  }

  Future<Memory?> getMemory(String memoryId) async {
    return _memoryBox.get(memoryId);
  }

  Future<List<Memory>> getCharacterMemories(String characterId, String userId) async {
    return _memoryBox.values
        .where((memory) => memory.characterId == characterId && memory.userId == userId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Future<List<Memory>> getUserMemories(String userId) async {
    return _memoryBox.values
        .where((memory) => memory.userId == userId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Future<List<Memory>> getMemoriesByType(String characterId, String userId, MemoryType type) async {
    return _memoryBox.values
        .where((memory) =>
            memory.characterId == characterId &&
            memory.userId == userId &&
            memory.type == type)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Future<void> deleteMemory(String memoryId) async {
    await _memoryBox.delete(memoryId);
  }

  // 记忆聚类相关操作
  Future<void> saveMemoryCluster(MemoryCluster cluster) async {
    await _memoryClusterBox.put(cluster.id, cluster);
  }

  Future<MemoryCluster?> getMemoryCluster(String clusterId) async {
    return _memoryClusterBox.get(clusterId);
  }

  Future<List<MemoryCluster>> getCharacterMemoryClusters(String characterId, String userId) async {
    return _memoryClusterBox.values
        .where((cluster) => cluster.characterId == characterId && cluster.userId == userId)
        .toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  // 用户档案相关操作
  Future<void> saveUserProfile(UserProfile profile) async {
    await _userProfileBox.put(profile.userId, profile);
  }

  Future<UserProfile?> getUserProfile(String userId) async {
    return _userProfileBox.get(userId);
  }

  Future<void> deleteUserProfile(String userId) async {
    await _userProfileBox.delete(userId);
  }

  // 用户偏好相关操作
  Future<void> saveUserPreference(UserPreference preference) async {
    await _userPreferenceBox.put(preference.id, preference);
  }

  Future<UserPreference?> getUserPreference(String preferenceId) async {
    return _userPreferenceBox.get(preferenceId);
  }

  Future<List<UserPreference>> getUserPreferences(String userId, String characterId) async {
    return _userPreferenceBox.values
        .where((pref) => pref.userId == userId && pref.characterId == characterId && pref.isActive)
        .toList()
      ..sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));
  }

  Future<List<UserPreference>> getUserPreferencesByType(
    String userId,
    String characterId,
    PreferenceType type
  ) async {
    return _userPreferenceBox.values
        .where((pref) =>
            pref.userId == userId &&
            pref.characterId == characterId &&
            pref.type == type &&
            pref.isActive)
        .toList()
      ..sort((a, b) => b.confidence.compareTo(a.confidence));
  }

  Future<void> deleteUserPreference(String preferenceId) async {
    await _userPreferenceBox.delete(preferenceId);
  }

  // 偏好模式相关操作
  Future<void> savePreferencePattern(PreferencePattern pattern) async {
    await _preferencePatternBox.put(pattern.id, pattern);
  }

  Future<List<PreferencePattern>> getPreferencePatterns(String userId, String characterId) async {
    return _preferencePatternBox.values
        .where((pattern) => pattern.userId == userId && pattern.characterId == characterId)
        .toList()
      ..sort((a, b) => b.strength.compareTo(a.strength));
  }

  // 行为分析相关操作
  Future<void> saveUserBehaviorAnalysis(UserBehaviorAnalysis analysis) async {
    await _behaviorAnalysisBox.put(analysis.id, analysis);
  }

  Future<UserBehaviorAnalysis?> getLatestUserBehaviorAnalysis(String userId, String characterId) async {
    final analyses = _behaviorAnalysisBox.values
        .where((analysis) => analysis.userId == userId && analysis.characterId == characterId)
        .toList()
      ..sort((a, b) => b.analysisDate.compareTo(a.analysisDate));

    return analyses.isNotEmpty ? analyses.first : null;
  }

  Future<List<UserBehaviorAnalysis>> getUserBehaviorAnalyses(String userId, String characterId) async {
    return _behaviorAnalysisBox.values
        .where((analysis) => analysis.userId == userId && analysis.characterId == characterId)
        .toList()
      ..sort((a, b) => b.analysisDate.compareTo(a.analysisDate));
  }

  // 对话洞察相关操作
  Future<void> saveConversationInsight(ConversationInsight insight) async {
    await _conversationInsightBox.put(insight.id, insight);
  }

  Future<List<ConversationInsight>> getConversationInsights(String userId, String characterId) async {
    return _conversationInsightBox.values
        .where((insight) => insight.userId == userId && insight.characterId == characterId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Future<List<ConversationInsight>> getUnAppliedInsights(String userId, String characterId) async {
    return _conversationInsightBox.values
        .where((insight) =>
            insight.userId == userId &&
            insight.characterId == characterId &&
            !insight.isApplied)
        .toList()
      ..sort((a, b) => b.confidence.compareTo(a.confidence));
  }

  // 群组对话相关操作
  Future<void> saveGroupConversation(GroupConversation conversation) async {
    await _groupConversationBox.put(conversation.id, conversation);
  }

  Future<GroupConversation?> getGroupConversation(String conversationId) async {
    return _groupConversationBox.get(conversationId);
  }

  Future<List<GroupConversation>> getGroupConversations(String userId) async {
    return _groupConversationBox.values
        .where((conv) => conv.userId == userId)
        .toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<void> deleteGroupConversation(String conversationId) async {
    await _groupConversationBox.delete(conversationId);
    // 同时删除相关的群组消息
    final messages = await getGroupMessages(conversationId);
    for (final message in messages) {
      await _groupMessageBox.delete(message.id);
    }
  }

  // 群组消息相关操作
  Future<void> saveGroupMessage(GroupMessage message) async {
    await _groupMessageBox.put(message.id, message);
  }

  Future<GroupMessage?> getGroupMessage(String messageId) async {
    return _groupMessageBox.get(messageId);
  }

  Future<List<GroupMessage>> getGroupMessages(String groupConversationId, {int? limit}) async {
    var messages = _groupMessageBox.values
        .where((msg) => msg.groupConversationId == groupConversationId)
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    if (limit != null && messages.length > limit) {
      messages = messages.sublist(messages.length - limit);
    }

    return messages;
  }

  Future<void> deleteGroupMessage(String messageId) async {
    await _groupMessageBox.delete(messageId);
  }

  // 角色关系相关操作
  Future<void> saveCharacterRelationship(CharacterRelationship relationship) async {
    await _characterRelationshipBox.put(relationship.id, relationship);
  }

  Future<CharacterRelationship?> getCharacterRelationship(String character1Id, String character2Id, String userId) async {
    return _characterRelationshipBox.values
        .where((rel) =>
            rel.userId == userId &&
            ((rel.character1Id == character1Id && rel.character2Id == character2Id) ||
             (rel.character1Id == character2Id && rel.character2Id == character1Id)))
        .firstOrNull;
  }

  Future<List<CharacterRelationship>> getCharacterRelationships(String characterId, String userId) async {
    return _characterRelationshipBox.values
        .where((rel) =>
            rel.userId == userId &&
            (rel.character1Id == characterId || rel.character2Id == characterId))
        .toList()
      ..sort((a, b) => b.strength.compareTo(a.strength));
  }

  // 群组互动事件相关操作
  Future<void> saveGroupInteractionEvent(GroupInteractionEvent event) async {
    await _groupInteractionEventBox.put(event.id, event);
  }

  Future<List<GroupInteractionEvent>> getGroupInteractionEvents(String groupConversationId) async {
    return _groupInteractionEventBox.values
        .where((event) => event.groupConversationId == groupConversationId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  // 对话动态相关操作
  Future<void> saveConversationDynamics(ConversationDynamics dynamics) async {
    await _conversationDynamicsBox.put(dynamics.groupConversationId, dynamics);
  }

  Future<ConversationDynamics?> getConversationDynamics(String groupConversationId) async {
    return _conversationDynamicsBox.get(groupConversationId);
  }

  // 情感状态相关操作
  Future<void> saveEmotionalState(EmotionalState state) async {
    await _emotionalStateBox.put(state.id, state);
  }

  Future<EmotionalState?> getEmotionalState(String stateId) async {
    return _emotionalStateBox.get(stateId);
  }

  Future<List<EmotionalState>> getEmotionalStates(String userId, String characterId, {int? limit}) async {
    var states = _emotionalStateBox.values
        .where((state) => state.userId == userId && state.characterId == characterId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    if (limit != null && states.length > limit) {
      states = states.sublist(0, limit);
    }

    return states;
  }

  Future<List<EmotionalState>> getEmotionalStatesInRange(
    String userId,
    String characterId,
    DateTime start,
    DateTime end,
  ) async {
    return _emotionalStateBox.values
        .where((state) =>
            state.userId == userId &&
            state.characterId == characterId &&
            state.timestamp.isAfter(start) &&
            state.timestamp.isBefore(end))
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  // 情感模式相关操作
  Future<void> saveEmotionalPattern(EmotionalPattern pattern) async {
    await _emotionalPatternBox.put(pattern.id, pattern);
  }

  Future<List<EmotionalPattern>> getEmotionalPatterns(String userId, String characterId) async {
    return _emotionalPatternBox.values
        .where((pattern) => pattern.userId == userId && pattern.characterId == characterId)
        .toList()
      ..sort((a, b) => b.detectedAt.compareTo(a.detectedAt));
  }

  // 情感洞察相关操作
  Future<void> saveEmotionalInsight(EmotionalInsight insight) async {
    await _emotionalInsightBox.put(insight.id, insight);
  }

  Future<List<EmotionalInsight>> getEmotionalInsights(String userId, String characterId) async {
    return _emotionalInsightBox.values
        .where((insight) => insight.userId == userId && insight.characterId == characterId)
        .toList()
      ..sort((a, b) => b.generatedAt.compareTo(a.generatedAt));
  }

  // 情绪摘要相关操作
  Future<void> saveMoodSummary(MoodSummary summary) async {
    await _moodSummaryBox.put(summary.id, summary);
  }

  Future<MoodSummary?> getLatestMoodSummary(String userId, String characterId) async {
    final summaries = _moodSummaryBox.values
        .where((summary) => summary.userId == userId && summary.characterId == characterId)
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));

    return summaries.isNotEmpty ? summaries.first : null;
  }

  Future<List<MoodSummary>> getMoodSummaries(String userId, String characterId, {int? limit}) async {
    var summaries = _moodSummaryBox.values
        .where((summary) => summary.userId == userId && summary.characterId == characterId)
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));

    if (limit != null && summaries.length > limit) {
      summaries = summaries.sublist(0, limit);
    }

    return summaries;
  }

  // 情感触发器相关操作
  Future<void> saveEmotionalTrigger(EmotionalTrigger trigger) async {
    await _emotionalTriggerBox.put(trigger.id, trigger);
  }

  Future<EmotionalTrigger?> getEmotionalTrigger(
    String userId,
    String characterId,
    String trigger,
    TriggerType type,
  ) async {
    return _emotionalTriggerBox.values
        .where((t) =>
            t.userId == userId &&
            t.characterId == characterId &&
            t.trigger == trigger &&
            t.type == type)
        .firstOrNull;
  }

  Future<List<EmotionalTrigger>> getEmotionalTriggers(String userId, String characterId) async {
    return _emotionalTriggerBox.values
        .where((trigger) => trigger.userId == userId && trigger.characterId == characterId)
        .toList()
      ..sort((a, b) => b.strength.compareTo(a.strength));
  }

  // 清理所有数据
  Future<void> clearAllData() async {
    await _messageBox.clear();
    await _conversationBox.clear();
    await _characterBox.clear();
    await _memoryBox.clear();
    await _memoryClusterBox.clear();
    await _userProfileBox.clear();
    await _userPreferenceBox.clear();
    await _preferencePatternBox.clear();
    await _behaviorAnalysisBox.clear();
    await _conversationInsightBox.clear();
    await _groupConversationBox.clear();
    await _groupMessageBox.clear();
    await _characterRelationshipBox.clear();
    await _groupInteractionEventBox.clear();
    await _conversationDynamicsBox.clear();
    await _emotionalStateBox.clear();
    await _emotionalPatternBox.clear();
    await _emotionalInsightBox.clear();
    await _moodSummaryBox.clear();
    await _emotionalTriggerBox.clear();
    await _initializeDefaultCharacters();
  }

  // 关闭数据库
  Future<void> close() async {
    await _messageBox.close();
    await _conversationBox.close();
    await _characterBox.close();
    await _memoryBox.close();
    await _memoryClusterBox.close();
    await _userProfileBox.close();
    await _userPreferenceBox.close();
    await _preferencePatternBox.close();
    await _behaviorAnalysisBox.close();
    await _conversationInsightBox.close();
    await _groupConversationBox.close();
    await _groupMessageBox.close();
    await _characterRelationshipBox.close();
    await _groupInteractionEventBox.close();
    await _conversationDynamicsBox.close();
    await _emotionalStateBox.close();
    await _emotionalPatternBox.close();
    await _emotionalInsightBox.close();
    await _moodSummaryBox.close();
    await _emotionalTriggerBox.close();
  }
}

// Provider
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});
