import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/models.dart';

class StorageService {
  static const String _messagesBox = 'messages';
  static const String _conversationsBox = 'conversations';
  static const String _charactersBox = 'characters';
  static const String _memoriesBox = 'memories';
  static const String _memoryClustersBox = 'memory_clusters';
  static const String _userProfilesBox = 'user_profiles';
  static const String _userPreferencesBox = 'user_preferences';
  static const String _preferencePatternsBox = 'preference_patterns';
  static const String _behaviorAnalysisBox = 'behavior_analysis';
  static const String _conversationInsightsBox = 'conversation_insights';
  static const String _groupConversationsBox = 'group_conversations';
  static const String _groupMessagesBox = 'group_messages';
  static const String _characterRelationshipsBox = 'character_relationships';
  static const String _groupInteractionEventsBox = 'group_interaction_events';
  static const String _conversationDynamicsBox = 'conversation_dynamics';
  static const String _emotionalStatesBox = 'emotional_states';
  static const String _emotionalPatternsBox = 'emotional_patterns';
  static const String _emotionalInsightsBox = 'emotional_insights';
  static const String _moodSummariesBox = 'mood_summaries';
  static const String _emotionalTriggersBox = 'emotional_triggers';
  static const String _interactionAnalysesBox = 'interaction_analyses';
  static const String _interactionReportsBox = 'interaction_reports';
  static const String _roleTemplatesBox = 'role_templates';
  static const String _roleCollectionsBox = 'role_collections';
  static const String _roleInteractionRulesBox = 'role_interaction_rules';
  static const String _roleSchedulesBox = 'role_schedules';
  static const String _roleCustomizationsBox = 'role_customizations';
  static const String _roleConflictsBox = 'role_conflicts';
  static const String _roleRecommendationsBox = 'role_recommendations';
  static const String _notificationsBox = 'notifications';
  static const String _notificationSettingsBox = 'notification_settings';
  static const String _notificationTemplatesBox = 'notification_templates';
  static const String _notificationRulesBox = 'notification_rules';
  static const String _notificationAnalyticsBox = 'notification_analytics';
  static const String _smartSuggestionsBox = 'smart_suggestions';
  static const String _unlockableContentBox = 'unlockable_content';
  static const String _contentUnlocksBox = 'content_unlocks';
  static const String _unlockProgressBox = 'unlock_progress';
  static const String _contentRecommendationsBox = 'content_recommendations';
  static const String _unlockEventsBox = 'unlock_events';
  static const String _unlockNotificationsBox = 'unlock_notifications';
  static const String _contentCollectionsBox = 'content_collections';
  static const String _interactionContextsBox = 'interaction_contexts';
  static const String _interactionEventsBox = 'interaction_events';
  static const String _characterReactionsBox = 'character_reactions';
  static const String _interactionRulesBox = 'interaction_rules';
  static const String _interactionPatternsBox = 'interaction_patterns';
  static const String _characterRelationshipsBox = 'character_relationships';
  static const String _interactionAnalyticsBox = 'interaction_analytics';
  static const String _groupConversationsBox = 'group_conversations';
  static const String _groupEventsBox = 'group_events';
  static const String _conversationScenariosBox = 'conversation_scenarios';
  static const String _scenarioExecutionsBox = 'scenario_executions';
  static const String _executionEventsBox = 'execution_events';
  static const String _groupAnalyticsBox = 'group_analytics';
  static const String _conversationModeratorsBox = 'conversation_moderators';

  late Box<Message> _messageBox;
  late Box<Conversation> _conversationBox;
  late Box<Character> _characterBox;
  late Box<Memory> _memoryBox;
  late Box<MemoryCluster> _memoryClusterBox;
  late Box<UserProfile> _userProfileBox;
  late Box<UserPreference> _userPreferenceBox;
  late Box<PreferencePattern> _preferencePatternBox;
  late Box<UserBehaviorAnalysis> _behaviorAnalysisBox;
  late Box<ConversationInsight> _conversationInsightBox;
  late Box<GroupConversation> _groupConversationBox;
  late Box<GroupMessage> _groupMessageBox;
  late Box<CharacterRelationship> _characterRelationshipBox;
  late Box<GroupInteractionEvent> _groupInteractionEventBox;
  late Box<ConversationDynamics> _conversationDynamicsBox;
  late Box<EmotionalState> _emotionalStateBox;
  late Box<EmotionalPattern> _emotionalPatternBox;
  late Box<EmotionalInsight> _emotionalInsightBox;
  late Box<MoodSummary> _moodSummaryBox;
  late Box<EmotionalTrigger> _emotionalTriggerBox;
  late Box<InteractionAnalysis> _interactionAnalysisBox;
  late Box<InteractionReport> _interactionReportBox;
  late Box<RoleTemplate> _roleTemplateBox;
  late Box<RoleCollection> _roleCollectionBox;
  late Box<RoleInteractionRule> _roleInteractionRuleBox;
  late Box<RoleSchedule> _roleScheduleBox;
  late Box<RoleCustomization> _roleCustomizationBox;
  late Box<RoleConflict> _roleConflictBox;
  late Box<RoleRecommendation> _roleRecommendationBox;
  late Box<AppNotification> _notificationBox;
  late Box<NotificationSettings> _notificationSettingsBox;
  late Box<NotificationTemplate> _notificationTemplateBox;
  late Box<NotificationRule> _notificationRuleBox;
  late Box<NotificationAnalytics> _notificationAnalyticsBox;
  late Box<SmartNotificationSuggestion> _smartSuggestionBox;
  late Box<UnlockableContent> _unlockableContentBox;
  late Box<ContentUnlock> _contentUnlockBox;
  late Box<UnlockProgress> _unlockProgressBox;
  late Box<ContentRecommendation> _contentRecommendationBox;
  late Box<UnlockEvent> _unlockEventBox;
  late Box<UnlockNotification> _unlockNotificationBox;
  late Box<ContentCollection> _contentCollectionBox;
  late Box<InteractionContext> _interactionContextBox;
  late Box<InteractionEvent> _interactionEventBox;
  late Box<CharacterReaction> _characterReactionBox;
  late Box<InteractionRule> _interactionRuleBox;
  late Box<InteractionPattern> _interactionPatternBox;
  late Box<CharacterRelationship> _characterRelationshipBox;
  late Box<InteractionAnalytics> _interactionAnalyticsBox;
  late Box<GroupConversation> _groupConversationBox;
  late Box<GroupEvent> _groupEventBox;
  late Box<ConversationScenario> _conversationScenarioBox;
  late Box<ScenarioExecution> _scenarioExecutionBox;
  late Box<ExecutionEvent> _executionEventBox;
  late Box<GroupAnalytics> _groupAnalyticsBox;
  late Box<ConversationModerator> _conversationModeratorBox;

  // 初始化存储
  Future<void> initialize() async {
    await Hive.initFlutter();
    
    // 注册适配器
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(MessageAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(MessageTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(MessageSenderAdapter());
    }
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(MessageStatusAdapter());
    }
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(CharacterAdapter());
    }
    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(PersonalityTraitsAdapter());
    }
    if (!Hive.isAdapterRegistered(6)) {
      Hive.registerAdapter(CharacterGenderAdapter());
    }
    if (!Hive.isAdapterRegistered(7)) {
      Hive.registerAdapter(ConversationAdapter());
    }
    if (!Hive.isAdapterRegistered(8)) {
      Hive.registerAdapter(ConversationSummaryAdapter());
    }
    if (!Hive.isAdapterRegistered(9)) {
      Hive.registerAdapter(MemoryAdapter());
    }
    if (!Hive.isAdapterRegistered(10)) {
      Hive.registerAdapter(MemoryTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(11)) {
      Hive.registerAdapter(MemoryClusterAdapter());
    }
    if (!Hive.isAdapterRegistered(12)) {
      Hive.registerAdapter(UserProfileAdapter());
    }
    if (!Hive.isAdapterRegistered(13)) {
      Hive.registerAdapter(UserPreferenceAdapter());
    }
    if (!Hive.isAdapterRegistered(14)) {
      Hive.registerAdapter(PreferenceTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(15)) {
      Hive.registerAdapter(PreferencePatternAdapter());
    }
    if (!Hive.isAdapterRegistered(16)) {
      Hive.registerAdapter(UserBehaviorAnalysisAdapter());
    }
    if (!Hive.isAdapterRegistered(17)) {
      Hive.registerAdapter(ConversationInsightAdapter());
    }
    if (!Hive.isAdapterRegistered(18)) {
      Hive.registerAdapter(InsightTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(19)) {
      Hive.registerAdapter(GroupConversationAdapter());
    }
    if (!Hive.isAdapterRegistered(20)) {
      Hive.registerAdapter(GroupConversationTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(21)) {
      Hive.registerAdapter(GroupMessageAdapter());
    }
    if (!Hive.isAdapterRegistered(22)) {
      Hive.registerAdapter(CharacterRelationshipAdapter());
    }
    if (!Hive.isAdapterRegistered(23)) {
      Hive.registerAdapter(RelationshipTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(24)) {
      Hive.registerAdapter(GroupInteractionEventAdapter());
    }
    if (!Hive.isAdapterRegistered(25)) {
      Hive.registerAdapter(InteractionEventTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(26)) {
      Hive.registerAdapter(ConversationDynamicsAdapter());
    }
    if (!Hive.isAdapterRegistered(27)) {
      Hive.registerAdapter(EmotionalStateAdapter());
    }
    if (!Hive.isAdapterRegistered(28)) {
      Hive.registerAdapter(EmotionTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(29)) {
      Hive.registerAdapter(EmotionIntensityAdapter());
    }
    if (!Hive.isAdapterRegistered(30)) {
      Hive.registerAdapter(EmotionalPatternAdapter());
    }
    if (!Hive.isAdapterRegistered(31)) {
      Hive.registerAdapter(PatternTypeAdapter());
    }
    if (!Hive.isAdapterRegistered(32)) {
      Hive.registerAdapter(EmotionalInsightAdapter());
    }
    if (!Hive.isAdapterRegistered(33)) {
      Hive.registerAdapter(InsightCategoryAdapter());
    }
    if (!Hive.isAdapterRegistered(34)) {
      Hive.registerAdapter(MoodSummaryAdapter());
    }
    if (!Hive.isAdapterRegistered(35)) {
      Hive.registerAdapter(EmotionalTriggerAdapter());
    }
    if (!Hive.isAdapterRegistered(36)) {
      Hive.registerAdapter(TriggerTypeAdapter());
    }
    // 注册互动分析相关的适配器
    for (int i = 37; i <= 167; i++) {
      if (!Hive.isAdapterRegistered(i)) {
        switch (i) {
          case 37: Hive.registerAdapter(InteractionAnalysisAdapter()); break;
          case 38: Hive.registerAdapter(AnalysisPeriodAdapter()); break;
          case 39: Hive.registerAdapter(InteractionMetricsAdapter()); break;
          case 40: Hive.registerAdapter(CommunicationPatternsAdapter()); break;
          case 41: Hive.registerAdapter(CommunicationStyleAdapter()); break;
          case 42: Hive.registerAdapter(ConversationThemeAdapter()); break;
          case 43: Hive.registerAdapter(RelationshipInsightsAdapter()); break;
          case 44: Hive.registerAdapter(RelationshipStageAdapter()); break;
          case 45: Hive.registerAdapter(RelationshipMilestoneAdapter()); break;
          case 46: Hive.registerAdapter(MilestoneTypeAdapter()); break;
          case 47: Hive.registerAdapter(InteractionTrendAdapter()); break;
          case 48: Hive.registerAdapter(TrendDirectionAdapter()); break;
          case 49: Hive.registerAdapter(TrendSignificanceAdapter()); break;
          case 50: Hive.registerAdapter(DataPointAdapter()); break;
          case 51: Hive.registerAdapter(InteractionReportAdapter()); break;
          case 52: Hive.registerAdapter(ReportTypeAdapter()); break;
          case 53: Hive.registerAdapter(ReportSectionAdapter()); break;
          case 54: Hive.registerAdapter(SectionTypeAdapter()); break;
          // 角色管理相关适配器
          case 55: Hive.registerAdapter(RoleTemplateAdapter()); break;
          case 56: Hive.registerAdapter(RoleArchetypeAdapter()); break;
          case 57: Hive.registerAdapter(RoleCollectionAdapter()); break;
          case 58: Hive.registerAdapter(RoleInteractionRuleAdapter()); break;
          case 59: Hive.registerAdapter(InteractionTypeAdapter()); break;
          case 60: Hive.registerAdapter(RoleScheduleAdapter()); break;
          case 61: Hive.registerAdapter(ScheduleTypeAdapter()); break;
          case 62: Hive.registerAdapter(RolePresetAdapter()); break;
          case 63: Hive.registerAdapter(RoleAnalyticsAdapter()); break;
          case 64: Hive.registerAdapter(RoleCustomizationAdapter()); break;
          case 65: Hive.registerAdapter(CustomizationTypeAdapter()); break;
          case 66: Hive.registerAdapter(RoleEvolutionAdapter()); break;
          case 67: Hive.registerAdapter(EvolutionTriggerAdapter()); break;
          case 68: Hive.registerAdapter(RoleConflictAdapter()); break;
          case 69: Hive.registerAdapter(ConflictTypeAdapter()); break;
          case 70: Hive.registerAdapter(ConflictSeverityAdapter()); break;
          case 71: Hive.registerAdapter(ConflictStatusAdapter()); break;
          case 72: Hive.registerAdapter(RoleRecommendationAdapter()); break;
          case 73: Hive.registerAdapter(RecommendationTypeAdapter()); break;
          // 通知系统相关适配器
          case 74: Hive.registerAdapter(AppNotificationAdapter()); break;
          case 75: Hive.registerAdapter(NotificationTypeAdapter()); break;
          case 76: Hive.registerAdapter(NotificationPriorityAdapter()); break;
          case 77: Hive.registerAdapter(NotificationActionAdapter()); break;
          case 78: Hive.registerAdapter(ActionTypeAdapter()); break;
          case 79: Hive.registerAdapter(NotificationSettingsAdapter()); break;
          case 80: Hive.registerAdapter(QuietHoursAdapter()); break;
          case 81: Hive.registerAdapter(TimeOfDayAdapter()); break;
          case 82: Hive.registerAdapter(NotificationTemplateAdapter()); break;
          case 83: Hive.registerAdapter(NotificationRuleAdapter()); break;
          case 84: Hive.registerAdapter(RuleTriggerAdapter()); break;
          case 85: Hive.registerAdapter(TriggerTypeAdapter()); break;
          case 86: Hive.registerAdapter(RuleConditionAdapter()); break;
          case 87: Hive.registerAdapter(ConditionOperatorAdapter()); break;
          case 88: Hive.registerAdapter(LogicalOperatorAdapter()); break;
          case 89: Hive.registerAdapter(RuleActionAdapter()); break;
          case 90: Hive.registerAdapter(RuleActionTypeAdapter()); break;
          case 91: Hive.registerAdapter(NotificationAnalyticsAdapter()); break;
          case 92: Hive.registerAdapter(NotificationBatchAdapter()); break;
          case 93: Hive.registerAdapter(SmartNotificationSuggestionAdapter()); break;
          case 94: Hive.registerAdapter(SuggestionTypeAdapter()); break;
          // 内容解锁相关适配器
          case 95: Hive.registerAdapter(UnlockableContentAdapter()); break;
          case 96: Hive.registerAdapter(ContentTypeAdapter()); break;
          case 97: Hive.registerAdapter(ContentRarityAdapter()); break;
          case 98: Hive.registerAdapter(UnlockConditionAdapter()); break;
          case 99: Hive.registerAdapter(ConditionTypeAdapter()); break;
          case 100: Hive.registerAdapter(ContentUnlockAdapter()); break;
          case 101: Hive.registerAdapter(UnlockTriggerAdapter()); break;
          case 102: Hive.registerAdapter(TriggerTypeAdapter()); break;
          case 103: Hive.registerAdapter(ContentCollectionAdapter()); break;
          case 104: Hive.registerAdapter(CollectionTypeAdapter()); break;
          case 105: Hive.registerAdapter(UnlockProgressAdapter()); break;
          case 106: Hive.registerAdapter(ContentRecommendationAdapter()); break;
          case 107: Hive.registerAdapter(RecommendationReasonAdapter()); break;
          case 108: Hive.registerAdapter(UnlockEventAdapter()); break;
          case 109: Hive.registerAdapter(EventTypeAdapter()); break;
          case 110: Hive.registerAdapter(ContentAnalyticsAdapter()); break;
          case 111: Hive.registerAdapter(UnlockNotificationAdapter()); break;
          case 112: Hive.registerAdapter(NotificationTypeAdapter()); break;
          case 113: Hive.registerAdapter(ContentFilterAdapter()); break;
          case 114: Hive.registerAdapter(UnlockStatusAdapter()); break;
          case 115: Hive.registerAdapter(ViewStatusAdapter()); break;
          case 116: Hive.registerAdapter(SortByAdapter()); break;
          case 117: Hive.registerAdapter(SortOrderAdapter()); break;
          // 角色互动相关适配器
          case 118: Hive.registerAdapter(InteractionContextAdapter()); break;
          case 119: Hive.registerAdapter(InteractionTypeAdapter()); break;
          case 120: Hive.registerAdapter(InteractionStatusAdapter()); break;
          case 121: Hive.registerAdapter(InteractionEventAdapter()); break;
          case 122: Hive.registerAdapter(EventTypeAdapter()); break;
          case 123: Hive.registerAdapter(EventPriorityAdapter()); break;
          case 124: Hive.registerAdapter(CharacterReactionAdapter()); break;
          case 125: Hive.registerAdapter(ReactionTypeAdapter()); break;
          case 126: Hive.registerAdapter(ReactionTimingAdapter()); break;
          case 127: Hive.registerAdapter(InteractionRuleAdapter()); break;
          case 128: Hive.registerAdapter(RuleTypeAdapter()); break;
          case 129: Hive.registerAdapter(RuleConditionAdapter()); break;
          case 130: Hive.registerAdapter(ConditionTypeAdapter()); break;
          case 131: Hive.registerAdapter(ConditionOperatorAdapter()); break;
          case 132: Hive.registerAdapter(RuleActionAdapter()); break;
          case 133: Hive.registerAdapter(ActionTypeAdapter()); break;
          case 134: Hive.registerAdapter(InteractionPatternAdapter()); break;
          case 135: Hive.registerAdapter(PatternTypeAdapter()); break;
          case 136: Hive.registerAdapter(PatternStepAdapter()); break;
          case 137: Hive.registerAdapter(StepTypeAdapter()); break;
          case 138: Hive.registerAdapter(InteractionAnalyticsAdapter()); break;
          case 139: Hive.registerAdapter(CharacterRelationshipAdapter()); break;
          case 140: Hive.registerAdapter(RelationshipTypeAdapter()); break;
          case 141: Hive.registerAdapter(RelationshipStatusAdapter()); break;
          // 群组对话相关适配器
          case 142: Hive.registerAdapter(GroupConversationAdapter()); break;
          case 143: Hive.registerAdapter(GroupTypeAdapter()); break;
          case 144: Hive.registerAdapter(GroupStatusAdapter()); break;
          case 145: Hive.registerAdapter(GroupRoleAdapter()); break;
          case 146: Hive.registerAdapter(GroupSettingsAdapter()); break;
          case 147: Hive.registerAdapter(MessageFilterAdapter()); break;
          case 148: Hive.registerAdapter(ConversationThemeAdapter()); break;
          case 149: Hive.registerAdapter(GroupEventAdapter()); break;
          case 150: Hive.registerAdapter(GroupEventTypeAdapter()); break;
          case 151: Hive.registerAdapter(ConversationScenarioAdapter()); break;
          case 152: Hive.registerAdapter(ScenarioTypeAdapter()); break;
          case 153: Hive.registerAdapter(ScenarioStepAdapter()); break;
          case 154: Hive.registerAdapter(StepTypeAdapter()); break;
          case 155: Hive.registerAdapter(StepBranchAdapter()); break;
          case 156: Hive.registerAdapter(ScenarioTriggerAdapter()); break;
          case 157: Hive.registerAdapter(TriggerTypeAdapter()); break;
          case 158: Hive.registerAdapter(ScenarioExecutionAdapter()); break;
          case 159: Hive.registerAdapter(ExecutionStatusAdapter()); break;
          case 160: Hive.registerAdapter(ExecutionEventAdapter()); break;
          case 161: Hive.registerAdapter(ExecutionEventTypeAdapter()); break;
          case 162: Hive.registerAdapter(GroupAnalyticsAdapter()); break;
          case 163: Hive.registerAdapter(ConversationModeratorAdapter()); break;
          case 164: Hive.registerAdapter(ModeratorTypeAdapter()); break;
          case 165: Hive.registerAdapter(ModerationRuleAdapter()); break;
          case 166: Hive.registerAdapter(RuleTypeAdapter()); break;
          case 167: Hive.registerAdapter(ModerationActionAdapter()); break;
        }
      }
    }

    // 打开数据库
    _messageBox = await Hive.openBox<Message>(_messagesBox);
    _conversationBox = await Hive.openBox<Conversation>(_conversationsBox);
    _characterBox = await Hive.openBox<Character>(_charactersBox);
    _memoryBox = await Hive.openBox<Memory>(_memoriesBox);
    _memoryClusterBox = await Hive.openBox<MemoryCluster>(_memoryClustersBox);
    _userProfileBox = await Hive.openBox<UserProfile>(_userProfilesBox);
    _userPreferenceBox = await Hive.openBox<UserPreference>(_userPreferencesBox);
    _preferencePatternBox = await Hive.openBox<PreferencePattern>(_preferencePatternsBox);
    _behaviorAnalysisBox = await Hive.openBox<UserBehaviorAnalysis>(_behaviorAnalysisBox);
    _conversationInsightBox = await Hive.openBox<ConversationInsight>(_conversationInsightsBox);
    _groupConversationBox = await Hive.openBox<GroupConversation>(_groupConversationsBox);
    _groupMessageBox = await Hive.openBox<GroupMessage>(_groupMessagesBox);
    _characterRelationshipBox = await Hive.openBox<CharacterRelationship>(_characterRelationshipsBox);
    _groupInteractionEventBox = await Hive.openBox<GroupInteractionEvent>(_groupInteractionEventsBox);
    _conversationDynamicsBox = await Hive.openBox<ConversationDynamics>(_conversationDynamicsBox);
    _emotionalStateBox = await Hive.openBox<EmotionalState>(_emotionalStatesBox);
    _emotionalPatternBox = await Hive.openBox<EmotionalPattern>(_emotionalPatternsBox);
    _emotionalInsightBox = await Hive.openBox<EmotionalInsight>(_emotionalInsightsBox);
    _moodSummaryBox = await Hive.openBox<MoodSummary>(_moodSummariesBox);
    _emotionalTriggerBox = await Hive.openBox<EmotionalTrigger>(_emotionalTriggersBox);
    _interactionAnalysisBox = await Hive.openBox<InteractionAnalysis>(_interactionAnalysesBox);
    _interactionReportBox = await Hive.openBox<InteractionReport>(_interactionReportsBox);
    _roleTemplateBox = await Hive.openBox<RoleTemplate>(_roleTemplatesBox);
    _roleCollectionBox = await Hive.openBox<RoleCollection>(_roleCollectionsBox);
    _roleInteractionRuleBox = await Hive.openBox<RoleInteractionRule>(_roleInteractionRulesBox);
    _roleScheduleBox = await Hive.openBox<RoleSchedule>(_roleSchedulesBox);
    _roleCustomizationBox = await Hive.openBox<RoleCustomization>(_roleCustomizationsBox);
    _roleConflictBox = await Hive.openBox<RoleConflict>(_roleConflictsBox);
    _roleRecommendationBox = await Hive.openBox<RoleRecommendation>(_roleRecommendationsBox);
    _notificationBox = await Hive.openBox<AppNotification>(_notificationsBox);
    _notificationSettingsBox = await Hive.openBox<NotificationSettings>(_notificationSettingsBox);
    _notificationTemplateBox = await Hive.openBox<NotificationTemplate>(_notificationTemplatesBox);
    _notificationRuleBox = await Hive.openBox<NotificationRule>(_notificationRulesBox);
    _notificationAnalyticsBox = await Hive.openBox<NotificationAnalytics>(_notificationAnalyticsBox);
    _smartSuggestionBox = await Hive.openBox<SmartNotificationSuggestion>(_smartSuggestionsBox);
    _unlockableContentBox = await Hive.openBox<UnlockableContent>(_unlockableContentBox);
    _contentUnlockBox = await Hive.openBox<ContentUnlock>(_contentUnlocksBox);
    _unlockProgressBox = await Hive.openBox<UnlockProgress>(_unlockProgressBox);
    _contentRecommendationBox = await Hive.openBox<ContentRecommendation>(_contentRecommendationsBox);
    _unlockEventBox = await Hive.openBox<UnlockEvent>(_unlockEventsBox);
    _unlockNotificationBox = await Hive.openBox<UnlockNotification>(_unlockNotificationsBox);
    _contentCollectionBox = await Hive.openBox<ContentCollection>(_contentCollectionsBox);
    _interactionContextBox = await Hive.openBox<InteractionContext>(_interactionContextsBox);
    _interactionEventBox = await Hive.openBox<InteractionEvent>(_interactionEventsBox);
    _characterReactionBox = await Hive.openBox<CharacterReaction>(_characterReactionsBox);
    _interactionRuleBox = await Hive.openBox<InteractionRule>(_interactionRulesBox);
    _interactionPatternBox = await Hive.openBox<InteractionPattern>(_interactionPatternsBox);
    _characterRelationshipBox = await Hive.openBox<CharacterRelationship>(_characterRelationshipsBox);
    _interactionAnalyticsBox = await Hive.openBox<InteractionAnalytics>(_interactionAnalyticsBox);
    _groupConversationBox = await Hive.openBox<GroupConversation>(_groupConversationsBox);
    _groupEventBox = await Hive.openBox<GroupEvent>(_groupEventsBox);
    _conversationScenarioBox = await Hive.openBox<ConversationScenario>(_conversationScenariosBox);
    _scenarioExecutionBox = await Hive.openBox<ScenarioExecution>(_scenarioExecutionsBox);
    _executionEventBox = await Hive.openBox<ExecutionEvent>(_executionEventsBox);
    _groupAnalyticsBox = await Hive.openBox<GroupAnalytics>(_groupAnalyticsBox);
    _conversationModeratorBox = await Hive.openBox<ConversationModerator>(_conversationModeratorsBox);
    _groupConversationBox = await Hive.openBox<GroupConversation>(_groupConversationsBox);
    _groupEventBox = await Hive.openBox<GroupEvent>(_groupEventsBox);
    _conversationScenarioBox = await Hive.openBox<ConversationScenario>(_conversationScenariosBox);
    _scenarioExecutionBox = await Hive.openBox<ScenarioExecution>(_scenarioExecutionsBox);
    _executionEventBox = await Hive.openBox<ExecutionEvent>(_executionEventsBox);
    _groupAnalyticsBox = await Hive.openBox<GroupAnalytics>(_groupAnalyticsBox);
    _conversationModeratorBox = await Hive.openBox<ConversationModerator>(_conversationModeratorsBox);

    // 初始化默认角色
    await _initializeDefaultCharacters();
  }

  // 初始化默认角色
  Future<void> _initializeDefaultCharacters() async {
    if (_characterBox.isEmpty) {
      final defaultCharacters = [
        Character(
          id: '1',
          name: '小雨',
          description: '温柔体贴的邻家女孩，喜欢文学和音乐',
          avatarUrl: '',
          personality: const PersonalityTraits(
            extroversion: 0.3,
            agreeableness: 0.8,
            conscientiousness: 0.7,
            neuroticism: 0.4,
            openness: 0.6,
            humor: 0.5,
            empathy: 0.9,
            creativity: 0.7,
          ),
          interests: ['文学', '音乐', '绘画', '咖啡'],
          gender: CharacterGender.female,
          age: 22,
          occupation: '大学生',
          traits: ['温柔', '文艺', '善解人意'],
          speakingStyle: 'gentle',
        ),
        Character(
          id: '2',
          name: '阿凯',
          description: '阳光开朗的运动男孩，充满正能量',
          avatarUrl: '',
          personality: const PersonalityTraits(
            extroversion: 0.8,
            agreeableness: 0.7,
            conscientiousness: 0.6,
            neuroticism: 0.2,
            openness: 0.7,
            humor: 0.8,
            empathy: 0.6,
            creativity: 0.5,
          ),
          interests: ['运动', '健身', '旅行', '音乐'],
          gender: CharacterGender.male,
          age: 25,
          occupation: '健身教练',
          traits: ['阳光', '运动', '幽默'],
          speakingStyle: 'energetic',
        ),
        Character(
          id: '3',
          name: '小雪',
          description: '知性优雅的职场女性，理性而独立',
          avatarUrl: '',
          personality: const PersonalityTraits(
            extroversion: 0.5,
            agreeableness: 0.6,
            conscientiousness: 0.9,
            neuroticism: 0.3,
            openness: 0.8,
            humor: 0.4,
            empathy: 0.7,
            creativity: 0.6,
          ),
          interests: ['商业', '投资', '读书', '红酒'],
          gender: CharacterGender.female,
          age: 28,
          occupation: '产品经理',
          traits: ['知性', '独立', '理性'],
          speakingStyle: 'professional',
        ),
        Character(
          id: '4',
          name: '小明',
          description: '技术宅男，对科技和游戏充满热情',
          avatarUrl: '',
          personality: const PersonalityTraits(
            extroversion: 0.4,
            agreeableness: 0.6,
            conscientiousness: 0.8,
            neuroticism: 0.3,
            openness: 0.9,
            humor: 0.6,
            empathy: 0.5,
            creativity: 0.8,
          ),
          interests: ['编程', '游戏', '科技', '动漫'],
          gender: CharacterGender.male,
          age: 24,
          occupation: '软件工程师',
          traits: ['技术', '游戏', '创新'],
          speakingStyle: 'technical',
        ),
      ];

      for (final character in defaultCharacters) {
        await _characterBox.put(character.id, character);
      }
    }
  }

  // 消息相关操作
  Future<void> saveMessage(Message message) async {
    await _messageBox.put(message.id, message);
  }

  Future<Message?> getMessage(String messageId) async {
    return _messageBox.get(messageId);
  }

  Future<List<Message>> getConversationMessages(String conversationId) async {
    return _messageBox.values
        .where((message) => message.conversationId == conversationId)
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  Future<void> deleteMessage(String messageId) async {
    await _messageBox.delete(messageId);
  }

  // 对话相关操作
  Future<void> saveConversation(Conversation conversation) async {
    await _conversationBox.put(conversation.id, conversation);
  }

  Future<Conversation?> getConversation(String conversationId) async {
    return _conversationBox.get(conversationId);
  }

  Future<List<Conversation>> getCharacterConversations(String characterId) async {
    return _conversationBox.values
        .where((conversation) => conversation.characterId == characterId)
        .toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<List<Conversation>> getAllConversations() async {
    return _conversationBox.values.toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<void> deleteConversation(String conversationId) async {
    // 删除对话相关的所有消息
    final messages = await getConversationMessages(conversationId);
    for (final message in messages) {
      await deleteMessage(message.id);
    }
    // 删除对话
    await _conversationBox.delete(conversationId);
  }

  Future<void> clearConversationMessages(String conversationId) async {
    final messages = await getConversationMessages(conversationId);
    for (final message in messages) {
      await deleteMessage(message.id);
    }
    
    // 重置对话状态
    final conversation = await getConversation(conversationId);
    if (conversation != null) {
      final clearedConversation = conversation.copyWith(
        messageIds: [],
        messageCount: 0,
        lastMessageId: null,
        lastMessagePreview: null,
        unreadCount: 0,
        updatedAt: DateTime.now(),
      );
      await saveConversation(clearedConversation);
    }
  }

  // 角色相关操作
  Future<void> saveCharacter(Character character) async {
    await _characterBox.put(character.id, character);
  }

  Future<Character?> getCharacter(String characterId) async {
    return _characterBox.get(characterId);
  }

  Future<List<Character>> getAllCharacters() async {
    return _characterBox.values.toList();
  }

  Future<void> deleteCharacter(String characterId) async {
    await _characterBox.delete(characterId);
  }

  // 记忆相关操作
  Future<void> saveMemory(Memory memory) async {
    await _memoryBox.put(memory.id, memory);
  }

  Future<Memory?> getMemory(String memoryId) async {
    return _memoryBox.get(memoryId);
  }

  Future<List<Memory>> getCharacterMemories(String characterId, String userId) async {
    return _memoryBox.values
        .where((memory) => memory.characterId == characterId && memory.userId == userId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Future<List<Memory>> getUserMemories(String userId) async {
    return _memoryBox.values
        .where((memory) => memory.userId == userId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Future<List<Memory>> getMemoriesByType(String characterId, String userId, MemoryType type) async {
    return _memoryBox.values
        .where((memory) =>
            memory.characterId == characterId &&
            memory.userId == userId &&
            memory.type == type)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Future<void> deleteMemory(String memoryId) async {
    await _memoryBox.delete(memoryId);
  }

  // 记忆聚类相关操作
  Future<void> saveMemoryCluster(MemoryCluster cluster) async {
    await _memoryClusterBox.put(cluster.id, cluster);
  }

  Future<MemoryCluster?> getMemoryCluster(String clusterId) async {
    return _memoryClusterBox.get(clusterId);
  }

  Future<List<MemoryCluster>> getCharacterMemoryClusters(String characterId, String userId) async {
    return _memoryClusterBox.values
        .where((cluster) => cluster.characterId == characterId && cluster.userId == userId)
        .toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  // 用户档案相关操作
  Future<void> saveUserProfile(UserProfile profile) async {
    await _userProfileBox.put(profile.userId, profile);
  }

  Future<UserProfile?> getUserProfile(String userId) async {
    return _userProfileBox.get(userId);
  }

  Future<void> deleteUserProfile(String userId) async {
    await _userProfileBox.delete(userId);
  }

  // 用户偏好相关操作
  Future<void> saveUserPreference(UserPreference preference) async {
    await _userPreferenceBox.put(preference.id, preference);
  }

  Future<UserPreference?> getUserPreference(String preferenceId) async {
    return _userPreferenceBox.get(preferenceId);
  }

  Future<List<UserPreference>> getUserPreferences(String userId, String characterId) async {
    return _userPreferenceBox.values
        .where((pref) => pref.userId == userId && pref.characterId == characterId && pref.isActive)
        .toList()
      ..sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));
  }

  Future<List<UserPreference>> getUserPreferencesByType(
    String userId,
    String characterId,
    PreferenceType type
  ) async {
    return _userPreferenceBox.values
        .where((pref) =>
            pref.userId == userId &&
            pref.characterId == characterId &&
            pref.type == type &&
            pref.isActive)
        .toList()
      ..sort((a, b) => b.confidence.compareTo(a.confidence));
  }

  Future<void> deleteUserPreference(String preferenceId) async {
    await _userPreferenceBox.delete(preferenceId);
  }

  // 偏好模式相关操作
  Future<void> savePreferencePattern(PreferencePattern pattern) async {
    await _preferencePatternBox.put(pattern.id, pattern);
  }

  Future<List<PreferencePattern>> getPreferencePatterns(String userId, String characterId) async {
    return _preferencePatternBox.values
        .where((pattern) => pattern.userId == userId && pattern.characterId == characterId)
        .toList()
      ..sort((a, b) => b.strength.compareTo(a.strength));
  }

  // 行为分析相关操作
  Future<void> saveUserBehaviorAnalysis(UserBehaviorAnalysis analysis) async {
    await _behaviorAnalysisBox.put(analysis.id, analysis);
  }

  Future<UserBehaviorAnalysis?> getLatestUserBehaviorAnalysis(String userId, String characterId) async {
    final analyses = _behaviorAnalysisBox.values
        .where((analysis) => analysis.userId == userId && analysis.characterId == characterId)
        .toList()
      ..sort((a, b) => b.analysisDate.compareTo(a.analysisDate));

    return analyses.isNotEmpty ? analyses.first : null;
  }

  Future<List<UserBehaviorAnalysis>> getUserBehaviorAnalyses(String userId, String characterId) async {
    return _behaviorAnalysisBox.values
        .where((analysis) => analysis.userId == userId && analysis.characterId == characterId)
        .toList()
      ..sort((a, b) => b.analysisDate.compareTo(a.analysisDate));
  }

  // 对话洞察相关操作
  Future<void> saveConversationInsight(ConversationInsight insight) async {
    await _conversationInsightBox.put(insight.id, insight);
  }

  Future<List<ConversationInsight>> getConversationInsights(String userId, String characterId) async {
    return _conversationInsightBox.values
        .where((insight) => insight.userId == userId && insight.characterId == characterId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Future<List<ConversationInsight>> getUnAppliedInsights(String userId, String characterId) async {
    return _conversationInsightBox.values
        .where((insight) =>
            insight.userId == userId &&
            insight.characterId == characterId &&
            !insight.isApplied)
        .toList()
      ..sort((a, b) => b.confidence.compareTo(a.confidence));
  }

  // 群组对话相关操作
  Future<void> saveGroupConversation(GroupConversation conversation) async {
    await _groupConversationBox.put(conversation.id, conversation);
  }

  Future<GroupConversation?> getGroupConversation(String conversationId) async {
    return _groupConversationBox.get(conversationId);
  }

  Future<List<GroupConversation>> getGroupConversations(String userId) async {
    return _groupConversationBox.values
        .where((conv) => conv.userId == userId)
        .toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<void> deleteGroupConversation(String conversationId) async {
    await _groupConversationBox.delete(conversationId);
    // 同时删除相关的群组消息
    final messages = await getGroupMessages(conversationId);
    for (final message in messages) {
      await _groupMessageBox.delete(message.id);
    }
  }

  // 群组消息相关操作
  Future<void> saveGroupMessage(GroupMessage message) async {
    await _groupMessageBox.put(message.id, message);
  }

  Future<GroupMessage?> getGroupMessage(String messageId) async {
    return _groupMessageBox.get(messageId);
  }

  Future<List<GroupMessage>> getGroupMessages(String groupConversationId, {int? limit}) async {
    var messages = _groupMessageBox.values
        .where((msg) => msg.groupConversationId == groupConversationId)
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    if (limit != null && messages.length > limit) {
      messages = messages.sublist(messages.length - limit);
    }

    return messages;
  }

  Future<void> deleteGroupMessage(String messageId) async {
    await _groupMessageBox.delete(messageId);
  }

  // 角色关系相关操作
  Future<void> saveCharacterRelationship(CharacterRelationship relationship) async {
    await _characterRelationshipBox.put(relationship.id, relationship);
  }

  Future<CharacterRelationship?> getCharacterRelationship(String character1Id, String character2Id, String userId) async {
    return _characterRelationshipBox.values
        .where((rel) =>
            rel.userId == userId &&
            ((rel.character1Id == character1Id && rel.character2Id == character2Id) ||
             (rel.character1Id == character2Id && rel.character2Id == character1Id)))
        .firstOrNull;
  }

  Future<List<CharacterRelationship>> getCharacterRelationships(String characterId, String userId) async {
    return _characterRelationshipBox.values
        .where((rel) =>
            rel.userId == userId &&
            (rel.character1Id == characterId || rel.character2Id == characterId))
        .toList()
      ..sort((a, b) => b.strength.compareTo(a.strength));
  }

  // 群组互动事件相关操作
  Future<void> saveGroupInteractionEvent(GroupInteractionEvent event) async {
    await _groupInteractionEventBox.put(event.id, event);
  }

  Future<List<GroupInteractionEvent>> getGroupInteractionEvents(String groupConversationId) async {
    return _groupInteractionEventBox.values
        .where((event) => event.groupConversationId == groupConversationId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  // 对话动态相关操作
  Future<void> saveConversationDynamics(ConversationDynamics dynamics) async {
    await _conversationDynamicsBox.put(dynamics.groupConversationId, dynamics);
  }

  Future<ConversationDynamics?> getConversationDynamics(String groupConversationId) async {
    return _conversationDynamicsBox.get(groupConversationId);
  }

  // 情感状态相关操作
  Future<void> saveEmotionalState(EmotionalState state) async {
    await _emotionalStateBox.put(state.id, state);
  }

  Future<EmotionalState?> getEmotionalState(String stateId) async {
    return _emotionalStateBox.get(stateId);
  }

  Future<List<EmotionalState>> getEmotionalStates(String userId, String characterId, {int? limit}) async {
    var states = _emotionalStateBox.values
        .where((state) => state.userId == userId && state.characterId == characterId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    if (limit != null && states.length > limit) {
      states = states.sublist(0, limit);
    }

    return states;
  }

  Future<List<EmotionalState>> getEmotionalStatesInRange(
    String userId,
    String characterId,
    DateTime start,
    DateTime end,
  ) async {
    return _emotionalStateBox.values
        .where((state) =>
            state.userId == userId &&
            state.characterId == characterId &&
            state.timestamp.isAfter(start) &&
            state.timestamp.isBefore(end))
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  // 情感模式相关操作
  Future<void> saveEmotionalPattern(EmotionalPattern pattern) async {
    await _emotionalPatternBox.put(pattern.id, pattern);
  }

  Future<List<EmotionalPattern>> getEmotionalPatterns(String userId, String characterId) async {
    return _emotionalPatternBox.values
        .where((pattern) => pattern.userId == userId && pattern.characterId == characterId)
        .toList()
      ..sort((a, b) => b.detectedAt.compareTo(a.detectedAt));
  }

  // 情感洞察相关操作
  Future<void> saveEmotionalInsight(EmotionalInsight insight) async {
    await _emotionalInsightBox.put(insight.id, insight);
  }

  Future<List<EmotionalInsight>> getEmotionalInsights(String userId, String characterId) async {
    return _emotionalInsightBox.values
        .where((insight) => insight.userId == userId && insight.characterId == characterId)
        .toList()
      ..sort((a, b) => b.generatedAt.compareTo(a.generatedAt));
  }

  // 情绪摘要相关操作
  Future<void> saveMoodSummary(MoodSummary summary) async {
    await _moodSummaryBox.put(summary.id, summary);
  }

  Future<MoodSummary?> getLatestMoodSummary(String userId, String characterId) async {
    final summaries = _moodSummaryBox.values
        .where((summary) => summary.userId == userId && summary.characterId == characterId)
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));

    return summaries.isNotEmpty ? summaries.first : null;
  }

  Future<List<MoodSummary>> getMoodSummaries(String userId, String characterId, {int? limit}) async {
    var summaries = _moodSummaryBox.values
        .where((summary) => summary.userId == userId && summary.characterId == characterId)
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));

    if (limit != null && summaries.length > limit) {
      summaries = summaries.sublist(0, limit);
    }

    return summaries;
  }

  // 情感触发器相关操作
  Future<void> saveEmotionalTrigger(EmotionalTrigger trigger) async {
    await _emotionalTriggerBox.put(trigger.id, trigger);
  }

  Future<EmotionalTrigger?> getEmotionalTrigger(
    String userId,
    String characterId,
    String trigger,
    TriggerType type,
  ) async {
    return _emotionalTriggerBox.values
        .where((t) =>
            t.userId == userId &&
            t.characterId == characterId &&
            t.trigger == trigger &&
            t.type == type)
        .firstOrNull;
  }

  Future<List<EmotionalTrigger>> getEmotionalTriggers(String userId, String characterId) async {
    return _emotionalTriggerBox.values
        .where((trigger) => trigger.userId == userId && trigger.characterId == characterId)
        .toList()
      ..sort((a, b) => b.strength.compareTo(a.strength));
  }

  // 互动分析相关操作
  Future<void> saveInteractionAnalysis(InteractionAnalysis analysis) async {
    await _interactionAnalysisBox.put(analysis.id, analysis);
  }

  Future<InteractionAnalysis?> getInteractionAnalysis(String analysisId) async {
    return _interactionAnalysisBox.get(analysisId);
  }

  Future<List<InteractionAnalysis>> getInteractionAnalyses(String userId, String characterId, {int? limit}) async {
    var analyses = _interactionAnalysisBox.values
        .where((analysis) => analysis.userId == userId && analysis.characterId == characterId)
        .toList()
      ..sort((a, b) => b.analysisDate.compareTo(a.analysisDate));

    if (limit != null && analyses.length > limit) {
      analyses = analyses.sublist(0, limit);
    }

    return analyses;
  }

  Future<InteractionAnalysis?> getLatestInteractionAnalysis(String userId, String characterId) async {
    final analyses = await getInteractionAnalyses(userId, characterId, limit: 1);
    return analyses.isNotEmpty ? analyses.first : null;
  }

  // 互动报告相关操作
  Future<void> saveInteractionReport(InteractionReport report) async {
    await _interactionReportBox.put(report.id, report);
  }

  Future<InteractionReport?> getInteractionReport(String reportId) async {
    return _interactionReportBox.get(reportId);
  }

  Future<List<InteractionReport>> getInteractionReports(String userId, String characterId, {int? limit}) async {
    var reports = _interactionReportBox.values
        .where((report) => report.userId == userId && report.characterId == characterId)
        .toList()
      ..sort((a, b) => b.generatedAt.compareTo(a.generatedAt));

    if (limit != null && reports.length > limit) {
      reports = reports.sublist(0, limit);
    }

    return reports;
  }

  Future<List<InteractionReport>> getInteractionReportsByType(
    String userId,
    String characterId,
    ReportType type
  ) async {
    return _interactionReportBox.values
        .where((report) =>
            report.userId == userId &&
            report.characterId == characterId &&
            report.type == type)
        .toList()
      ..sort((a, b) => b.generatedAt.compareTo(a.generatedAt));
  }

  // 角色管理相关操作
  Future<void> saveRoleTemplate(RoleTemplate template) async {
    await _roleTemplateBox.put(template.id, template);
  }

  Future<RoleTemplate?> getRoleTemplate(String templateId) async {
    return _roleTemplateBox.get(templateId);
  }

  Future<List<RoleTemplate>> getAllRoleTemplates() async {
    return _roleTemplateBox.values.toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  Future<List<RoleTemplate>> getRoleTemplatesByCategory(String category) async {
    return _roleTemplateBox.values
        .where((template) => template.category == category)
        .toList()
      ..sort((a, b) => b.popularity.compareTo(a.popularity));
  }

  Future<void> saveRoleCollection(RoleCollection collection) async {
    await _roleCollectionBox.put(collection.id, collection);
  }

  Future<RoleCollection?> getRoleCollection(String collectionId) async {
    return _roleCollectionBox.get(collectionId);
  }

  Future<List<RoleCollection>> getRoleCollections(String userId) async {
    return _roleCollectionBox.values
        .where((collection) => collection.userId == userId)
        .toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  Future<void> saveRoleInteractionRule(RoleInteractionRule rule) async {
    await _roleInteractionRuleBox.put(rule.id, rule);
  }

  Future<RoleInteractionRule?> getRoleInteractionRule(String ruleId) async {
    return _roleInteractionRuleBox.get(ruleId);
  }

  Future<List<RoleInteractionRule>> getRoleInteractionRules({
    String? fromCharacterId,
    String? toCharacterId,
  }) async {
    var rules = _roleInteractionRuleBox.values.where((rule) => rule.isActive);

    if (fromCharacterId != null) {
      rules = rules.where((rule) => rule.fromCharacterId == fromCharacterId);
    }

    if (toCharacterId != null) {
      rules = rules.where((rule) => rule.toCharacterId == toCharacterId);
    }

    return rules.toList()
      ..sort((a, b) => b.strength.compareTo(a.strength));
  }

  Future<void> saveRoleSchedule(RoleSchedule schedule) async {
    await _roleScheduleBox.put(schedule.id, schedule);
  }

  Future<RoleSchedule?> getRoleSchedule(String scheduleId) async {
    return _roleScheduleBox.get(scheduleId);
  }

  Future<List<RoleSchedule>> getRoleSchedules(String characterId) async {
    return _roleScheduleBox.values
        .where((schedule) => schedule.characterId == characterId)
        .toList()
      ..sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  Future<void> saveRoleCustomization(RoleCustomization customization) async {
    await _roleCustomizationBox.put(customization.id, customization);
  }

  Future<RoleCustomization?> getRoleCustomization(String customizationId) async {
    return _roleCustomizationBox.get(customizationId);
  }

  Future<List<RoleCustomization>> getRoleCustomizations(String characterId) async {
    return _roleCustomizationBox.values
        .where((customization) => customization.characterId == characterId)
        .toList()
      ..sort((a, b) => b.appliedAt.compareTo(a.appliedAt));
  }

  Future<void> saveRoleConflict(RoleConflict conflict) async {
    await _roleConflictBox.put(conflict.id, conflict);
  }

  Future<RoleConflict?> getRoleConflict(String conflictId) async {
    return _roleConflictBox.get(conflictId);
  }

  Future<List<RoleConflict>> getRoleConflicts(String userId) async {
    return _roleConflictBox.values
        .where((conflict) => conflict.userId == userId)
        .toList()
      ..sort((a, b) => b.detectedAt.compareTo(a.detectedAt));
  }

  Future<void> saveRoleRecommendation(RoleRecommendation recommendation) async {
    await _roleRecommendationBox.put(recommendation.id, recommendation);
  }

  Future<RoleRecommendation?> getRoleRecommendation(String recommendationId) async {
    return _roleRecommendationBox.get(recommendationId);
  }

  Future<List<RoleRecommendation>> getRoleRecommendations(String userId) async {
    return _roleRecommendationBox.values
        .where((recommendation) => recommendation.userId == userId)
        .toList()
      ..sort((a, b) => b.generatedAt.compareTo(a.generatedAt));
  }

  // 通知系统相关操作
  Future<void> saveNotification(AppNotification notification) async {
    await _notificationBox.put(notification.id, notification);
  }

  Future<AppNotification?> getNotification(String notificationId) async {
    return _notificationBox.get(notificationId);
  }

  Future<List<AppNotification>> getNotifications({
    required String userId,
    NotificationType? type,
    String? characterId,
    bool? isRead,
    NotificationPriority? priority,
    int? limit,
  }) async {
    var notifications = _notificationBox.values.where((notification) =>
        notification.userId == userId);

    if (type != null) {
      notifications = notifications.where((n) => n.type == type);
    }

    if (characterId != null) {
      notifications = notifications.where((n) => n.characterId == characterId);
    }

    if (isRead != null) {
      notifications = notifications.where((n) => n.isRead == isRead);
    }

    if (priority != null) {
      notifications = notifications.where((n) => n.priority == priority);
    }

    var result = notifications.toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    if (limit != null && result.length > limit) {
      result = result.sublist(0, limit);
    }

    return result;
  }

  Future<List<AppNotification>> getNotificationsInRange(
    String userId,
    DateTime start,
    DateTime end,
  ) async {
    return _notificationBox.values
        .where((notification) =>
            notification.userId == userId &&
            notification.createdAt.isAfter(start) &&
            notification.createdAt.isBefore(end))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  Future<void> deleteNotification(String notificationId) async {
    await _notificationBox.delete(notificationId);
  }

  Future<void> deleteExpiredNotifications(String userId, DateTime cutoffTime) async {
    final expiredNotifications = _notificationBox.values
        .where((notification) =>
            notification.userId == userId &&
            notification.createdAt.isBefore(cutoffTime))
        .toList();

    for (final notification in expiredNotifications) {
      await _notificationBox.delete(notification.id);
    }
  }

  Future<void> saveNotificationSettings(NotificationSettings settings) async {
    await _notificationSettingsBox.put(settings.userId, settings);
  }

  Future<NotificationSettings?> getNotificationSettings(String userId) async {
    return _notificationSettingsBox.get(userId);
  }

  Future<void> saveNotificationTemplate(NotificationTemplate template) async {
    await _notificationTemplateBox.put(template.id, template);
  }

  Future<NotificationTemplate?> getNotificationTemplate(String templateId) async {
    return _notificationTemplateBox.get(templateId);
  }

  Future<List<NotificationTemplate>> getNotificationTemplates({
    NotificationType? type,
    bool? enabled,
  }) async {
    var templates = _notificationTemplateBox.values.asIterable();

    if (type != null) {
      templates = templates.where((template) => template.type == type);
    }

    if (enabled != null) {
      templates = templates.where((template) => template.enabled == enabled);
    }

    return templates.toList();
  }

  Future<void> saveNotificationRule(NotificationRule rule) async {
    await _notificationRuleBox.put(rule.id, rule);
  }

  Future<NotificationRule?> getNotificationRule(String ruleId) async {
    return _notificationRuleBox.get(ruleId);
  }

  Future<List<NotificationRule>> getNotificationRules(String userId) async {
    return _notificationRuleBox.values
        .where((rule) => rule.userId == userId)
        .toList()
      ..sort((a, b) => b.priority.compareTo(a.priority));
  }

  Future<void> saveNotificationAnalytics(NotificationAnalytics analytics) async {
    final key = '${analytics.userId}_${analytics.date.millisecondsSinceEpoch}';
    await _notificationAnalyticsBox.put(key, analytics);
  }

  Future<NotificationAnalytics?> getNotificationAnalytics(String userId, DateTime date) async {
    final key = '${userId}_${date.millisecondsSinceEpoch}';
    return _notificationAnalyticsBox.get(key);
  }

  Future<List<NotificationAnalytics>> getNotificationAnalyticsRange(
    String userId,
    DateTime start,
    DateTime end,
  ) async {
    return _notificationAnalyticsBox.values
        .where((analytics) =>
            analytics.userId == userId &&
            analytics.date.isAfter(start) &&
            analytics.date.isBefore(end))
        .toList()
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  Future<void> saveSmartSuggestion(SmartNotificationSuggestion suggestion) async {
    await _smartSuggestionBox.put(suggestion.id, suggestion);
  }

  Future<SmartNotificationSuggestion?> getSmartSuggestion(String suggestionId) async {
    return _smartSuggestionBox.get(suggestionId);
  }

  Future<List<SmartNotificationSuggestion>> getSmartSuggestions(String userId) async {
    return _smartSuggestionBox.values
        .where((suggestion) => suggestion.userId == userId)
        .toList()
      ..sort((a, b) => b.generatedAt.compareTo(a.generatedAt));
  }

  // 内容解锁相关操作
  Future<void> saveUnlockableContent(UnlockableContent content) async {
    await _unlockableContentBox.put(content.id, content);
  }

  Future<UnlockableContent?> getUnlockableContent(String contentId) async {
    return _unlockableContentBox.get(contentId);
  }

  Future<List<UnlockableContent>> getUnlockableContent(String characterId) async {
    return _unlockableContentBox.values
        .where((content) => content.characterId == characterId)
        .toList()
      ..sort((a, b) => a.unlockOrder.compareTo(b.unlockOrder));
  }

  Future<void> saveContentUnlock(ContentUnlock unlock) async {
    await _contentUnlockBox.put(unlock.id, unlock);
  }

  Future<ContentUnlock?> getContentUnlock(String userId, String contentId) async {
    return _contentUnlockBox.values
        .where((unlock) => unlock.userId == userId && unlock.contentId == contentId)
        .firstOrNull;
  }

  Future<List<ContentUnlock>> getContentUnlocks(String userId, String characterId) async {
    // 获取角色的所有内容ID
    final characterContent = await getUnlockableContent(characterId);
    final characterContentIds = characterContent.map((c) => c.id).toSet();

    return _contentUnlockBox.values
        .where((unlock) => unlock.userId == userId && characterContentIds.contains(unlock.contentId))
        .toList()
      ..sort((a, b) => b.unlockedAt.compareTo(a.unlockedAt));
  }

  Future<void> saveUnlockProgress(UnlockProgress progress) async {
    final key = '${progress.userId}_${progress.characterId}_${progress.contentId}';
    await _unlockProgressBox.put(key, progress);
  }

  Future<UnlockProgress?> getUnlockProgress(String userId, String characterId, String contentId) async {
    final key = '${userId}_${characterId}_$contentId';
    return _unlockProgressBox.get(key);
  }

  Future<List<UnlockProgress>> getUnlockProgressList(String userId, String characterId) async {
    return _unlockProgressBox.values
        .where((progress) => progress.userId == userId && progress.characterId == characterId)
        .toList()
      ..sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));
  }

  Future<void> saveContentRecommendation(ContentRecommendation recommendation) async {
    await _contentRecommendationBox.put(recommendation.id, recommendation);
  }

  Future<List<ContentRecommendation>> getContentRecommendations(String userId) async {
    return _contentRecommendationBox.values
        .where((rec) => rec.userId == userId)
        .toList()
      ..sort((a, b) => b.generatedAt.compareTo(a.generatedAt));
  }

  Future<void> saveUnlockEvent(UnlockEvent event) async {
    await _unlockEventBox.put(event.id, event);
  }

  Future<List<UnlockEvent>> getUnlockEvents(String userId, String characterId) async {
    return _unlockEventBox.values
        .where((event) => event.userId == userId && event.characterId == characterId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Future<void> saveUnlockNotification(UnlockNotification notification) async {
    await _unlockNotificationBox.put(notification.id, notification);
  }

  Future<List<UnlockNotification>> getUnlockNotifications(String userId) async {
    return _unlockNotificationBox.values
        .where((notification) => notification.userId == userId)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  Future<void> saveContentCollection(ContentCollection collection) async {
    await _contentCollectionBox.put(collection.id, collection);
  }

  Future<List<ContentCollection>> getContentCollections(String characterId) async {
    return _contentCollectionBox.values
        .where((collection) => collection.characterId == characterId)
        .toList()
      ..sort((a, b) => a.unlockOrder.compareTo(b.unlockOrder));
  }

  // 角色互动相关操作
  Future<void> saveInteractionContext(InteractionContext context) async {
    await _interactionContextBox.put(context.id, context);
  }

  Future<InteractionContext?> getInteractionContext(String contextId) async {
    return _interactionContextBox.get(contextId);
  }

  Future<List<InteractionContext>> getInteractionContexts({
    String? userId,
    String? characterId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    var contexts = _interactionContextBox.values.asIterable();

    if (userId != null) {
      contexts = contexts.where((context) => context.userId == userId);
    }

    if (characterId != null) {
      contexts = contexts.where((context) => context.characterIds.contains(characterId));
    }

    if (startDate != null) {
      contexts = contexts.where((context) => context.startTime.isAfter(startDate));
    }

    if (endDate != null) {
      contexts = contexts.where((context) => context.startTime.isBefore(endDate));
    }

    return contexts.toList()
      ..sort((a, b) => b.startTime.compareTo(a.startTime));
  }

  Future<void> saveInteractionEvent(InteractionEvent event) async {
    await _interactionEventBox.put(event.id, event);
  }

  Future<List<InteractionEvent>> getInteractionEvents(String interactionId) async {
    return _interactionEventBox.values
        .where((event) => event.interactionId == interactionId)
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  Future<void> saveCharacterReaction(CharacterReaction reaction) async {
    await _characterReactionBox.put(reaction.id, reaction);
  }

  Future<List<CharacterReaction>> getCharacterReactions(String characterId) async {
    return _characterReactionBox.values
        .where((reaction) => reaction.characterId == characterId)
        .toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  Future<void> saveInteractionRule(InteractionRule rule) async {
    await _interactionRuleBox.put(rule.id, rule);
  }

  Future<InteractionRule?> getInteractionRule(String ruleId) async {
    return _interactionRuleBox.get(ruleId);
  }

  Future<List<InteractionRule>> getInteractionRules() async {
    return _interactionRuleBox.values
        .where((rule) => rule.isActive)
        .toList()
      ..sort((a, b) => b.priority.compareTo(a.priority));
  }

  Future<void> saveInteractionPattern(InteractionPattern pattern) async {
    await _interactionPatternBox.put(pattern.id, pattern);
  }

  Future<List<InteractionPattern>> getInteractionPatterns() async {
    return _interactionPatternBox.values.toList()
      ..sort((a, b) => b.probability.compareTo(a.probability));
  }

  Future<void> saveCharacterRelationship(CharacterRelationship relationship) async {
    await _characterRelationshipBox.put(relationship.id, relationship);
  }

  Future<CharacterRelationship?> getCharacterRelationship(String character1Id, String character2Id) async {
    return _characterRelationshipBox.values
        .where((rel) =>
            (rel.character1Id == character1Id && rel.character2Id == character2Id) ||
            (rel.character1Id == character2Id && rel.character2Id == character1Id))
        .firstOrNull;
  }

  Future<List<CharacterRelationship>> getCharacterRelationships(String characterId) async {
    return _characterRelationshipBox.values
        .where((rel) => rel.character1Id == characterId || rel.character2Id == characterId)
        .toList()
      ..sort((a, b) => b.strength.compareTo(a.strength));
  }

  Future<void> saveInteractionAnalytics(InteractionAnalytics analytics) async {
    final key = '${analytics.characterIds.join('_')}_${analytics.date.millisecondsSinceEpoch}';
    await _interactionAnalyticsBox.put(key, analytics);
  }

  Future<List<InteractionAnalytics>> getInteractionAnalytics({
    List<String>? characterIds,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    var analytics = _interactionAnalyticsBox.values.asIterable();

    if (characterIds != null && characterIds.isNotEmpty) {
      analytics = analytics.where((analytic) =>
          characterIds.any((id) => analytic.characterIds.contains(id)));
    }

    if (startDate != null) {
      analytics = analytics.where((analytic) => analytic.date.isAfter(startDate));
    }

    if (endDate != null) {
      analytics = analytics.where((analytic) => analytic.date.isBefore(endDate));
    }

    return analytics.toList()
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  // 群组对话相关操作
  Future<void> saveGroupConversation(GroupConversation group) async {
    await _groupConversationBox.put(group.id, group);
  }

  Future<GroupConversation?> getGroupConversation(String groupId) async {
    return _groupConversationBox.get(groupId);
  }

  Future<List<GroupConversation>> getGroupConversations({
    String? userId,
    GroupType? type,
    GroupStatus? status,
  }) async {
    var groups = _groupConversationBox.values.asIterable();

    if (userId != null) {
      groups = groups.where((group) => group.participantIds.contains(userId));
    }

    if (type != null) {
      groups = groups.where((group) => group.type == type);
    }

    if (status != null) {
      groups = groups.where((group) => group.status == status);
    }

    return groups.toList()
      ..sort((a, b) => b.lastActivity?.compareTo(a.lastActivity ?? DateTime.now()) ?? 0);
  }

  Future<List<Message>> getGroupMessages(String groupId, {int? limit, int? offset}) async {
    final group = await getGroupConversation(groupId);
    if (group == null) return [];

    var messageIds = group.messageIds;

    if (offset != null && offset > 0) {
      messageIds = messageIds.skip(offset).toList();
    }

    if (limit != null && limit > 0) {
      messageIds = messageIds.take(limit).toList();
    }

    final messages = <Message>[];
    for (final messageId in messageIds) {
      final message = await getMessage(messageId);
      if (message != null) {
        messages.add(message);
      }
    }

    return messages..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  Future<void> saveGroupEvent(GroupEvent event) async {
    await _groupEventBox.put(event.id, event);
  }

  Future<List<GroupEvent>> getGroupEvents(String groupId) async {
    return _groupEventBox.values
        .where((event) => event.groupId == groupId)
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  Future<void> saveConversationScenario(ConversationScenario scenario) async {
    await _conversationScenarioBox.put(scenario.id, scenario);
  }

  Future<ConversationScenario?> getConversationScenario(String scenarioId) async {
    return _conversationScenarioBox.get(scenarioId);
  }

  Future<List<ConversationScenario>> getConversationScenarios({
    ScenarioType? type,
    List<String>? tags,
    double? minDifficulty,
    double? maxDifficulty,
  }) async {
    var scenarios = _conversationScenarioBox.values.where((s) => s.isActive);

    if (type != null) {
      scenarios = scenarios.where((scenario) => scenario.type == type);
    }

    if (tags != null && tags.isNotEmpty) {
      scenarios = scenarios.where((scenario) =>
          tags.any((tag) => scenario.tags.contains(tag)));
    }

    if (minDifficulty != null) {
      scenarios = scenarios.where((scenario) => scenario.difficulty >= minDifficulty);
    }

    if (maxDifficulty != null) {
      scenarios = scenarios.where((scenario) => scenario.difficulty <= maxDifficulty);
    }

    return scenarios.toList()
      ..sort((a, b) => a.difficulty.compareTo(b.difficulty));
  }

  Future<void> saveScenarioExecution(ScenarioExecution execution) async {
    await _scenarioExecutionBox.put(execution.id, execution);
  }

  Future<ScenarioExecution?> getScenarioExecution(String executionId) async {
    return _scenarioExecutionBox.get(executionId);
  }

  Future<List<ScenarioExecution>> getScenarioExecutions({
    String? scenarioId,
    String? groupId,
    ExecutionStatus? status,
  }) async {
    var executions = _scenarioExecutionBox.values.asIterable();

    if (scenarioId != null) {
      executions = executions.where((exec) => exec.scenarioId == scenarioId);
    }

    if (groupId != null) {
      executions = executions.where((exec) => exec.groupId == groupId);
    }

    if (status != null) {
      executions = executions.where((exec) => exec.status == status);
    }

    return executions.toList()
      ..sort((a, b) => b.startTime.compareTo(a.startTime));
  }

  Future<void> saveExecutionEvent(ExecutionEvent event) async {
    await _executionEventBox.put(event.id, event);
  }

  Future<List<ExecutionEvent>> getExecutionEvents(String executionId) async {
    return _executionEventBox.values
        .where((event) => event.executionId == executionId)
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  Future<void> saveGroupAnalytics(GroupAnalytics analytics) async {
    final key = '${analytics.groupId}_${analytics.date.millisecondsSinceEpoch}';
    await _groupAnalyticsBox.put(key, analytics);
  }

  Future<List<GroupAnalytics>> getGroupAnalytics({
    String? groupId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    var analytics = _groupAnalyticsBox.values.asIterable();

    if (groupId != null) {
      analytics = analytics.where((analytic) => analytic.groupId == groupId);
    }

    if (startDate != null) {
      analytics = analytics.where((analytic) => analytic.date.isAfter(startDate));
    }

    if (endDate != null) {
      analytics = analytics.where((analytic) => analytic.date.isBefore(endDate));
    }

    return analytics.toList()
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  Future<void> saveConversationModerator(ConversationModerator moderator) async {
    await _conversationModeratorBox.put(moderator.id, moderator);
  }

  Future<List<ConversationModerator>> getConversationModerators(String groupId) async {
    return _conversationModeratorBox.values
        .where((moderator) => moderator.groupId == groupId)
        .toList();
  }

  // 清理所有数据
  Future<void> clearAllData() async {
    await _messageBox.clear();
    await _conversationBox.clear();
    await _characterBox.clear();
    await _memoryBox.clear();
    await _memoryClusterBox.clear();
    await _userProfileBox.clear();
    await _userPreferenceBox.clear();
    await _preferencePatternBox.clear();
    await _behaviorAnalysisBox.clear();
    await _conversationInsightBox.clear();
    await _groupConversationBox.clear();
    await _groupMessageBox.clear();
    await _characterRelationshipBox.clear();
    await _groupInteractionEventBox.clear();
    await _conversationDynamicsBox.clear();
    await _emotionalStateBox.clear();
    await _emotionalPatternBox.clear();
    await _emotionalInsightBox.clear();
    await _moodSummaryBox.clear();
    await _emotionalTriggerBox.clear();
    await _interactionAnalysisBox.clear();
    await _interactionReportBox.clear();
    await _roleTemplateBox.clear();
    await _roleCollectionBox.clear();
    await _roleInteractionRuleBox.clear();
    await _roleScheduleBox.clear();
    await _roleCustomizationBox.clear();
    await _roleConflictBox.clear();
    await _roleRecommendationBox.clear();
    await _notificationBox.clear();
    await _notificationSettingsBox.clear();
    await _notificationTemplateBox.clear();
    await _notificationRuleBox.clear();
    await _notificationAnalyticsBox.clear();
    await _smartSuggestionBox.clear();
    await _unlockableContentBox.clear();
    await _contentUnlockBox.clear();
    await _unlockProgressBox.clear();
    await _contentRecommendationBox.clear();
    await _unlockEventBox.clear();
    await _unlockNotificationBox.clear();
    await _contentCollectionBox.clear();
    await _interactionContextBox.clear();
    await _interactionEventBox.clear();
    await _characterReactionBox.clear();
    await _interactionRuleBox.clear();
    await _interactionPatternBox.clear();
    await _characterRelationshipBox.clear();
    await _interactionAnalyticsBox.clear();
    await _groupConversationBox.clear();
    await _groupEventBox.clear();
    await _conversationScenarioBox.clear();
    await _scenarioExecutionBox.clear();
    await _executionEventBox.clear();
    await _groupAnalyticsBox.clear();
    await _conversationModeratorBox.clear();
    await _initializeDefaultCharacters();
    await _initializeDefaultContent();
    await _initializeDefaultInteractionRules();
    await _initializeDefaultScenarios();
  }

  // 关闭数据库
  Future<void> close() async {
    await _messageBox.close();
    await _conversationBox.close();
    await _characterBox.close();
    await _memoryBox.close();
    await _memoryClusterBox.close();
    await _userProfileBox.close();
    await _userPreferenceBox.close();
    await _preferencePatternBox.close();
    await _behaviorAnalysisBox.close();
    await _conversationInsightBox.close();
    await _groupConversationBox.close();
    await _groupMessageBox.close();
    await _characterRelationshipBox.close();
    await _groupInteractionEventBox.close();
    await _conversationDynamicsBox.close();
    await _emotionalStateBox.close();
    await _emotionalPatternBox.close();
    await _emotionalInsightBox.close();
    await _moodSummaryBox.close();
    await _emotionalTriggerBox.close();
    await _interactionAnalysisBox.close();
    await _interactionReportBox.close();
    await _roleTemplateBox.close();
    await _roleCollectionBox.close();
    await _roleInteractionRuleBox.close();
    await _roleScheduleBox.close();
    await _roleCustomizationBox.close();
    await _roleConflictBox.close();
    await _roleRecommendationBox.close();
    await _notificationBox.close();
    await _notificationSettingsBox.close();
    await _notificationTemplateBox.close();
    await _notificationRuleBox.close();
    await _notificationAnalyticsBox.close();
    await _smartSuggestionBox.close();
    await _unlockableContentBox.close();
    await _contentUnlockBox.close();
    await _unlockProgressBox.close();
    await _contentRecommendationBox.close();
    await _unlockEventBox.close();
    await _unlockNotificationBox.close();
    await _contentCollectionBox.close();
    await _interactionContextBox.close();
    await _interactionEventBox.close();
    await _characterReactionBox.close();
    await _interactionRuleBox.close();
    await _interactionPatternBox.close();
    await _characterRelationshipBox.close();
    await _interactionAnalyticsBox.close();
    await _groupConversationBox.close();
    await _groupEventBox.close();
    await _conversationScenarioBox.close();
    await _scenarioExecutionBox.close();
    await _executionEventBox.close();
    await _groupAnalyticsBox.close();
    await _conversationModeratorBox.close();
  }

  // 初始化默认内容
  Future<void> _initializeDefaultContent() async {
    if (_unlockableContentBox.isNotEmpty) return;

    final defaultContent = [
      // 小雨的内容
      UnlockableContent(
        id: 'content_1_1',
        characterId: '1',
        type: ContentType.story,
        title: '初次相遇的回忆',
        description: '记录我们第一次见面时的美好时光',
        content: '那是一个阳光明媚的下午，我们在咖啡厅里第一次相遇...',
        unlockConditions: [
          UnlockCondition(
            id: 'cond_1',
            type: ConditionType.conversationCount,
            parameters: {'count': 5},
            description: '与小雨对话5次',
          ),
        ],
        rarity: ContentRarity.common,
        unlockOrder: 1,
        tags: ['回忆', '初遇', '温馨'],
        createdAt: DateTime.now(),
      ),
      UnlockableContent(
        id: 'content_1_2',
        characterId: '1',
        type: ContentType.image,
        title: '小雨的笑容',
        description: '捕捉到的最美瞬间',
        content: '一张温暖的笑容照片',
        unlockConditions: [
          UnlockCondition(
            id: 'cond_2',
            type: ConditionType.intimacyLevel,
            parameters: {'level': 2},
            description: '亲密度达到2级',
          ),
        ],
        rarity: ContentRarity.uncommon,
        unlockOrder: 2,
        tags: ['照片', '笑容', '美好'],
        createdAt: DateTime.now(),
      ),
      UnlockableContent(
        id: 'content_1_3',
        characterId: '1',
        type: ContentType.dialogue,
        title: '特殊对话：心里话',
        description: '小雨向你敞开心扉的特殊对话',
        content: '其实...我一直想对你说...',
        unlockConditions: [
          UnlockCondition(
            id: 'cond_3',
            type: ConditionType.intimacyLevel,
            parameters: {'level': 5},
            description: '亲密度达到5级',
          ),
          UnlockCondition(
            id: 'cond_4',
            type: ConditionType.timeSpent,
            parameters: {'minutes': 60},
            description: '相处时间超过1小时',
          ),
        ],
        rarity: ContentRarity.rare,
        unlockOrder: 3,
        tags: ['对话', '心里话', '深度'],
        createdAt: DateTime.now(),
      ),

      // 智子的内容
      UnlockableContent(
        id: 'content_2_1',
        characterId: '2',
        type: ContentType.background,
        title: '智子的过往',
        description: '了解智子的成长经历',
        content: '智子从小就展现出过人的智慧...',
        unlockConditions: [
          UnlockCondition(
            id: 'cond_5',
            type: ConditionType.conversationCount,
            parameters: {'count': 3},
            description: '与智子对话3次',
          ),
        ],
        rarity: ContentRarity.common,
        unlockOrder: 1,
        tags: ['背景', '成长', '智慧'],
        createdAt: DateTime.now(),
      ),
      UnlockableContent(
        id: 'content_2_2',
        characterId: '2',
        type: ContentType.achievement,
        title: '学者之路',
        description: '见证智子的学术成就',
        content: '智子在学术领域的杰出表现',
        unlockConditions: [
          UnlockCondition(
            id: 'cond_6',
            type: ConditionType.specificDialogue,
            parameters: {'keywords': ['学习', '知识', '研究']},
            description: '与智子讨论学术话题',
          ),
        ],
        rarity: ContentRarity.epic,
        unlockOrder: 2,
        tags: ['成就', '学术', '知识'],
        createdAt: DateTime.now(),
      ),
    ];

    for (final content in defaultContent) {
      await saveUnlockableContent(content);
    }
  }

  // 初始化默认互动规则
  Future<void> _initializeDefaultInteractionRules() async {
    if (_interactionRuleBox.isNotEmpty) return;

    final defaultRules = [
      // 情绪响应规则
      InteractionRule(
        id: 'rule_emotion_response',
        name: '情绪响应规则',
        type: RuleType.trigger,
        conditions: [
          RuleCondition(
            id: 'cond_emotion_high',
            type: ConditionType.emotionalState,
            field: 'mood',
            operator: ConditionOperator.greaterThan,
            value: 0.8,
            description: '情绪高涨时',
          ),
        ],
        actions: [
          RuleAction(
            id: 'action_positive_reaction',
            type: ActionType.triggerReaction,
            target: 'any',
            parameters: {
              'type': 'emotional',
              'content': '表现出积极的情绪反应',
              'intensity': 0.7,
            },
            description: '触发积极反应',
          ),
        ],
        priority: 0.8,
        applicableInteractions: [InteractionType.oneOnOne, InteractionType.groupChat],
        description: '当角色情绪高涨时触发积极反应',
        createdAt: DateTime.now(),
      ),

      // 亲密度调整规则
      InteractionRule(
        id: 'rule_intimacy_boost',
        name: '亲密度提升规则',
        type: RuleType.modifier,
        conditions: [
          RuleCondition(
            id: 'cond_positive_interaction',
            type: ConditionType.contextual,
            field: 'emotionalTone',
            operator: ConditionOperator.contains,
            value: 'joy',
            description: '积极互动时',
          ),
        ],
        actions: [
          RuleAction(
            id: 'action_boost_intimacy',
            type: ActionType.adjustIntimacy,
            target: 'sender',
            parameters: {'adjustment': 0.1},
            intensity: 0.5,
            description: '提升亲密度',
          ),
        ],
        priority: 0.6,
        description: '积极互动时提升亲密度',
        createdAt: DateTime.now(),
      ),

      // 冲突检测规则
      InteractionRule(
        id: 'rule_conflict_detection',
        name: '冲突检测规则',
        type: RuleType.trigger,
        conditions: [
          RuleCondition(
            id: 'cond_negative_emotion',
            type: ConditionType.emotionalState,
            field: 'mood',
            operator: ConditionOperator.lessThan,
            value: 0.3,
            description: '情绪低落时',
          ),
        ],
        actions: [
          RuleAction(
            id: 'action_conflict_event',
            type: ActionType.triggerEvent,
            target: 'interaction',
            parameters: {
              'eventType': 'conflictDetected',
              'severity': 'medium',
            },
            description: '触发冲突事件',
          ),
        ],
        priority: 0.9,
        description: '检测并处理角色间冲突',
        createdAt: DateTime.now(),
      ),

      // 记忆创建规则
      InteractionRule(
        id: 'rule_memory_creation',
        name: '记忆创建规则',
        type: RuleType.enhancement,
        conditions: [
          RuleCondition(
            id: 'cond_important_moment',
            type: ConditionType.contextual,
            field: 'importance',
            operator: ConditionOperator.greaterThan,
            value: 0.7,
            description: '重要时刻',
          ),
        ],
        actions: [
          RuleAction(
            id: 'action_create_memory',
            type: ActionType.createMemory,
            target: 'all',
            parameters: {
              'importance': 0.8,
              'tags': ['重要时刻', '互动'],
            },
            description: '创建重要记忆',
          ),
        ],
        priority: 0.7,
        description: '在重要时刻创建记忆',
        createdAt: DateTime.now(),
      ),
    ];

    for (final rule in defaultRules) {
      await saveInteractionRule(rule);
    }

    // 创建默认互动模式
    final defaultPatterns = [
      InteractionPattern(
        id: 'pattern_friendly_chat',
        name: '友好聊天模式',
        type: PatternType.conversation,
        characterIds: ['1', '2'], // 小雨和智子
        steps: [
          PatternStep(
            order: 1,
            characterId: '1',
            type: StepType.message,
            content: '你好！今天过得怎么样？',
            probability: 0.8,
          ),
          PatternStep(
            order: 2,
            characterId: '2',
            type: StepType.reaction,
            content: '表现出友好的态度',
            parameters: {
              'type': 'social',
              'intensity': 0.6,
            },
            probability: 0.7,
          ),
        ],
        probability: 0.3,
        triggers: ['friendly_mood'],
        description: '角色间的友好对话模式',
        createdAt: DateTime.now(),
      ),
    ];

    for (final pattern in defaultPatterns) {
      await saveInteractionPattern(pattern);
    }
  }

  // 初始化默认对话场景
  Future<void> _initializeDefaultScenarios() async {
    if (_conversationScenarioBox.isNotEmpty) return;

    final defaultScenarios = [
      ConversationScenario(
        id: 'scenario_icebreaker_1',
        name: '初次见面破冰',
        description: '帮助角色们在初次见面时打破僵局',
        type: ScenarioType.icebreaker,
        requiredCharacters: ['1', '2'], // 小雨和智子
        steps: [
          ScenarioStep(
            order: 1,
            type: StepType.introduction,
            characterId: '1',
            content: '大家好！我是小雨，很高兴认识大家！',
            probability: 1.0,
          ),
          ScenarioStep(
            order: 2,
            type: StepType.response,
            characterId: '2',
            content: '你好小雨，我是智子。很高兴见到你！',
            probability: 0.9,
          ),
          ScenarioStep(
            order: 3,
            type: StepType.question,
            characterId: '1',
            content: '智子，你平时喜欢做什么呢？',
            probability: 0.8,
          ),
          ScenarioStep(
            order: 4,
            type: StepType.response,
            characterId: '2',
            content: '我喜欢读书和研究新知识，你呢？',
            probability: 0.8,
          ),
        ],
        triggers: [
          ScenarioTrigger(
            id: 'trigger_first_meeting',
            type: TriggerType.eventBasedTrigger,
            conditions: {
              'keywords': ['你好', '初次', '认识'],
            },
            probability: 0.7,
          ),
        ],
        tags: ['破冰', '初次见面', '友好'],
        difficulty: 1.0,
        estimatedDuration: 15,
        createdAt: DateTime.now(),
      ),

      ConversationScenario(
        id: 'scenario_problem_solving',
        name: '协作解决问题',
        description: '角色们一起讨论和解决问题',
        type: ScenarioType.problemSolving,
        requiredCharacters: ['1', '2'],
        steps: [
          ScenarioStep(
            order: 1,
            type: StepType.introduction,
            characterId: '1',
            content: '我们遇到了一个问题，大家一起想想解决办法吧！',
            probability: 1.0,
          ),
          ScenarioStep(
            order: 2,
            type: StepType.question,
            characterId: '2',
            content: '能详细说说是什么问题吗？',
            probability: 0.9,
          ),
          ScenarioStep(
            order: 3,
            type: StepType.response,
            characterId: '1',
            content: '问题是这样的...',
            probability: 0.8,
          ),
          ScenarioStep(
            order: 4,
            type: StepType.action,
            characterId: '2',
            content: '让我分析一下，我觉得可以这样解决...',
            probability: 0.8,
          ),
        ],
        triggers: [
          ScenarioTrigger(
            id: 'trigger_problem',
            type: TriggerType.eventBasedTrigger,
            conditions: {
              'keywords': ['问题', '困难', '帮助', '解决'],
            },
            probability: 0.6,
          ),
        ],
        tags: ['问题解决', '协作', '思考'],
        difficulty: 2.0,
        estimatedDuration: 25,
        createdAt: DateTime.now(),
      ),

      ConversationScenario(
        id: 'scenario_celebration',
        name: '庆祝时刻',
        description: '一起庆祝特殊的时刻或成就',
        type: ScenarioType.celebration,
        requiredCharacters: ['1', '2'],
        steps: [
          ScenarioStep(
            order: 1,
            type: StepType.emotion,
            characterId: '1',
            content: '太棒了！我们成功了！',
            parameters: {'emotion': 'joy', 'intensity': 0.9},
            probability: 1.0,
          ),
          ScenarioStep(
            order: 2,
            type: StepType.reaction,
            characterId: '2',
            content: '是的！这真是值得庆祝的时刻！',
            parameters: {'emotion': 'joy', 'intensity': 0.8},
            probability: 0.9,
          ),
          ScenarioStep(
            order: 3,
            type: StepType.action,
            characterId: '1',
            content: '我们应该好好庆祝一下！',
            probability: 0.8,
          ),
        ],
        triggers: [
          ScenarioTrigger(
            id: 'trigger_success',
            type: TriggerType.emotionBasedTrigger,
            conditions: {
              'emotions': ['开心', '成功', '庆祝', '太棒了'],
            },
            probability: 0.8,
          ),
        ],
        tags: ['庆祝', '成功', '快乐'],
        difficulty: 1.5,
        estimatedDuration: 20,
        createdAt: DateTime.now(),
      ),

      ConversationScenario(
        id: 'scenario_storytelling',
        name: '故事分享',
        description: '角色们轮流分享有趣的故事',
        type: ScenarioType.storytelling,
        requiredCharacters: ['1'],
        optionalCharacters: ['2', '3', '4'],
        steps: [
          ScenarioStep(
            order: 1,
            type: StepType.introduction,
            characterId: '1',
            content: '我想和大家分享一个有趣的故事...',
            probability: 1.0,
          ),
          ScenarioStep(
            order: 2,
            type: StepType.narration,
            characterId: '1',
            content: '从前有一个...',
            probability: 0.9,
          ),
          ScenarioStep(
            order: 3,
            type: StepType.pause,
            content: '等待反应',
            parameters: {'duration': 2000},
            probability: 0.7,
          ),
          ScenarioStep(
            order: 4,
            type: StepType.question,
            characterId: '2',
            content: '然后呢？发生了什么？',
            probability: 0.8,
          ),
        ],
        triggers: [
          ScenarioTrigger(
            id: 'trigger_story',
            type: TriggerType.eventBasedTrigger,
            conditions: {
              'keywords': ['故事', '分享', '从前', '有趣'],
            },
            probability: 0.5,
          ),
        ],
        tags: ['故事', '分享', '娱乐'],
        difficulty: 1.8,
        estimatedDuration: 30,
        createdAt: DateTime.now(),
      ),
    ];

    for (final scenario in defaultScenarios) {
      await saveConversationScenario(scenario);
    }
  }
}

// Provider
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});
