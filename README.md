# AIX Companion - AI虚拟伴侣应用

## 项目概述

AIX Companion 是一款基于Flutter的跨平台AI虚拟伴侣应用，旨在为用户提供个性化、智能化的情感陪伴体验。

## 技术栈

- **前端**: Flutter (iOS/Android/Web)
- **后端**: Node.js/Python FastAPI
- **数据库**: PostgreSQL + Redis
- **AI服务**: OpenAI API/本地LLM + 向量数据库
- **实时通信**: WebSocket
- **消息队列**: RabbitMQ/Apache Kafka

## 项目结构

```
AIXCompanion/
├── lib/                          # Flutter应用主代码
│   ├── modules/                  # 功能模块
│   │   ├── user_management/      # 用户管理模块
│   │   ├── character_management/ # 角色管理模块
│   │   ├── conversation_engine/  # 对话引擎模块
│   │   ├── relationship/         # 关系发展模块
│   │   ├── time_awareness/       # 时间感知模块
│   │   ├── multi_character/      # 多角色互动模块
│   │   ├── memory_system/        # 记忆系统模块
│   │   ├── emotion_computing/    # 情感计算模块
│   │   ├── messaging/            # 消息通信模块
│   │   └── notification/         # 通知系统模块
│   ├── services/                 # 服务层
│   ├── models/                   # 数据模型
│   ├── utils/                    # 工具类
│   ├── ui/                       # UI组件
│   │   ├── screens/              # 页面
│   │   ├── widgets/              # 组件
│   │   └── themes/               # 主题
│   └── main.dart                 # 应用入口
├── assets/                       # 资源文件
│   ├── images/                   # 图片资源
│   ├── fonts/                    # 字体文件
│   └── data/                     # 数据文件
├── test/                         # 测试文件
├── backend/                      # 后端代码
│   ├── api/                      # API接口
│   ├── models/                   # 数据模型
│   ├── services/                 # 业务逻辑
│   └── config/                   # 配置文件
├── docs/                         # 文档
└── .taskmaster/                  # TaskMaster AI项目管理
```

## 开发环境要求

### Flutter环境
- Flutter SDK 3.x 稳定版
- Dart SDK
- Android Studio / VS Code
- iOS开发需要Xcode (macOS)

### 后端环境
- Node.js 18+ 或 Python 3.9+
- PostgreSQL 14+
- Redis 6+

## 安装指南

### 1. 安装Flutter

#### Windows:
1. 下载Flutter SDK: https://flutter.dev/docs/get-started/install/windows
2. 解压到合适目录 (如 C:\flutter)
3. 添加到系统PATH: C:\flutter\bin
4. 运行 `flutter doctor` 检查环境

#### macOS:
```bash
# 使用Homebrew安装
brew install flutter

# 或手动下载安装
# 下载SDK并解压
# 添加到PATH: export PATH="$PATH:`pwd`/flutter/bin"
```

#### Linux:
```bash
# 下载并解压Flutter SDK
wget https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.x.x-stable.tar.xz
tar xf flutter_linux_3.x.x-stable.tar.xz

# 添加到PATH
export PATH="$PATH:`pwd`/flutter/bin"
```

### 2. 验证安装
```bash
flutter doctor
flutter --version
```

### 3. 创建项目
```bash
# 在当前目录创建Flutter项目
flutter create . --org com.aixcompanion --project-name aix_companion

# 安装依赖
flutter pub get

# 运行项目
flutter run
```

## 开发规范

- 使用Dart官方代码规范
- 遵循Flutter最佳实践
- 模块化开发，低耦合高内聚
- 完善的单元测试和集成测试

## 许可证

MIT License
