import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';
import '../../services/ai_service.dart';

class GroupChatService {
  final StorageService _storageService;
  final AIService _aiService;
  final Uuid _uuid = const Uuid();
  
  static const String _defaultUserId = 'default_user';

  GroupChatService(this._storageService, this._aiService);

  // 创建群组对话
  Future<GroupConversation> createGroupConversation({
    required List<String> characterIds,
    String? title,
    GroupConversationType type = GroupConversationType.casual,
  }) async {
    if (characterIds.length < 2) {
      throw Exception('群组对话至少需要2个角色');
    }

    final conversation = GroupConversation(
      id: _uuid.v4(),
      userId: _defaultUserId,
      title: title ?? _generateGroupTitle(characterIds),
      characterIds: characterIds,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      type: type,
      isActive: true,
    );

    await _storageService.saveGroupConversation(conversation);
    
    // 发送系统欢迎消息
    await _sendSystemMessage(
      conversation.id,
      '欢迎来到群聊！参与者：${await _getCharacterNames(characterIds)}',
    );

    // 让每个角色发送自我介绍
    await _sendIntroductionMessages(conversation.id, characterIds);

    return conversation;
  }

  // 生成群组标题
  Future<String> _generateGroupTitle(List<String> characterIds) async {
    final characters = <Character>[];
    for (final id in characterIds) {
      final character = await _storageService.getCharacter(id);
      if (character != null) characters.add(character);
    }
    
    if (characters.length == 2) {
      return '${characters[0].name} & ${characters[1].name}';
    } else if (characters.length == 3) {
      return '${characters[0].name}、${characters[1].name} & ${characters[2].name}';
    } else {
      return '${characters[0].name}等${characters.length}人的群聊';
    }
  }

  // 获取角色名称列表
  Future<String> _getCharacterNames(List<String> characterIds) async {
    final names = <String>[];
    for (final id in characterIds) {
      final character = await _storageService.getCharacter(id);
      if (character != null) names.add(character.name);
    }
    return names.join('、');
  }

  // 发送自我介绍消息
  Future<void> _sendIntroductionMessages(String groupId, List<String> characterIds) async {
    for (final characterId in characterIds) {
      final character = await _storageService.getCharacter(characterId);
      if (character != null) {
        final introduction = _generateIntroduction(character);
        await _sendCharacterMessage(groupId, characterId, introduction);
        
        // 添加延迟，让介绍更自然
        await Future.delayed(const Duration(seconds: 1));
      }
    }
  }

  // 生成角色介绍
  String _generateIntroduction(Character character) {
    final introductions = {
      'gentle': '大家好，我是${character.name}～很高兴认识大家呢！',
      'energetic': '嗨！我是${character.name}！很兴奋能和大家一起聊天！',
      'professional': '您好，我是${character.name}。期待与各位的交流。',
      'technical': 'Hello，我是${character.name}。准备开始有趣的讨论了！',
      'casual': '嗨，我是${character.name}，大家好！',
    };
    
    return introductions[character.speakingStyle] ?? introductions['casual']!;
  }

  // 发送用户消息到群组
  Future<GroupMessage> sendUserMessage({
    required String groupId,
    required String content,
    String? replyToMessageId,
    List<String> mentionedCharacterIds = const [],
  }) async {
    final message = GroupMessage(
      id: _uuid.v4(),
      groupConversationId: groupId,
      content: content,
      type: MessageType.text,
      sender: MessageSender.user,
      timestamp: DateTime.now(),
      replyToMessageId: replyToMessageId,
      mentionedCharacterIds: mentionedCharacterIds,
    );

    await _storageService.saveGroupMessage(message);
    await _updateGroupConversation(groupId, message);

    // 触发角色回复
    await _triggerCharacterResponses(groupId, message);

    return message;
  }

  // 发送角色消息
  Future<GroupMessage> _sendCharacterMessage(
    String groupId,
    String characterId,
    String content, {
    String? replyToMessageId,
    List<String> mentionedCharacterIds = const [],
  }) async {
    final message = GroupMessage(
      id: _uuid.v4(),
      groupConversationId: groupId,
      content: content,
      type: MessageType.text,
      sender: MessageSender.character,
      timestamp: DateTime.now(),
      characterId: characterId,
      replyToMessageId: replyToMessageId,
      mentionedCharacterIds: mentionedCharacterIds,
    );

    await _storageService.saveGroupMessage(message);
    await _updateGroupConversation(groupId, message);

    return message;
  }

  // 发送系统消息
  Future<GroupMessage> _sendSystemMessage(String groupId, String content) async {
    final message = GroupMessage(
      id: _uuid.v4(),
      groupConversationId: groupId,
      content: content,
      type: MessageType.system,
      sender: MessageSender.system,
      timestamp: DateTime.now(),
      isSystemMessage: true,
    );

    await _storageService.saveGroupMessage(message);
    await _updateGroupConversation(groupId, message);

    return message;
  }

  // 触发角色回复
  Future<void> _triggerCharacterResponses(String groupId, GroupMessage userMessage) async {
    final conversation = await _storageService.getGroupConversation(groupId);
    if (conversation == null) return;

    // 决定哪些角色会回复
    final respondingCharacters = await _selectRespondingCharacters(
      conversation,
      userMessage,
    );

    // 按照一定的顺序和延迟让角色回复
    for (int i = 0; i < respondingCharacters.length; i++) {
      final characterId = respondingCharacters[i];
      
      // 添加延迟，模拟思考时间
      await Future.delayed(Duration(seconds: 2 + i));
      
      await _generateCharacterResponse(groupId, characterId, userMessage);
    }
  }

  // 选择会回复的角色
  Future<List<String>> _selectRespondingCharacters(
    GroupConversation conversation,
    GroupMessage userMessage,
  ) async {
    final respondingCharacters = <String>[];
    
    // 如果用户@了特定角色，这些角色一定会回复
    if (userMessage.mentionedCharacterIds.isNotEmpty) {
      respondingCharacters.addAll(userMessage.mentionedCharacterIds);
    }
    
    // 根据角色性格和当前对话状态决定其他角色是否回复
    for (final characterId in conversation.characterIds) {
      if (!respondingCharacters.contains(characterId)) {
        final shouldRespond = await _shouldCharacterRespond(
          characterId,
          conversation,
          userMessage,
        );
        if (shouldRespond) {
          respondingCharacters.add(characterId);
        }
      }
    }
    
    // 限制同时回复的角色数量，避免过于混乱
    if (respondingCharacters.length > 3) {
      respondingCharacters.shuffle();
      return respondingCharacters.take(3).toList();
    }
    
    return respondingCharacters;
  }

  // 判断角色是否应该回复
  Future<bool> _shouldCharacterRespond(
    String characterId,
    GroupConversation conversation,
    GroupMessage userMessage,
  ) async {
    final character = await _storageService.getCharacter(characterId);
    if (character == null) return false;

    // 外向的角色更容易回复
    if (character.personality.extroversion > 0.7) {
      return true;
    }
    
    // 如果消息内容与角色兴趣相关
    final content = userMessage.content.toLowerCase();
    for (final interest in character.interests) {
      if (content.contains(interest.toLowerCase())) {
        return true;
      }
    }
    
    // 随机因素
    return DateTime.now().millisecond % 100 < 30; // 30% 概率
  }

  // 生成角色回复
  Future<void> _generateCharacterResponse(
    String groupId,
    String characterId,
    GroupMessage triggerMessage,
  ) async {
    try {
      final character = await _storageService.getCharacter(characterId);
      if (character == null) return;

      // 获取最近的群组消息作为上下文
      final recentMessages = await _storageService.getGroupMessages(groupId, limit: 10);
      
      // 生成回复内容
      final response = await _generateGroupResponse(
        character,
        triggerMessage,
        recentMessages,
      );

      // 发送回复
      await _sendCharacterMessage(groupId, characterId, response);
      
    } catch (e) {
      print('生成角色回复失败: $e');
      // 发送备用回复
      await _sendCharacterMessage(
        groupId,
        characterId,
        _getFallbackResponse(characterId),
      );
    }
  }

  // 生成群组回复
  Future<String> _generateGroupResponse(
    Character character,
    GroupMessage triggerMessage,
    List<GroupMessage> recentMessages,
  ) async {
    // 构建群组对话的提示词
    final prompt = _buildGroupPrompt(character, triggerMessage, recentMessages);
    
    // 这里可以调用AI服务生成回复
    // 暂时使用简单的回复生成
    return _generateSimpleGroupResponse(character, triggerMessage.content);
  }

  // 构建群组提示词
  String _buildGroupPrompt(
    Character character,
    GroupMessage triggerMessage,
    List<GroupMessage> recentMessages,
  ) {
    final context = recentMessages.map((msg) {
      final senderName = msg.sender == MessageSender.user 
          ? '用户' 
          : msg.characterId != null 
              ? '角色${msg.characterId}' 
              : '系统';
      return '$senderName: ${msg.content}';
    }).join('\n');

    return '''
你是${character.name}，正在参与一个群聊。

角色设定：
- 性格：${character.description}
- 特点：${character.traits.join('、')}
- 说话风格：${character.speakingStyle}

最近的对话：
$context

用户刚刚说：${triggerMessage.content}

请以${character.name}的身份在群聊中回复，保持角色特色，回复要简洁自然。
''';
  }

  // 简单的群组回复生成
  String _generateSimpleGroupResponse(Character character, String userMessage) {
    final responses = _getGroupResponsesByCharacter(character.id);
    final randomIndex = DateTime.now().millisecond % responses.length;
    return responses[randomIndex];
  }

  // 根据角色获取群组回复
  List<String> _getGroupResponsesByCharacter(String characterId) {
    switch (characterId) {
      case '1': // 小雨
        return [
          '我也觉得是这样呢～',
          '这个想法很有趣！',
          '大家都说得很好呢',
          '我同意这个观点',
          '让我想想...',
        ];
      case '2': // 阿凯
        return [
          '哈哈，说得对！',
          '我也是这么想的！',
          '这个话题很棒！',
          '大家都很厉害啊！',
          '我来补充一下...',
        ];
      case '3': // 小雪
        return [
          '从理性角度来看，这很有道理。',
          '我认为这个分析很到位。',
          '这确实是个值得思考的问题。',
          '大家的观点都很有见地。',
          '让我分享一下我的看法...',
        ];
      case '4': // 小明
        return [
          '从技术角度来说...',
          '这让我想到了一个算法问题',
          '有意思的观点！',
          '我觉得可以这样优化...',
          '这个逻辑很清晰',
        ];
      default:
        return [
          '我觉得很有道理',
          '这个想法不错',
          '我同意',
          '说得好',
          '让我想想...',
        ];
    }
  }

  // 备用回复
  String _getFallbackResponse(String characterId) {
    final responses = _getGroupResponsesByCharacter(characterId);
    return responses.first;
  }

  // 更新群组对话
  Future<void> _updateGroupConversation(String groupId, GroupMessage message) async {
    final conversation = await _storageService.getGroupConversation(groupId);
    if (conversation != null) {
      final updatedConversation = conversation.copyWith(
        updatedAt: DateTime.now(),
        lastMessageId: message.id,
        lastMessagePreview: message.content,
        messageCount: conversation.messageCount + 1,
        messageIds: [...conversation.messageIds, message.id],
      );
      await _storageService.saveGroupConversation(updatedConversation);
    }
  }

  // 获取群组对话列表
  Future<List<GroupConversation>> getGroupConversations() async {
    return await _storageService.getGroupConversations(_defaultUserId);
  }

  // 获取群组消息
  Future<List<GroupMessage>> getGroupMessages(String groupId) async {
    return await _storageService.getGroupMessages(groupId);
  }

  // 添加角色到群组
  Future<void> addCharacterToGroup(String groupId, String characterId) async {
    final conversation = await _storageService.getGroupConversation(groupId);
    if (conversation != null && !conversation.characterIds.contains(characterId)) {
      final updatedConversation = conversation.copyWith(
        characterIds: [...conversation.characterIds, characterId],
        updatedAt: DateTime.now(),
      );
      await _storageService.saveGroupConversation(updatedConversation);
      
      // 发送系统消息
      final character = await _storageService.getCharacter(characterId);
      if (character != null) {
        await _sendSystemMessage(groupId, '${character.name} 加入了群聊');
        await _sendCharacterMessage(groupId, characterId, _generateIntroduction(character));
      }
    }
  }

  // 从群组移除角色
  Future<void> removeCharacterFromGroup(String groupId, String characterId) async {
    final conversation = await _storageService.getGroupConversation(groupId);
    if (conversation != null && conversation.characterIds.contains(characterId)) {
      final updatedCharacterIds = conversation.characterIds.where((id) => id != characterId).toList();
      
      if (updatedCharacterIds.length < 2) {
        throw Exception('群组至少需要保留2个角色');
      }
      
      final updatedConversation = conversation.copyWith(
        characterIds: updatedCharacterIds,
        updatedAt: DateTime.now(),
      );
      await _storageService.saveGroupConversation(updatedConversation);
      
      // 发送系统消息
      final character = await _storageService.getCharacter(characterId);
      if (character != null) {
        await _sendSystemMessage(groupId, '${character.name} 离开了群聊');
      }
    }
  }
}

// Provider
final groupChatServiceProvider = Provider<GroupChatService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  final aiService = ref.watch(aiServiceProvider);
  return GroupChatService(storageService, aiService);
});
