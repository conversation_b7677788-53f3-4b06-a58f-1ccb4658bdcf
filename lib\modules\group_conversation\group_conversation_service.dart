import 'dart:async';
import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';
import '../ai_service/ai_service.dart';
import '../character_interaction/interaction_engine.dart';

class GroupConversationService {
  final StorageService _storageService;
  final AIService _aiService;
  final InteractionEngine _interactionEngine;
  final Uuid _uuid = const Uuid();
  final Random _random = Random();
  
  static const String _defaultUserId = 'default_user';
  
  // 活跃的群组对话
  final Map<String, GroupConversation> _activeGroups = {};
  
  // 场景执行器
  final Map<String, ScenarioExecution> _activeExecutions = {};
  
  // 事件流控制器
  final StreamController<GroupEvent> _eventController = StreamController.broadcast();
  
  Stream<GroupEvent> get eventStream => _eventController.stream;

  GroupConversationService(this._storageService, this._aiService, this._interactionEngine);

  // 创建群组对话
  Future<GroupConversation> createGroup({
    required String name,
    required String description,
    required List<String> participantIds,
    required GroupType type,
    GroupSettings? settings,
    List<String>? tags,
  }) async {
    final groupId = _uuid.v4();
    
    // 设置成员角色
    final memberRoles = <String, GroupRole>{};
    final joinTimes = <String, DateTime>{};
    final now = DateTime.now();
    
    for (int i = 0; i < participantIds.length; i++) {
      final participantId = participantIds[i];
      memberRoles[participantId] = i == 0 ? GroupRole.owner : GroupRole.member;
      joinTimes[participantId] = now;
    }

    final group = GroupConversation(
      id: groupId,
      name: name,
      description: description,
      participantIds: participantIds,
      creatorId: participantIds.first,
      type: type,
      createdAt: now,
      lastActivity: now,
      memberRoles: memberRoles,
      joinTimes: joinTimes,
      settings: settings ?? const GroupSettings(),
      tags: tags ?? [],
    );

    await _storageService.saveGroupConversation(group);
    _activeGroups[groupId] = group;
    
    // 触发群组创建事件
    await _triggerGroupEvent(
      groupId: groupId,
      type: GroupEventType.conversationStarted,
      description: '群组对话创建',
    );

    return group;
  }

  // 加入群组
  Future<void> joinGroup(String groupId, String participantId) async {
    final group = await _getGroup(groupId);
    if (group == null) throw Exception('群组不存在');

    if (group.participantIds.contains(participantId)) {
      throw Exception('用户已在群组中');
    }

    if (group.participantIds.length >= group.settings.maxMembers) {
      throw Exception('群组已满');
    }

    final updatedGroup = group.copyWith(
      participantIds: [...group.participantIds, participantId],
      memberRoles: {
        ...group.memberRoles,
        participantId: GroupRole.member,
      },
      joinTimes: {
        ...group.joinTimes,
        participantId: DateTime.now(),
      },
      lastActivity: DateTime.now(),
    );

    await _storageService.saveGroupConversation(updatedGroup);
    _activeGroups[groupId] = updatedGroup;

    await _triggerGroupEvent(
      groupId: groupId,
      type: GroupEventType.memberJoined,
      actorId: participantId,
      description: '成员加入群组',
    );
  }

  // 离开群组
  Future<void> leaveGroup(String groupId, String participantId) async {
    final group = await _getGroup(groupId);
    if (group == null) throw Exception('群组不存在');

    if (!group.participantIds.contains(participantId)) {
      throw Exception('用户不在群组中');
    }

    final updatedParticipants = group.participantIds.where((id) => id != participantId).toList();
    final updatedRoles = Map<String, GroupRole>.from(group.memberRoles)..remove(participantId);
    final updatedJoinTimes = Map<String, DateTime>.from(group.joinTimes)..remove(participantId);

    final updatedGroup = group.copyWith(
      participantIds: updatedParticipants,
      memberRoles: updatedRoles,
      joinTimes: updatedJoinTimes,
      lastActivity: DateTime.now(),
    );

    await _storageService.saveGroupConversation(updatedGroup);
    
    if (updatedParticipants.isEmpty) {
      _activeGroups.remove(groupId);
    } else {
      _activeGroups[groupId] = updatedGroup;
    }

    await _triggerGroupEvent(
      groupId: groupId,
      type: GroupEventType.memberLeft,
      actorId: participantId,
      description: '成员离开群组',
    );
  }

  // 发送群组消息
  Future<List<Message>> sendGroupMessage({
    required String groupId,
    required String senderId,
    required String content,
    Map<String, dynamic>? metadata,
  }) async {
    final group = await _getGroup(groupId);
    if (group == null) throw Exception('群组不存在');

    if (!group.participantIds.contains(senderId)) {
      throw Exception('发送者不在群组中');
    }

    // 检查消息过滤
    if (!_isMessageAllowed(content, group.settings.messageFilter)) {
      throw Exception('消息不符合群组规则');
    }

    // 创建用户消息
    final userMessage = Message(
      id: _uuid.v4(),
      conversationId: groupId,
      senderId: senderId,
      content: content,
      timestamp: DateTime.now(),
      type: MessageType.text,
      metadata: metadata ?? {},
    );

    await _storageService.saveMessage(userMessage);

    // 更新群组信息
    final updatedGroup = group.copyWith(
      messageIds: [...group.messageIds, userMessage.id],
      messageCount: group.messageCount + 1,
      lastActivity: DateTime.now(),
      lastSeenTimes: {
        ...group.lastSeenTimes,
        senderId: DateTime.now(),
      },
    );

    await _storageService.saveGroupConversation(updatedGroup);
    _activeGroups[groupId] = updatedGroup;

    // 启动互动上下文
    final interactionContext = await _interactionEngine.startInteraction(
      characterIds: group.participantIds,
      type: InteractionType.groupChat,
      initialContext: {
        'groupId': groupId,
        'groupType': group.type.name,
        'messageContent': content,
      },
    );

    // 生成AI角色响应
    final responses = await _interactionEngine.processMessage(
      interactionId: interactionContext.id,
      senderId: senderId,
      content: content,
      metadata: metadata,
    );

    // 保存AI响应消息
    for (final response in responses) {
      await _storageService.saveMessage(response);
      
      final finalGroup = await _getGroup(groupId);
      if (finalGroup != null) {
        final updatedFinalGroup = finalGroup.copyWith(
          messageIds: [...finalGroup.messageIds, response.id],
          messageCount: finalGroup.messageCount + 1,
          lastActivity: DateTime.now(),
        );
        
        await _storageService.saveGroupConversation(updatedFinalGroup);
        _activeGroups[groupId] = updatedFinalGroup;
      }
    }

    // 检查是否需要触发场景
    await _checkScenarioTriggers(groupId, content, senderId);

    return [userMessage, ...responses];
  }

  // 执行对话场景
  Future<ScenarioExecution> executeScenario({
    required String scenarioId,
    required String groupId,
    Map<String, dynamic>? initialContext,
  }) async {
    final scenario = await _storageService.getConversationScenario(scenarioId);
    if (scenario == null) throw Exception('场景不存在');

    final group = await _getGroup(groupId);
    if (group == null) throw Exception('群组不存在');

    // 检查必需角色
    for (final requiredCharacter in scenario.requiredCharacters) {
      if (!group.participantIds.contains(requiredCharacter)) {
        throw Exception('缺少必需角色: $requiredCharacter');
      }
    }

    final executionId = _uuid.v4();
    final execution = ScenarioExecution(
      id: executionId,
      scenarioId: scenarioId,
      groupId: groupId,
      startTime: DateTime.now(),
      context: {
        ...scenario.initialContext,
        ...initialContext ?? {},
        'groupInfo': group.toJson(),
      },
    );

    await _storageService.saveScenarioExecution(execution);
    _activeExecutions[executionId] = execution;

    // 开始执行场景步骤
    await _executeScenarioSteps(execution, scenario);

    return execution;
  }

  // 执行场景步骤
  Future<void> _executeScenarioSteps(ScenarioExecution execution, ConversationScenario scenario) async {
    for (int i = 0; i < scenario.steps.length; i++) {
      final step = scenario.steps[i];
      
      // 检查执行状态
      final currentExecution = _activeExecutions[execution.id];
      if (currentExecution?.status != ExecutionStatus.running) {
        break;
      }

      // 检查步骤条件
      if (!_checkStepConditions(step, execution)) {
        continue;
      }

      // 检查概率
      if (_random.nextDouble() > step.probability) {
        continue;
      }

      // 执行步骤
      await _executeStep(step, execution);
      
      // 更新执行进度
      final updatedExecution = execution.copyWith(currentStep: i + 1);
      _activeExecutions[execution.id] = updatedExecution;
      await _storageService.saveScenarioExecution(updatedExecution);

      // 添加延迟
      if (step.maxDelay > 0) {
        final delay = step.minDelay + _random.nextInt(step.maxDelay - step.minDelay);
        await Future.delayed(Duration(milliseconds: delay));
      }
    }

    // 完成场景执行
    await _completeScenarioExecution(execution.id);
  }

  // 执行单个步骤
  Future<void> _executeStep(ScenarioStep step, ScenarioExecution execution) async {
    switch (step.type) {
      case StepType.introduction:
        await _executeIntroductionStep(step, execution);
        break;
      case StepType.question:
        await _executeQuestionStep(step, execution);
        break;
      case StepType.response:
        await _executeResponseStep(step, execution);
        break;
      case StepType.action:
        await _executeActionStep(step, execution);
        break;
      case StepType.emotion:
        await _executeEmotionStep(step, execution);
        break;
      case StepType.pause:
        await _executePauseStep(step, execution);
        break;
      case StepType.transition:
        await _executeTransitionStep(step, execution);
        break;
      case StepType.conclusion:
        await _executeConclusionStep(step, execution);
        break;
      case StepType.choice:
        await _executeChoiceStep(step, execution);
        break;
      case StepType.reaction:
        await _executeReactionStep(step, execution);
        break;
      case StepType.narration:
        await _executeNarrationStep(step, execution);
        break;
    }

    // 记录步骤执行事件
    await _recordExecutionEvent(
      execution.id,
      ExecutionEventType.stepCompleted,
      {'stepOrder': step.order, 'stepType': step.type.name},
    );
  }

  // 执行介绍步骤
  Future<void> _executeIntroductionStep(ScenarioStep step, ScenarioExecution execution) async {
    if (step.characterId != null) {
      await _sendScenarioMessage(
        groupId: execution.groupId,
        senderId: step.characterId!,
        content: step.content,
        isSystemMessage: true,
      );
    }
  }

  // 执行提问步骤
  Future<void> _executeQuestionStep(ScenarioStep step, ScenarioExecution execution) async {
    if (step.characterId != null) {
      await _sendScenarioMessage(
        groupId: execution.groupId,
        senderId: step.characterId!,
        content: step.content,
        isSystemMessage: false,
      );
    }
  }

  // 执行回应步骤
  Future<void> _executeResponseStep(ScenarioStep step, ScenarioExecution execution) async {
    // 生成基于上下文的回应
    final context = execution.context;
    final response = await _generateContextualResponse(step.content, context);
    
    if (step.characterId != null) {
      await _sendScenarioMessage(
        groupId: execution.groupId,
        senderId: step.characterId!,
        content: response,
        isSystemMessage: false,
      );
    }
  }

  // 发送场景消息
  Future<void> _sendScenarioMessage({
    required String groupId,
    required String senderId,
    required String content,
    bool isSystemMessage = false,
  }) async {
    final message = Message(
      id: _uuid.v4(),
      conversationId: groupId,
      senderId: senderId,
      content: content,
      timestamp: DateTime.now(),
      type: isSystemMessage ? MessageType.system : MessageType.text,
      metadata: {'isScenarioMessage': true},
    );

    await _storageService.saveMessage(message);

    // 更新群组消息列表
    final group = await _getGroup(groupId);
    if (group != null) {
      final updatedGroup = group.copyWith(
        messageIds: [...group.messageIds, message.id],
        messageCount: group.messageCount + 1,
        lastActivity: DateTime.now(),
      );
      
      await _storageService.saveGroupConversation(updatedGroup);
      _activeGroups[groupId] = updatedGroup;
    }
  }

  // 生成上下文相关的回应
  Future<String> _generateContextualResponse(String template, Map<String, dynamic> context) async {
    // 简化的模板替换
    String response = template;
    
    context.forEach((key, value) {
      response = response.replaceAll('{$key}', value.toString());
    });
    
    return response;
  }

  // 检查步骤条件
  bool _checkStepConditions(ScenarioStep step, ScenarioExecution execution) {
    for (final condition in step.conditions) {
      if (!_evaluateStepCondition(condition, execution)) {
        return false;
      }
    }
    return true;
  }

  // 评估步骤条件
  bool _evaluateStepCondition(String condition, ScenarioExecution execution) {
    // 简化的条件评估
    final context = execution.context;
    
    if (condition.startsWith('has_')) {
      final key = condition.substring(4);
      return context.containsKey(key);
    }
    
    if (condition.startsWith('count_')) {
      final parts = condition.split('_');
      if (parts.length >= 3) {
        final key = parts[1];
        final expectedCount = int.tryParse(parts[2]) ?? 0;
        final actualCount = context[key] as int? ?? 0;
        return actualCount >= expectedCount;
      }
    }
    
    return true;
  }

  // 其他步骤执行方法的简化实现
  Future<void> _executeActionStep(ScenarioStep step, ScenarioExecution execution) async {
    // 执行动作步骤
  }

  Future<void> _executeEmotionStep(ScenarioStep step, ScenarioExecution execution) async {
    // 执行情感步骤
  }

  Future<void> _executePauseStep(ScenarioStep step, ScenarioExecution execution) async {
    final pauseDuration = step.parameters['duration'] as int? ?? 1000;
    await Future.delayed(Duration(milliseconds: pauseDuration));
  }

  Future<void> _executeTransitionStep(ScenarioStep step, ScenarioExecution execution) async {
    // 执行过渡步骤
  }

  Future<void> _executeConclusionStep(ScenarioStep step, ScenarioExecution execution) async {
    // 执行结论步骤
  }

  Future<void> _executeChoiceStep(ScenarioStep step, ScenarioExecution execution) async {
    // 执行选择步骤
  }

  Future<void> _executeReactionStep(ScenarioStep step, ScenarioExecution execution) async {
    // 执行反应步骤
  }

  Future<void> _executeNarrationStep(ScenarioStep step, ScenarioExecution execution) async {
    // 执行旁白步骤
  }

  // 检查场景触发器
  Future<void> _checkScenarioTriggers(String groupId, String content, String senderId) async {
    final scenarios = await _storageService.getConversationScenarios();
    
    for (final scenario in scenarios) {
      if (!scenario.isActive) continue;
      
      for (final trigger in scenario.triggers) {
        if (_shouldTriggerScenario(trigger, groupId, content, senderId)) {
          if (_random.nextDouble() <= trigger.probability) {
            await executeScenario(scenarioId: scenario.id, groupId: groupId);
            break;
          }
        }
      }
    }
  }

  // 检查是否应该触发场景
  bool _shouldTriggerScenario(ScenarioTrigger trigger, String groupId, String content, String senderId) {
    switch (trigger.type) {
      case TriggerType.eventBasedTrigger:
        final keywords = trigger.conditions['keywords'] as List<String>? ?? [];
        return keywords.any((keyword) => content.toLowerCase().contains(keyword.toLowerCase()));
      case TriggerType.emotionBasedTrigger:
        // 简化的情感检测
        final emotions = trigger.conditions['emotions'] as List<String>? ?? [];
        return emotions.any((emotion) => content.toLowerCase().contains(emotion.toLowerCase()));
      case TriggerType.contextBasedTrigger:
        // 上下文触发检查
        return true;
      case TriggerType.userActionTrigger:
        // 用户行为触发检查
        return true;
      case TriggerType.randomTrigger:
        return true;
      case TriggerType.timeBasedTrigger:
        // 时间触发检查
        return true;
    }
  }

  // 完成场景执行
  Future<void> _completeScenarioExecution(String executionId) async {
    final execution = _activeExecutions[executionId];
    if (execution == null) return;

    final completedExecution = execution.copyWith(
      status: ExecutionStatus.completed,
      endTime: DateTime.now(),
    );

    await _storageService.saveScenarioExecution(completedExecution);
    _activeExecutions.remove(executionId);

    await _recordExecutionEvent(
      executionId,
      ExecutionEventType.stepCompleted,
      {'status': 'completed'},
    );
  }

  // 记录执行事件
  Future<void> _recordExecutionEvent(
    String executionId,
    ExecutionEventType type,
    Map<String, dynamic> data,
  ) async {
    final event = ExecutionEvent(
      id: _uuid.v4(),
      executionId: executionId,
      type: type,
      timestamp: DateTime.now(),
      data: data,
    );

    await _storageService.saveExecutionEvent(event);
  }

  // 触发群组事件
  Future<void> _triggerGroupEvent({
    required String groupId,
    required GroupEventType type,
    String? actorId,
    String? targetId,
    Map<String, dynamic>? data,
    String? description,
  }) async {
    final event = GroupEvent(
      id: _uuid.v4(),
      groupId: groupId,
      type: type,
      timestamp: DateTime.now(),
      actorId: actorId,
      targetId: targetId,
      data: data ?? {},
      description: description,
    );

    await _storageService.saveGroupEvent(event);
    _eventController.add(event);
  }

  // 检查消息是否允许
  bool _isMessageAllowed(String content, MessageFilter filter) {
    if (!filter.allowText && content.isNotEmpty) return false;
    if (content.length > filter.maxMessageLength) return false;
    
    // 简化的垃圾信息检测
    if (filter.enableSpamFilter) {
      final spamKeywords = ['spam', '广告', '推广'];
      if (spamKeywords.any((keyword) => content.toLowerCase().contains(keyword))) {
        return false;
      }
    }
    
    return true;
  }

  // 获取群组
  Future<GroupConversation?> _getGroup(String groupId) async {
    if (_activeGroups.containsKey(groupId)) {
      return _activeGroups[groupId];
    }
    
    final group = await _storageService.getGroupConversation(groupId);
    if (group != null) {
      _activeGroups[groupId] = group;
    }
    
    return group;
  }

  // 获取群组列表
  Future<List<GroupConversation>> getGroups({
    String? userId,
    GroupType? type,
    GroupStatus? status,
  }) async {
    return await _storageService.getGroupConversations(
      userId: userId,
      type: type,
      status: status,
    );
  }

  // 获取群组消息
  Future<List<Message>> getGroupMessages(String groupId, {int? limit, int? offset}) async {
    return await _storageService.getGroupMessages(groupId, limit: limit, offset: offset);
  }

  // 获取场景列表
  Future<List<ConversationScenario>> getScenarios({
    ScenarioType? type,
    List<String>? tags,
    double? minDifficulty,
    double? maxDifficulty,
  }) async {
    return await _storageService.getConversationScenarios(
      type: type,
      tags: tags,
      minDifficulty: minDifficulty,
      maxDifficulty: maxDifficulty,
    );
  }

  // 清理资源
  void dispose() {
    _eventController.close();
    _activeGroups.clear();
    _activeExecutions.clear();
  }
}

// Provider
final groupConversationServiceProvider = Provider<GroupConversationService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  final aiService = ref.watch(aiServiceProvider);
  final interactionEngine = ref.watch(interactionEngineProvider);
  return GroupConversationService(storageService, aiService, interactionEngine);
});
