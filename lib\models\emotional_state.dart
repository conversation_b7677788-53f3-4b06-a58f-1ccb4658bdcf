import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'emotional_state.freezed.dart';
part 'emotional_state.g.dart';

@freezed
@HiveType(typeId: 27)
class EmotionalState with _$EmotionalState {
  const factory EmotionalState({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required DateTime timestamp,
    @HiveField(4) required Map<EmotionType, double> emotions,
    @HiveField(5) required double valence, // 情感效价 (-1到1，负面到正面)
    @HiveField(6) required double arousal, // 情感唤醒度 (0到1，平静到激动)
    @HiveField(7) required EmotionIntensity intensity,
    @HiveField(8) required String triggerMessageId,
    @HiveField(9) String? dominantEmotion,
    @HiveField(10) @Default([]) List<String> emotionalKeywords,
    @HiveField(11) @Default(1.0) double confidence,
    @HiveField(12) Map<String, dynamic>? context,
  }) = _EmotionalState;

  factory EmotionalState.fromJson(Map<String, dynamic> json) => _$EmotionalStateFromJson(json);
}

@HiveType(typeId: 28)
enum EmotionType {
  @HiveField(0)
  joy,        // 快乐
  @HiveField(1)
  sadness,    // 悲伤
  @HiveField(2)
  anger,      // 愤怒
  @HiveField(3)
  fear,       // 恐惧
  @HiveField(4)
  surprise,   // 惊讶
  @HiveField(5)
  disgust,    // 厌恶
  @HiveField(6)
  trust,      // 信任
  @HiveField(7)
  anticipation, // 期待
  @HiveField(8)
  love,       // 爱
  @HiveField(9)
  excitement, // 兴奋
  @HiveField(10)
  anxiety,    // 焦虑
  @HiveField(11)
  frustration, // 沮丧
  @HiveField(12)
  contentment, // 满足
  @HiveField(13)
  loneliness,  // 孤独
  @HiveField(14)
  gratitude,   // 感激
  @HiveField(15)
  confusion,   // 困惑
}

@HiveType(typeId: 29)
enum EmotionIntensity {
  @HiveField(0)
  low,      // 低强度
  @HiveField(1)
  medium,   // 中等强度
  @HiveField(2)
  high,     // 高强度
  @HiveField(3)
  extreme,  // 极高强度
}

@freezed
@HiveType(typeId: 30)
class EmotionalPattern with _$EmotionalPattern {
  const factory EmotionalPattern({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required DateTime detectedAt,
    @HiveField(4) required PatternType type,
    @HiveField(5) required String description,
    @HiveField(6) required List<String> emotionalStateIds,
    @HiveField(7) required double strength,
    @HiveField(8) required Duration timeSpan,
    @HiveField(9) @Default([]) List<String> triggers,
    @HiveField(10) Map<String, dynamic>? metadata,
  }) = _EmotionalPattern;

  factory EmotionalPattern.fromJson(Map<String, dynamic> json) => _$EmotionalPatternFromJson(json);
}

@HiveType(typeId: 31)
enum PatternType {
  @HiveField(0)
  moodSwing,      // 情绪波动
  @HiveField(1)
  emotionalCycle, // 情感周期
  @HiveField(2)
  stressPattern,  // 压力模式
  @HiveField(3)
  happinessSpike, // 快乐高峰
  @HiveField(4)
  sadnessDepth,   // 悲伤低谷
  @HiveField(5)
  anxietyTrend,   // 焦虑趋势
  @HiveField(6)
  emotionalStability, // 情感稳定性
}

@freezed
@HiveType(typeId: 32)
class EmotionalInsight with _$EmotionalInsight {
  const factory EmotionalInsight({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required DateTime generatedAt,
    @HiveField(4) required InsightCategory category,
    @HiveField(5) required String title,
    @HiveField(6) required String description,
    @HiveField(7) required List<String> recommendations,
    @HiveField(8) required double confidence,
    @HiveField(9) @Default([]) List<String> supportingDataIds,
    @HiveField(10) @Default(false) bool isActionable,
    @HiveField(11) Map<String, dynamic>? actionData,
  }) = _EmotionalInsight;

  factory EmotionalInsight.fromJson(Map<String, dynamic> json) => _$EmotionalInsightFromJson(json);
}

@HiveType(typeId: 33)
enum InsightCategory {
  @HiveField(0)
  wellbeing,      // 心理健康
  @HiveField(1)
  communication,  // 沟通建议
  @HiveField(2)
  relationship,   // 关系改善
  @HiveField(3)
  selfCare,       // 自我关怀
  @HiveField(4)
  stressManagement, // 压力管理
  @HiveField(5)
  emotionalGrowth,  // 情感成长
}

@freezed
@HiveType(typeId: 34)
class MoodSummary with _$MoodSummary {
  const factory MoodSummary({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required DateTime date,
    @HiveField(4) required Map<EmotionType, double> averageEmotions,
    @HiveField(5) required double overallValence,
    @HiveField(6) required double overallArousal,
    @HiveField(7) required EmotionType dominantEmotion,
    @HiveField(8) required int totalInteractions,
    @HiveField(9) required List<String> emotionalHighlights,
    @HiveField(10) @Default([]) List<String> concerningPatterns,
    @HiveField(11) @Default([]) List<String> positivePatterns,
    @HiveField(12) Map<String, dynamic>? insights,
  }) = _MoodSummary;

  factory MoodSummary.fromJson(Map<String, dynamic> json) => _$MoodSummaryFromJson(json);
}

@freezed
@HiveType(typeId: 35)
class EmotionalTrigger with _$EmotionalTrigger {
  const factory EmotionalTrigger({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required String trigger,
    @HiveField(4) required TriggerType type,
    @HiveField(5) required EmotionType resultingEmotion,
    @HiveField(6) required double strength,
    @HiveField(7) required int occurrenceCount,
    @HiveField(8) required DateTime firstDetected,
    @HiveField(9) required DateTime lastOccurred,
    @HiveField(10) @Default([]) List<String> relatedMessageIds,
    @HiveField(11) String? description,
  }) = _EmotionalTrigger;

  factory EmotionalTrigger.fromJson(Map<String, dynamic> json) => _$EmotionalTriggerFromJson(json);
}

@HiveType(typeId: 36)
enum TriggerType {
  @HiveField(0)
  keyword,    // 关键词触发
  @HiveField(1)
  topic,      // 话题触发
  @HiveField(2)
  context,    // 上下文触发
  @HiveField(3)
  time,       // 时间触发
  @HiveField(4)
  interaction, // 互动方式触发
}
