import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/models.dart';
import '../../services/chat_service.dart';
import '../../services/ai_service.dart';
import '../../services/storage_service.dart';
import '../memory_system/memory_service.dart';
import '../relationship/intimacy_service.dart';

// 聊天状态
class ChatState {
  final List<Message> messages;
  final bool isLoading;
  final bool isTyping;
  final String? error;
  final Conversation? currentConversation;

  const ChatState({
    this.messages = const [],
    this.isLoading = false,
    this.isTyping = false,
    this.error,
    this.currentConversation,
  });

  ChatState copyWith({
    List<Message>? messages,
    bool? isLoading,
    bool? isTyping,
    String? error,
    Conversation? currentConversation,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      isTyping: isTyping ?? this.isTyping,
      error: error ?? this.error,
      currentConversation: currentConversation ?? this.currentConversation,
    );
  }
}

// 聊天状态管理器
class ChatNotifier extends StateNotifier<ChatState> {
  final ChatService _chatService;
  final AIService _aiService;
  final StorageService _storageService;
  final MemoryService _memoryService;
  final IntimacyService _intimacyService;
  final String characterId;
  Character? _character;
  static const String _defaultUserId = 'default_user'; // 临时用户ID，后续会被真实用户系统替换

  ChatNotifier(this._chatService, this._aiService, this._storageService, this._memoryService, this._intimacyService, this.characterId)
      : super(const ChatState()) {
    _initializeChat();
  }

  // 初始化聊天
  Future<void> _initializeChat() async {
    state = state.copyWith(isLoading: true);

    try {
      // 加载角色信息
      _character = await _storageService.getCharacter(characterId);
      if (_character == null) {
        throw Exception('角色不存在');
      }

      // 获取或创建对话
      final conversations = await _chatService.getCharacterConversations(characterId);
      Conversation conversation;

      if (conversations.isEmpty) {
        conversation = await _chatService.createConversation(characterId: characterId);
        // 发送欢迎消息
        await _sendWelcomeMessage(conversation.id);
      } else {
        conversation = conversations.first;
      }

      // 加载消息
      final messages = await _chatService.getConversationMessages(conversation.id);

      state = state.copyWith(
        currentConversation: conversation,
        messages: messages,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  // 发送欢迎消息
  Future<void> _sendWelcomeMessage(String conversationId) async {
    final welcomeMessages = {
      '1': '你好！我是小雨，很高兴认识你！有什么想聊的吗？',
      '2': '嗨！我是阿凯，今天过得怎么样？',
      '3': '您好，我是小雪。有什么可以帮助您的吗？',
      '4': 'Hi！我是小明，对什么技术话题感兴趣？',
    };

    final welcomeContent = welcomeMessages[characterId] ?? '你好！很高兴认识你！';
    
    await _chatService.receiveMessage(
      conversationId: conversationId,
      content: welcomeContent,
      characterId: characterId,
      type: MessageType.greeting,
    );
  }

  // 发送消息
  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty || state.currentConversation == null) return;

    try {
      // 发送用户消息
      final userMessage = await _chatService.sendMessage(
        conversationId: state.currentConversation!.id,
        content: content.trim(),
        characterId: characterId,
      );

      // 更新状态
      state = state.copyWith(
        messages: [...state.messages, userMessage],
        isTyping: true,
      );

      // 从用户消息中提取记忆
      await _extractMemoriesFromMessage(userMessage);

      // 更新亲密度
      await _updateIntimacy(userMessage);

      // 模拟AI回复延迟
      await Future.delayed(const Duration(seconds: 1, milliseconds: 500));

      // 获取相关记忆来增强AI回复
      final relevantMemories = await _memoryService.getRelevantMemories(
        characterId: characterId,
        userId: _defaultUserId,
        query: content,
        limit: 5,
      );

      // 生成AI回复
      final aiResponse = await _generateAIResponse(content, relevantMemories);
      final aiMessage = await _chatService.receiveMessage(
        conversationId: state.currentConversation!.id,
        content: aiResponse,
        characterId: characterId,
      );

      // 更新状态
      state = state.copyWith(
        messages: [...state.messages, aiMessage],
        isTyping: false,
      );

    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isTyping: false,
      );
    }
  }

  // 从用户消息中提取记忆
  Future<void> _extractMemoriesFromMessage(Message message) async {
    try {
      await _memoryService.extractMemoriesFromConversation(
        characterId: characterId,
        userId: _defaultUserId,
        conversationId: state.currentConversation!.id,
        messages: [message],
      );
    } catch (e) {
      print('记忆提取失败: $e');
    }
  }

  // 更新亲密度
  Future<void> _updateIntimacy(Message message) async {
    try {
      await _intimacyService.updateIntimacy(
        characterId: characterId,
        interactionType: InteractionType.message,
        content: message.content,
      );
    } catch (e) {
      print('亲密度更新失败: $e');
    }
  }

  // AI回复生成（增强版，包含记忆上下文）
  Future<String> _generateAIResponse(String userMessage, List<Memory> relevantMemories) async {
    if (_character == null) {
      return '抱歉，我现在无法回复。';
    }

    try {
      // 构建包含记忆的对话历史
      final enhancedHistory = _buildEnhancedHistory(relevantMemories);

      // 使用AI服务生成回复
      final response = await _aiService.generateResponse(
        userMessage: userMessage,
        character: _character!,
        conversationHistory: enhancedHistory,
      );
      return response;
    } catch (e) {
      print('AI回复生成失败: $e');
      // 返回备用回复
      return _getFallbackResponse(userMessage);
    }
  }

  // 构建增强的对话历史（包含相关记忆）
  List<Message> _buildEnhancedHistory(List<Memory> relevantMemories) {
    final enhancedHistory = <Message>[...state.messages];

    // 如果有相关记忆，在历史开头添加记忆上下文
    if (relevantMemories.isNotEmpty) {
      final memoryContext = relevantMemories
          .map((memory) => '${memory.summary}: ${memory.content}')
          .join('\n');

      final contextMessage = Message(
        id: 'memory_context',
        conversationId: state.currentConversation!.id,
        content: '记忆上下文：\n$memoryContext',
        type: MessageType.system,
        sender: MessageSender.system,
        timestamp: DateTime.now(),
        characterId: characterId,
      );

      enhancedHistory.insert(0, contextMessage);
    }

    return enhancedHistory;
  }

  // 备用回复
  String _getFallbackResponse(String userMessage) {
    final responses = {
      '1': [ // 小雨的回复
        '我理解你的想法，这确实很有趣呢～',
        '谢谢你和我分享这些，我很开心能听到你的想法。',
        '这听起来很棒！你能告诉我更多吗？',
        '我觉得你说得很有道理，我也是这么想的。',
        '哇，这真的很有意思！我从来没有这样想过。',
      ],
      '2': [ // 阿凯的回复
        '哈哈，你说得对！我也觉得是这样。',
        '这个想法不错！我们可以一起讨论讨论。',
        '真的吗？那太酷了！',
        '我完全同意你的观点，兄弟！',
        '这让我想到了一个有趣的事情...',
      ],
      '3': [ // 小雪的回复
        '您的观点很有见地，我认为这值得深入思考。',
        '从理性的角度来看，这确实是一个不错的想法。',
        '我理解您的立场，这是一个很好的分析。',
        '根据我的理解，您说的很有道理。',
        '这是一个很专业的观点，我很赞同。',
      ],
      '4': [ // 小明的回复
        '从技术角度来看，这个想法很有创新性！',
        '这让我想到了最新的技术趋势...',
        '有意思！这个问题可以用编程的思维来解决。',
        '我觉得可以用算法来优化这个过程。',
        '这个概念在计算机科学中也有类似的应用。',
      ],
    };

    final characterResponses = responses[characterId] ?? responses['1']!;
    final randomIndex = DateTime.now().millisecond % characterResponses.length;
    return characterResponses[randomIndex];
  }

  // 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }

  // 标记消息为已读
  Future<void> markMessagesAsRead() async {
    for (final message in state.messages) {
      if (message.sender == MessageSender.character && 
          message.status != MessageStatus.read) {
        await _chatService.markMessageAsRead(message.id);
      }
    }
  }
}

// Provider
final chatProvider = StateNotifierProvider.family<ChatNotifier, ChatState, String>(
  (ref, characterId) {
    final chatService = ref.watch(chatServiceProvider);
    final aiService = ref.watch(aiServiceProvider);
    final storageService = ref.watch(storageServiceProvider);
    final memoryService = ref.watch(memoryServiceProvider);
    final intimacyService = ref.watch(intimacyServiceProvider);
    return ChatNotifier(chatService, aiService, storageService, memoryService, intimacyService, characterId);
  },
);
