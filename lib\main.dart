import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'ui/themes/app_theme.dart';
import 'ui/screens/splash_screen.dart';
import 'ui/screens/home_screen.dart';
import 'ui/screens/auth/login_screen.dart';
import 'ui/screens/auth/register_screen.dart';
import 'ui/screens/character/character_selection_screen.dart';
import 'ui/screens/character/character_customization_screen.dart';
import 'ui/screens/chat/chat_screen.dart';
import 'ui/screens/memory/memory_viewer_screen.dart';
import 'ui/screens/preference/user_preference_screen.dart';
import 'ui/screens/settings/time_awareness_settings_screen.dart';
import 'ui/screens/group_chat/group_list_screen.dart';
import 'ui/screens/group_chat/create_group_screen.dart';
import 'ui/screens/group_chat/group_chat_screen.dart';
import 'ui/screens/emotion/emotion_tracker_screen.dart';
import 'ui/screens/analytics/interaction_analysis_screen.dart';
import 'ui/screens/memory/memory_cluster_screen.dart';
import 'ui/screens/role_management/role_management_screen.dart';
import 'ui/screens/memory/advanced_memory_search_screen.dart';
import 'ui/screens/notification/notification_center_screen.dart';
import 'ui/screens/performance/performance_monitor_screen.dart';
import 'ui/screens/content/content_gallery_screen.dart';
import 'services/storage_service.dart';
import 'services/ai_service.dart';
import 'modules/time_awareness/time_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化存储服务
  final storageService = StorageService();
  await storageService.initialize();

  // 初始化AI服务
  final aiService = AIService();
  // 注意：这里需要配置Google API Key
  // 可以从环境变量或配置文件中读取
  const apiKey = String.fromEnvironment('GOOGLE_API_KEY', defaultValue: '');
  if (apiKey.isNotEmpty) {
    try {
      await aiService.initialize(apiKey);
    } catch (e) {
      print('AI服务初始化失败: $e');
    }
  }

  runApp(
    ProviderScope(
      overrides: [
        storageServiceProvider.overrideWithValue(storageService),
        aiServiceProvider.overrideWithValue(aiService),
      ],
      child: const AIXCompanionApp(),
    ),
  );
}

class AIXCompanionApp extends ConsumerStatefulWidget {
  const AIXCompanionApp({super.key});

  @override
  ConsumerState<AIXCompanionApp> createState() => _AIXCompanionAppState();
}

class _AIXCompanionAppState extends ConsumerState<AIXCompanionApp> {
  @override
  void initState() {
    super.initState();
    // 启动时间感知服务
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(timeAwarenessProvider.notifier).startTimeAwareness();
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'AIX Companion',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: _router,
      debugShowCheckedModeBanner: false,
    );
  }
}

final GoRouter _router = GoRouter(
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: '/home',
      builder: (context, state) => const HomeScreen(),
    ),
    GoRoute(
      path: '/login',
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: '/register',
      builder: (context, state) => const RegisterScreen(),
    ),
    GoRoute(
      path: '/character-selection',
      builder: (context, state) => const CharacterSelectionScreen(),
    ),
    GoRoute(
      path: '/character-customization/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return CharacterCustomizationScreen(characterId: characterId);
      },
    ),
    GoRoute(
      path: '/memory/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return MemoryViewerScreen(characterId: characterId);
      },
    ),
    GoRoute(
      path: '/preference/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return UserPreferenceScreen(characterId: characterId);
      },
    ),
    GoRoute(
      path: '/emotion/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return EmotionTrackerScreen(characterId: characterId);
      },
    ),
    GoRoute(
      path: '/analysis/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return InteractionAnalysisScreen(characterId: characterId);
      },
    ),
    GoRoute(
      path: '/memory-cluster/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return MemoryClusterScreen(characterId: characterId);
      },
    ),
    GoRoute(
      path: '/role-management',
      builder: (context, state) => const RoleManagementScreen(),
    ),
    GoRoute(
      path: '/advanced-memory-search/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return AdvancedMemorySearchScreen(characterId: characterId);
      },
    ),
    GoRoute(
      path: '/notification-center',
      builder: (context, state) => const NotificationCenterScreen(),
    ),
    GoRoute(
      path: '/performance-monitor',
      builder: (context, state) => const PerformanceMonitorScreen(),
    ),
    GoRoute(
      path: '/content-gallery/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return ContentGalleryScreen(characterId: characterId);
      },
    ),
    GoRoute(
      path: '/time-settings',
      builder: (context, state) => const TimeAwarenessSettingsScreen(),
    ),
    GoRoute(
      path: '/group-list',
      builder: (context, state) => const GroupListScreen(),
    ),
    GoRoute(
      path: '/create-group',
      builder: (context, state) => const CreateGroupScreen(),
    ),
    GoRoute(
      path: '/group-chat/:groupId',
      builder: (context, state) {
        final groupId = state.pathParameters['groupId']!;
        return GroupChatScreen(groupId: groupId);
      },
    ),
    GoRoute(
      path: '/chat/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return ChatScreen(characterId: characterId);
      },
    ),
  ],
);
