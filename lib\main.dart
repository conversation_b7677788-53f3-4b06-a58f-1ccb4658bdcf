import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'ui/themes/app_theme.dart';
import 'ui/screens/splash_screen.dart';
import 'ui/screens/home_screen.dart';
import 'ui/screens/auth/login_screen.dart';
import 'ui/screens/auth/register_screen.dart';
import 'ui/screens/character/character_selection_screen.dart';
import 'ui/screens/character/character_customization_screen.dart';
import 'ui/screens/chat/chat_screen.dart';
import 'services/storage_service.dart';
import 'services/ai_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化存储服务
  final storageService = StorageService();
  await storageService.initialize();

  // 初始化AI服务
  final aiService = AIService();
  // 注意：这里需要配置Google API Key
  // 可以从环境变量或配置文件中读取
  const apiKey = String.fromEnvironment('GOOGLE_API_KEY', defaultValue: '');
  if (apiKey.isNotEmpty) {
    try {
      await aiService.initialize(apiKey);
    } catch (e) {
      print('AI服务初始化失败: $e');
    }
  }

  runApp(
    ProviderScope(
      overrides: [
        storageServiceProvider.overrideWithValue(storageService),
        aiServiceProvider.overrideWithValue(aiService),
      ],
      child: const AIXCompanionApp(),
    ),
  );
}

class AIXCompanionApp extends StatelessWidget {
  const AIXCompanionApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'AIX Companion',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: _router,
      debugShowCheckedModeBanner: false,
    );
  }
}

final GoRouter _router = GoRouter(
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: '/home',
      builder: (context, state) => const HomeScreen(),
    ),
    GoRoute(
      path: '/login',
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: '/register',
      builder: (context, state) => const RegisterScreen(),
    ),
    GoRoute(
      path: '/character-selection',
      builder: (context, state) => const CharacterSelectionScreen(),
    ),
    GoRoute(
      path: '/character-customization/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return CharacterCustomizationScreen(characterId: characterId);
      },
    ),
    GoRoute(
      path: '/chat/:characterId',
      builder: (context, state) {
        final characterId = state.pathParameters['characterId']!;
        return ChatScreen(characterId: characterId);
      },
    ),
  ],
);
