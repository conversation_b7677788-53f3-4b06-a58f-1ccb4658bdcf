import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../models/models.dart';
import '../../../modules/memory_system/advanced_memory_retrieval_service.dart';
import '../../widgets/enhanced_loading.dart';
import '../../widgets/enhanced_card.dart';
import '../../widgets/enhanced_button.dart';

class AdvancedMemorySearchScreen extends ConsumerStatefulWidget {
  final String characterId;

  const AdvancedMemorySearchScreen({
    super.key,
    required this.characterId,
  });

  @override
  ConsumerState<AdvancedMemorySearchScreen> createState() => _AdvancedMemorySearchScreenState();
}

class _AdvancedMemorySearchScreenState extends ConsumerState<AdvancedMemorySearchScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _contextController = TextEditingController();
  
  List<Memory> _searchResults = [];
  List<Memory> _recommendations = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = false;
  bool _isSearching = false;
  Character? _character;
  
  SearchMode _currentMode = SearchMode.semantic;
  MemoryType? _selectedType;
  double _minImportance = 0.0;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _contextController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    final storageService = ref.read(storageServiceProvider);
    final retrievalService = ref.read(advancedMemoryRetrievalServiceProvider);
    
    final character = await storageService.getCharacter(widget.characterId);
    final statistics = await retrievalService.getRetrievalStatistics(widget.characterId);
    final recommendations = await retrievalService.intelligentRecommendation(
      characterId: widget.characterId,
      currentConversationContext: '日常对话',
      limit: 5,
    );

    if (mounted) {
      setState(() {
        _character = character;
        _statistics = statistics;
        _recommendations = recommendations;
        _isLoading = false;
      });
    }
  }

  Future<void> _performSearch() async {
    if (_searchController.text.trim().isEmpty) return;

    setState(() {
      _isSearching = true;
    });

    try {
      final retrievalService = ref.read(advancedMemoryRetrievalServiceProvider);
      List<Memory> results = [];

      switch (_currentMode) {
        case SearchMode.semantic:
          results = await retrievalService.semanticSearch(
            characterId: widget.characterId,
            query: _searchController.text,
            limit: 20,
          );
          break;
        case SearchMode.contextual:
          final recentTopics = _contextController.text.split(',')
              .map((s) => s.trim())
              .where((s) => s.isNotEmpty)
              .toList();
          results = await retrievalService.contextualRetrieval(
            characterId: widget.characterId,
            currentContext: _searchController.text,
            recentTopics: recentTopics,
            limit: 20,
          );
          break;
        case SearchMode.temporal:
          if (_startDate != null && _endDate != null) {
            results = await retrievalService.temporalRetrieval(
              characterId: widget.characterId,
              startTime: _startDate!,
              endTime: _endDate!,
              type: _selectedType,
              minImportance: _minImportance > 0 ? _minImportance : null,
            );
          }
          break;
      }

      setState(() {
        _searchResults = results;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('搜索失败: $e')),
        );
      }
    } finally {
      setState(() {
        _isSearching = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _character == null) {
      return const Scaffold(
        body: Center(
          child: EnhancedLoading(
            message: '正在加载高级搜索...',
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${_character!.name} 的高级记忆搜索'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.search), text: '搜索'),
            Tab(icon: Icon(Icons.recommend), text: '推荐'),
            Tab(icon: Icon(Icons.analytics), text: '统计'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildSearchTab(),
          _buildRecommendationsTab(),
          _buildStatisticsTab(),
        ],
      ),
    );
  }

  Widget _buildSearchTab() {
    return Column(
      children: [
        // 搜索配置
        _buildSearchConfig(),
        
        // 搜索结果
        Expanded(
          child: _buildSearchResults(),
        ),
      ],
    );
  }

  Widget _buildSearchConfig() {
    return EnhancedCard(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索模式选择
          const Text(
            '搜索模式:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: SearchMode.values.map((mode) {
              return ChoiceChip(
                label: Text(_getSearchModeLabel(mode)),
                selected: _currentMode == mode,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _currentMode = mode;
                    });
                  }
                },
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
          
          // 搜索输入
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              labelText: _getSearchInputLabel(),
              hintText: _getSearchInputHint(),
              border: const OutlineInputBorder(),
              suffixIcon: IconButton(
                icon: _isSearching 
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.search),
                onPressed: _isSearching ? null : _performSearch,
              ),
            ),
            onSubmitted: (_) => _performSearch(),
          ),
          
          // 上下文搜索额外输入
          if (_currentMode == SearchMode.contextual) ...[
            const SizedBox(height: 12),
            TextField(
              controller: _contextController,
              decoration: const InputDecoration(
                labelText: '相关话题 (用逗号分隔)',
                hintText: '工作, 学习, 娱乐',
                border: OutlineInputBorder(),
              ),
            ),
          ],
          
          // 时间搜索额外选项
          if (_currentMode == SearchMode.temporal) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _selectDate(true),
                    child: Text(_startDate != null 
                        ? DateFormat('yyyy/MM/dd').format(_startDate!)
                        : '开始日期'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _selectDate(false),
                    child: Text(_endDate != null 
                        ? DateFormat('yyyy/MM/dd').format(_endDate!)
                        : '结束日期'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<MemoryType>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: '记忆类型',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem<MemoryType>(
                  value: null,
                  child: Text('全部类型'),
                ),
                ...MemoryType.values.map((type) => DropdownMenuItem(
                  value: type,
                  child: Text(_getMemoryTypeLabel(type)),
                )),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedType = value;
                });
              },
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('最低重要性: '),
                Expanded(
                  child: Slider(
                    value: _minImportance,
                    min: 0.0,
                    max: 10.0,
                    divisions: 10,
                    label: _minImportance.toStringAsFixed(1),
                    onChanged: (value) {
                      setState(() {
                        _minImportance = value;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('输入关键词开始搜索', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final memory = _searchResults[index];
        return _buildMemoryCard(memory);
      },
    );
  }

  Widget _buildRecommendationsTab() {
    if (_recommendations.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.recommend_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('暂无推荐记忆', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _recommendations.length,
      itemBuilder: (context, index) {
        final memory = _recommendations[index];
        return _buildMemoryCard(memory, isRecommendation: true);
      },
    );
  }

  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildStatisticsCard(),
          const SizedBox(height: 16),
          _buildTypeDistributionCard(),
        ],
      ),
    );
  }

  Widget _buildMemoryCard(Memory memory, {bool isRecommendation = false}) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getMemoryTypeColor(memory.type).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getMemoryTypeLabel(memory.type),
                  style: TextStyle(
                    fontSize: 12,
                    color: _getMemoryTypeColor(memory.type),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (isRecommendation) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '推荐',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.amber[700],
                    ),
                  ),
                ),
              ],
              const Spacer(),
              Text(
                DateFormat('MM/dd HH:mm').format(memory.timestamp),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Text(
            memory.summary,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          
          Text(
            memory.content,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          
          if (memory.tags.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              children: memory.tags.map((tag) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    tag,
                    style: const TextStyle(fontSize: 10),
                  ),
                );
              }).toList(),
            ),
          ],
          
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.star, size: 12, color: Colors.amber),
              const SizedBox(width: 4),
              Text(
                memory.importance.toStringAsFixed(1),
                style: const TextStyle(fontSize: 12),
              ),
              const SizedBox(width: 16),
              Icon(Icons.visibility, size: 12, color: Colors.grey),
              const SizedBox(width: 4),
              Text(
                '${memory.accessCount}次',
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '记忆统计',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  '总记忆数',
                  '${_statistics['totalMemories'] ?? 0}',
                  Icons.memory,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatItem(
                  '总访问次数',
                  '${_statistics['totalAccesses'] ?? 0}',
                  Icons.visibility,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  '平均重要性',
                  '${(_statistics['averageImportance'] ?? 0.0).toStringAsFixed(1)}',
                  Icons.star,
                  Colors.amber,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatItem(
                  '平均访问',
                  '${(_statistics['averageAccessCount'] ?? 0.0).toStringAsFixed(1)}',
                  Icons.trending_up,
                  Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTypeDistributionCard() {
    final typeDistribution = _statistics['typeDistribution'] as Map<String, dynamic>? ?? {};
    
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '记忆类型分布',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          ...typeDistribution.entries.map((entry) {
            final total = typeDistribution.values.fold<int>(0, (sum, value) => sum + (value as int));
            final percentage = total > 0 ? (entry.value as int) / total : 0.0;
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(entry.key),
                  ),
                  Expanded(
                    flex: 3,
                    child: LinearProgressIndicator(
                      value: percentage,
                      backgroundColor: Colors.grey[300],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text('${entry.value}'),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Future<void> _selectDate(bool isStartDate) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );
    
    if (date != null) {
      setState(() {
        if (isStartDate) {
          _startDate = date;
        } else {
          _endDate = date;
        }
      });
    }
  }

  String _getSearchModeLabel(SearchMode mode) {
    switch (mode) {
      case SearchMode.semantic:
        return '语义搜索';
      case SearchMode.contextual:
        return '上下文搜索';
      case SearchMode.temporal:
        return '时间搜索';
    }
  }

  String _getSearchInputLabel() {
    switch (_currentMode) {
      case SearchMode.semantic:
        return '搜索关键词';
      case SearchMode.contextual:
        return '当前对话内容';
      case SearchMode.temporal:
        return '搜索关键词 (可选)';
    }
  }

  String _getSearchInputHint() {
    switch (_currentMode) {
      case SearchMode.semantic:
        return '输入要搜索的内容...';
      case SearchMode.contextual:
        return '描述当前对话的内容...';
      case SearchMode.temporal:
        return '可以输入关键词进一步筛选...';
    }
  }

  String _getMemoryTypeLabel(MemoryType type) {
    switch (type) {
      case MemoryType.conversation:
        return '对话';
      case MemoryType.personal:
        return '个人信息';
      case MemoryType.preference:
        return '偏好';
      case MemoryType.emotion:
        return '情感';
      case MemoryType.event:
        return '事件';
      case MemoryType.relationship:
        return '关系';
      case MemoryType.habit:
        return '习惯';
      case MemoryType.goal:
        return '目标';
      case MemoryType.experience:
        return '经历';
      case MemoryType.knowledge:
        return '知识';
    }
  }

  Color _getMemoryTypeColor(MemoryType type) {
    switch (type) {
      case MemoryType.conversation:
        return Colors.blue;
      case MemoryType.personal:
        return Colors.green;
      case MemoryType.preference:
        return Colors.purple;
      case MemoryType.emotion:
        return Colors.red;
      case MemoryType.event:
        return Colors.orange;
      case MemoryType.relationship:
        return Colors.pink;
      case MemoryType.habit:
        return Colors.teal;
      case MemoryType.goal:
        return Colors.indigo;
      case MemoryType.experience:
        return Colors.brown;
      case MemoryType.knowledge:
        return Colors.cyan;
    }
  }
}

enum SearchMode {
  semantic,    // 语义搜索
  contextual,  // 上下文搜索
  temporal,    // 时间搜索
}
