import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'interaction_analysis.freezed.dart';
part 'interaction_analysis.g.dart';

@freezed
@HiveType(typeId: 37)
class InteractionAnalysis with _$InteractionAnalysis {
  const factory InteractionAnalysis({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required DateTime analysisDate,
    @HiveField(4) required AnalysisPeriod period,
    @HiveField(5) required InteractionMetrics metrics,
    @HiveField(6) required CommunicationPatterns communicationPatterns,
    @HiveField(7) required RelationshipInsights relationshipInsights,
    @HiveField(8) required List<InteractionTrend> trends,
    @HiveField(9) required List<String> recommendations,
    @HiveField(10) @Default([]) List<String> highlights,
    @HiveField(11) @Default([]) List<String> concerns,
    @HiveField(12) Map<String, dynamic>? metadata,
  }) = _InteractionAnalysis;

  factory InteractionAnalysis.fromJson(Map<String, dynamic> json) => _$InteractionAnalysisFromJson(json);
}

@HiveType(typeId: 38)
enum AnalysisPeriod {
  @HiveField(0)
  daily,    // 日分析
  @HiveField(1)
  weekly,   // 周分析
  @HiveField(2)
  monthly,  // 月分析
  @HiveField(3)
  quarterly, // 季度分析
  @HiveField(4)
  yearly,   // 年分析
}

@freezed
@HiveType(typeId: 39)
class InteractionMetrics with _$InteractionMetrics {
  const factory InteractionMetrics({
    @HiveField(0) required int totalMessages,
    @HiveField(1) required int userMessages,
    @HiveField(2) required int characterMessages,
    @HiveField(3) required double averageMessageLength,
    @HiveField(4) required Duration totalChatTime,
    @HiveField(5) required Duration averageSessionDuration,
    @HiveField(6) required int sessionCount,
    @HiveField(7) required double responseRate,
    @HiveField(8) required Duration averageResponseTime,
    @HiveField(9) required Map<String, int> dailyMessageCounts,
    @HiveField(10) required Map<String, Duration> dailyChatTimes,
    @HiveField(11) @Default(0) int longestStreak,
    @HiveField(12) @Default(0) int currentStreak,
  }) = _InteractionMetrics;

  factory InteractionMetrics.fromJson(Map<String, dynamic> json) => _$InteractionMetricsFromJson(json);
}

@freezed
@HiveType(typeId: 40)
class CommunicationPatterns with _$CommunicationPatterns {
  const factory CommunicationPatterns({
    @HiveField(0) required Map<String, double> topicDistribution,
    @HiveField(1) required Map<String, double> emotionDistribution,
    @HiveField(2) required Map<String, double> timeOfDayActivity,
    @HiveField(3) required Map<String, double> dayOfWeekActivity,
    @HiveField(4) required List<String> frequentWords,
    @HiveField(5) required List<String> frequentPhrases,
    @HiveField(6) required double averageSentiment,
    @HiveField(7) required CommunicationStyle dominantStyle,
    @HiveField(8) @Default([]) List<ConversationTheme> themes,
  }) = _CommunicationPatterns;

  factory CommunicationPatterns.fromJson(Map<String, dynamic> json) => _$CommunicationPatternsFromJson(json);
}

@HiveType(typeId: 41)
enum CommunicationStyle {
  @HiveField(0)
  casual,       // 随意
  @HiveField(1)
  formal,       // 正式
  @HiveField(2)
  emotional,    // 情感化
  @HiveField(3)
  analytical,   // 分析性
  @HiveField(4)
  supportive,   // 支持性
  @HiveField(5)
  playful,      // 玩乐性
}

@freezed
@HiveType(typeId: 42)
class ConversationTheme with _$ConversationTheme {
  const factory ConversationTheme({
    @HiveField(0) required String theme,
    @HiveField(1) required double frequency,
    @HiveField(2) required double sentiment,
    @HiveField(3) required List<String> keywords,
    @HiveField(4) required DateTime firstMention,
    @HiveField(5) required DateTime lastMention,
  }) = _ConversationTheme;

  factory ConversationTheme.fromJson(Map<String, dynamic> json) => _$ConversationThemeFromJson(json);
}

@freezed
@HiveType(typeId: 43)
class RelationshipInsights with _$RelationshipInsights {
  const factory RelationshipInsights({
    @HiveField(0) required double intimacyGrowthRate,
    @HiveField(1) required double engagementScore,
    @HiveField(2) required double trustLevel,
    @HiveField(3) required double emotionalConnection,
    @HiveField(4) required List<RelationshipMilestone> milestones,
    @HiveField(5) required Map<String, double> relationshipDimensions,
    @HiveField(6) required RelationshipStage currentStage,
    @HiveField(7) required RelationshipStage predictedNextStage,
    @HiveField(8) @Default([]) List<String> strengthAreas,
    @HiveField(9) @Default([]) List<String> improvementAreas,
  }) = _RelationshipInsights;

  factory RelationshipInsights.fromJson(Map<String, dynamic> json) => _$RelationshipInsightsFromJson(json);
}

@HiveType(typeId: 44)
enum RelationshipStage {
  @HiveField(0)
  stranger,     // 陌生人
  @HiveField(1)
  acquaintance, // 熟人
  @HiveField(2)
  friend,       // 朋友
  @HiveField(3)
  closeFriend,  // 好朋友
  @HiveField(4)
  bestFriend,   // 最好的朋友
  @HiveField(5)
  companion,    // 伴侣
}

@freezed
@HiveType(typeId: 45)
class RelationshipMilestone with _$RelationshipMilestone {
  const factory RelationshipMilestone({
    @HiveField(0) required String id,
    @HiveField(1) required String title,
    @HiveField(2) required String description,
    @HiveField(3) required DateTime achievedAt,
    @HiveField(4) required MilestoneType type,
    @HiveField(5) required double significance,
    @HiveField(6) @Default([]) List<String> relatedMessageIds,
  }) = _RelationshipMilestone;

  factory RelationshipMilestone.fromJson(Map<String, dynamic> json) => _$RelationshipMilestoneFromJson(json);
}

@HiveType(typeId: 46)
enum MilestoneType {
  @HiveField(0)
  firstMessage,     // 第一条消息
  @HiveField(1)
  firstWeek,        // 第一周
  @HiveField(2)
  intimacyLevel,    // 亲密度等级
  @HiveField(3)
  conversationCount, // 对话次数
  @HiveField(4)
  emotionalMoment,  // 情感时刻
  @HiveField(5)
  personalSharing,  // 个人分享
  @HiveField(6)
  supportMoment,    // 支持时刻
}

@freezed
@HiveType(typeId: 47)
class InteractionTrend with _$InteractionTrend {
  const factory InteractionTrend({
    @HiveField(0) required String metric,
    @HiveField(1) required TrendDirection direction,
    @HiveField(2) required double changePercentage,
    @HiveField(3) required List<DataPoint> dataPoints,
    @HiveField(4) required String description,
    @HiveField(5) required TrendSignificance significance,
  }) = _InteractionTrend;

  factory InteractionTrend.fromJson(Map<String, dynamic> json) => _$InteractionTrendFromJson(json);
}

@HiveType(typeId: 48)
enum TrendDirection {
  @HiveField(0)
  increasing,   // 上升
  @HiveField(1)
  decreasing,   // 下降
  @HiveField(2)
  stable,       // 稳定
  @HiveField(3)
  fluctuating,  // 波动
}

@HiveType(typeId: 49)
enum TrendSignificance {
  @HiveField(0)
  low,      // 低
  @HiveField(1)
  medium,   // 中
  @HiveField(2)
  high,     // 高
  @HiveField(3)
  critical, // 关键
}

@freezed
@HiveType(typeId: 50)
class DataPoint with _$DataPoint {
  const factory DataPoint({
    @HiveField(0) required DateTime timestamp,
    @HiveField(1) required double value,
    @HiveField(2) String? label,
  }) = _DataPoint;

  factory DataPoint.fromJson(Map<String, dynamic> json) => _$DataPointFromJson(json);
}

@freezed
@HiveType(typeId: 51)
class InteractionReport with _$InteractionReport {
  const factory InteractionReport({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String characterId,
    @HiveField(3) required DateTime generatedAt,
    @HiveField(4) required ReportType type,
    @HiveField(5) required String title,
    @HiveField(6) required String summary,
    @HiveField(7) required List<ReportSection> sections,
    @HiveField(8) required List<String> keyFindings,
    @HiveField(9) required List<String> actionItems,
    @HiveField(10) @Default([]) List<String> attachmentIds,
  }) = _InteractionReport;

  factory InteractionReport.fromJson(Map<String, dynamic> json) => _$InteractionReportFromJson(json);
}

@HiveType(typeId: 52)
enum ReportType {
  @HiveField(0)
  weekly,       // 周报
  @HiveField(1)
  monthly,      // 月报
  @HiveField(2)
  relationship, // 关系报告
  @HiveField(3)
  emotional,    // 情感报告
  @HiveField(4)
  communication, // 沟通报告
}

@freezed
@HiveType(typeId: 53)
class ReportSection with _$ReportSection {
  const factory ReportSection({
    @HiveField(0) required String title,
    @HiveField(1) required String content,
    @HiveField(2) required SectionType type,
    @HiveField(3) @Default([]) List<String> charts,
    @HiveField(4) @Default([]) List<String> highlights,
    @HiveField(5) Map<String, dynamic>? data,
  }) = _ReportSection;

  factory ReportSection.fromJson(Map<String, dynamic> json) => _$ReportSectionFromJson(json);
}

@HiveType(typeId: 54)
enum SectionType {
  @HiveField(0)
  overview,     // 概览
  @HiveField(1)
  metrics,      // 指标
  @HiveField(2)
  trends,       // 趋势
  @HiveField(3)
  insights,     // 洞察
  @HiveField(4)
  recommendations, // 建议
}
