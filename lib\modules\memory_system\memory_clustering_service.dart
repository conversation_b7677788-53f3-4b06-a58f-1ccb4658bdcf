import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/models.dart';
import '../../services/storage_service.dart';

class MemoryClusteringService {
  final StorageService _storageService;
  final Uuid _uuid = const Uuid();
  
  static const String _defaultUserId = 'default_user';

  MemoryClusteringService(this._storageService);

  // 执行记忆聚类
  Future<List<MemoryCluster>> performClustering({
    required String characterId,
    int minClusterSize = 3,
    double similarityThreshold = 0.6,
  }) async {
    final memories = await _storageService.getMemoriesByCharacter(characterId);
    if (memories.length < minClusterSize) {
      return [];
    }

    // 计算记忆之间的相似度矩阵
    final similarityMatrix = _calculateSimilarityMatrix(memories);
    
    // 执行聚类算法
    final clusters = _performHierarchicalClustering(
      memories,
      similarityMatrix,
      similarityThreshold,
      minClusterSize,
    );

    // 生成聚类对象
    final memoryClusters = <MemoryCluster>[];
    for (int i = 0; i < clusters.length; i++) {
      final cluster = clusters[i];
      if (cluster.length >= minClusterSize) {
        final memoryCluster = await _createMemoryCluster(
          characterId,
          cluster,
          i,
        );
        memoryClusters.add(memoryCluster);
        await _storageService.saveMemoryCluster(memoryCluster);
      }
    }

    return memoryClusters;
  }

  // 计算相似度矩阵
  List<List<double>> _calculateSimilarityMatrix(List<Memory> memories) {
    final matrix = List.generate(
      memories.length,
      (i) => List.generate(memories.length, (j) => 0.0),
    );

    for (int i = 0; i < memories.length; i++) {
      for (int j = i + 1; j < memories.length; j++) {
        final similarity = _calculateMemorySimilarity(memories[i], memories[j]);
        matrix[i][j] = similarity;
        matrix[j][i] = similarity;
      }
      matrix[i][i] = 1.0; // 自相似度为1
    }

    return matrix;
  }

  // 计算两个记忆的相似度
  double _calculateMemorySimilarity(Memory memory1, Memory memory2) {
    double similarity = 0.0;

    // 类型相似度 (30%)
    if (memory1.type == memory2.type) {
      similarity += 0.3;
    }

    // 关键词相似度 (40%)
    final keywordSimilarity = _calculateKeywordSimilarity(
      memory1.keywords,
      memory2.keywords,
    );
    similarity += keywordSimilarity * 0.4;

    // 时间相似度 (20%)
    final timeSimilarity = _calculateTimeSimilarity(
      memory1.timestamp,
      memory2.timestamp,
    );
    similarity += timeSimilarity * 0.2;

    // 重要性相似度 (10%)
    final importanceDiff = (memory1.importance - memory2.importance).abs();
    final importanceSimilarity = 1.0 - (importanceDiff / 10.0);
    similarity += importanceSimilarity * 0.1;

    return similarity.clamp(0.0, 1.0);
  }

  // 计算关键词相似度
  double _calculateKeywordSimilarity(List<String> keywords1, List<String> keywords2) {
    if (keywords1.isEmpty && keywords2.isEmpty) return 1.0;
    if (keywords1.isEmpty || keywords2.isEmpty) return 0.0;

    final set1 = keywords1.toSet();
    final set2 = keywords2.toSet();
    final intersection = set1.intersection(set2);
    final union = set1.union(set2);

    return intersection.length / union.length;
  }

  // 计算时间相似度
  double _calculateTimeSimilarity(DateTime time1, DateTime time2) {
    final diffDays = time1.difference(time2).inDays.abs();
    
    // 时间差越小，相似度越高
    if (diffDays == 0) return 1.0;
    if (diffDays <= 1) return 0.8;
    if (diffDays <= 7) return 0.6;
    if (diffDays <= 30) return 0.4;
    if (diffDays <= 90) return 0.2;
    return 0.0;
  }

  // 执行层次聚类
  List<List<Memory>> _performHierarchicalClustering(
    List<Memory> memories,
    List<List<double>> similarityMatrix,
    double threshold,
    int minClusterSize,
  ) {
    // 初始化：每个记忆为一个聚类
    final clusters = memories.map((memory) => [memory]).toList();
    final clusterSimilarity = List.generate(
      clusters.length,
      (i) => List.generate(clusters.length, (j) => similarityMatrix[i][j]),
    );

    // 迭代合并相似的聚类
    while (clusters.length > 1) {
      // 找到最相似的两个聚类
      double maxSimilarity = 0.0;
      int cluster1Index = -1;
      int cluster2Index = -1;

      for (int i = 0; i < clusters.length; i++) {
        for (int j = i + 1; j < clusters.length; j++) {
          final similarity = _calculateClusterSimilarity(
            clusters[i],
            clusters[j],
            memories,
            similarityMatrix,
          );
          if (similarity > maxSimilarity) {
            maxSimilarity = similarity;
            cluster1Index = i;
            cluster2Index = j;
          }
        }
      }

      // 如果最大相似度低于阈值，停止合并
      if (maxSimilarity < threshold) break;

      // 合并两个最相似的聚类
      final mergedCluster = [...clusters[cluster1Index], ...clusters[cluster2Index]];
      clusters.removeAt(cluster2Index); // 先移除索引较大的
      clusters.removeAt(cluster1Index);
      clusters.add(mergedCluster);
    }

    // 过滤掉太小的聚类
    return clusters.where((cluster) => cluster.length >= minClusterSize).toList();
  }

  // 计算聚类间相似度
  double _calculateClusterSimilarity(
    List<Memory> cluster1,
    List<Memory> cluster2,
    List<Memory> allMemories,
    List<List<double>> similarityMatrix,
  ) {
    double totalSimilarity = 0.0;
    int count = 0;

    for (final memory1 in cluster1) {
      for (final memory2 in cluster2) {
        final index1 = allMemories.indexOf(memory1);
        final index2 = allMemories.indexOf(memory2);
        if (index1 != -1 && index2 != -1) {
          totalSimilarity += similarityMatrix[index1][index2];
          count++;
        }
      }
    }

    return count > 0 ? totalSimilarity / count : 0.0;
  }

  // 创建记忆聚类对象
  Future<MemoryCluster> _createMemoryCluster(
    String characterId,
    List<Memory> memories,
    int clusterIndex,
  ) async {
    // 分析聚类主题
    final theme = _analyzeClusterTheme(memories);
    
    // 计算聚类统计信息
    final stats = _calculateClusterStats(memories);
    
    // 生成聚类描述
    final description = _generateClusterDescription(memories, theme);
    
    // 提取关键词
    final keywords = _extractClusterKeywords(memories);

    return MemoryCluster(
      id: _uuid.v4(),
      userId: _defaultUserId,
      characterId: characterId,
      theme: theme,
      description: description,
      memoryIds: memories.map((m) => m.id).toList(),
      keywords: keywords,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      importance: _calculateClusterImportance(memories),
      coherenceScore: _calculateCoherenceScore(memories),
      metadata: {
        'clusterIndex': clusterIndex,
        'memoryCount': memories.length,
        'averageImportance': stats['averageImportance'],
        'timeSpan': stats['timeSpan'],
      },
    );
  }

  // 分析聚类主题
  String _analyzeClusterTheme(List<Memory> memories) {
    // 统计记忆类型
    final typeCounts = <MemoryType, int>{};
    for (final memory in memories) {
      typeCounts[memory.type] = (typeCounts[memory.type] ?? 0) + 1;
    }

    // 找到最常见的类型
    final dominantType = typeCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    // 分析关键词模式
    final allKeywords = memories.expand((m) => m.keywords).toList();
    final keywordCounts = <String, int>{};
    for (final keyword in allKeywords) {
      keywordCounts[keyword] = (keywordCounts[keyword] ?? 0) + 1;
    }

    final topKeywords = keywordCounts.entries
        .where((e) => e.value > 1)
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // 生成主题
    if (topKeywords.isNotEmpty) {
      return '${_getMemoryTypeLabel(dominantType)} - ${topKeywords.first.key}';
    } else {
      return _getMemoryTypeLabel(dominantType);
    }
  }

  // 获取记忆类型标签
  String _getMemoryTypeLabel(MemoryType type) {
    switch (type) {
      case MemoryType.personal:
        return '个人信息';
      case MemoryType.preference:
        return '偏好喜好';
      case MemoryType.experience:
        return '经历体验';
      case MemoryType.emotion:
        return '情感表达';
      case MemoryType.goal:
        return '目标计划';
      case MemoryType.relationship:
        return '关系互动';
      case MemoryType.knowledge:
        return '知识学习';
      case MemoryType.habit:
        return '习惯行为';
    }
  }

  // 计算聚类统计信息
  Map<String, dynamic> _calculateClusterStats(List<Memory> memories) {
    if (memories.isEmpty) return {};

    final importances = memories.map((m) => m.importance).toList();
    final averageImportance = importances.reduce((a, b) => a + b) / importances.length;

    final timestamps = memories.map((m) => m.timestamp).toList()..sort();
    final timeSpan = timestamps.last.difference(timestamps.first).inDays;

    return {
      'averageImportance': averageImportance,
      'timeSpan': timeSpan,
      'memoryCount': memories.length,
    };
  }

  // 生成聚类描述
  String _generateClusterDescription(List<Memory> memories, String theme) {
    final memoryCount = memories.length;
    final timeSpan = memories.map((m) => m.timestamp).toList()
      ..sort();
    
    final daySpan = timeSpan.isNotEmpty 
        ? timeSpan.last.difference(timeSpan.first).inDays
        : 0;

    if (daySpan == 0) {
      return '关于$theme的$memoryCount个相关记忆，都发生在同一天';
    } else if (daySpan <= 7) {
      return '关于$theme的$memoryCount个相关记忆，发生在一周内';
    } else if (daySpan <= 30) {
      return '关于$theme的$memoryCount个相关记忆，发生在一个月内';
    } else {
      return '关于$theme的$memoryCount个相关记忆，跨越${daySpan}天';
    }
  }

  // 提取聚类关键词
  List<String> _extractClusterKeywords(List<Memory> memories) {
    final keywordCounts = <String, int>{};
    
    for (final memory in memories) {
      for (final keyword in memory.keywords) {
        keywordCounts[keyword] = (keywordCounts[keyword] ?? 0) + 1;
      }
    }

    // 返回出现频率最高的关键词
    final sortedKeywords = keywordCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedKeywords
        .where((e) => e.value > 1) // 至少出现2次
        .take(5)
        .map((e) => e.key)
        .toList();
  }

  // 计算聚类重要性
  double _calculateClusterImportance(List<Memory> memories) {
    if (memories.isEmpty) return 0.0;

    final importances = memories.map((m) => m.importance).toList();
    final averageImportance = importances.reduce((a, b) => a + b) / importances.length;
    
    // 聚类大小也影响重要性
    final sizeBonus = (memories.length / 10.0).clamp(0.0, 2.0);
    
    return (averageImportance + sizeBonus).clamp(0.0, 10.0);
  }

  // 计算连贯性分数
  double _calculateCoherenceScore(List<Memory> memories) {
    if (memories.length < 2) return 1.0;

    double totalSimilarity = 0.0;
    int pairCount = 0;

    for (int i = 0; i < memories.length; i++) {
      for (int j = i + 1; j < memories.length; j++) {
        totalSimilarity += _calculateMemorySimilarity(memories[i], memories[j]);
        pairCount++;
      }
    }

    return pairCount > 0 ? totalSimilarity / pairCount : 0.0;
  }

  // 获取角色的所有记忆聚类
  Future<List<MemoryCluster>> getMemoryClusters(String characterId) async {
    return await _storageService.getMemoryClustersByCharacter(characterId);
  }

  // 更新聚类（当有新记忆时）
  Future<void> updateClustersWithNewMemory({
    required String characterId,
    required Memory newMemory,
  }) async {
    final existingClusters = await getMemoryClusters(characterId);
    
    // 找到最适合的聚类
    MemoryCluster? bestCluster;
    double bestSimilarity = 0.0;
    
    for (final cluster in existingClusters) {
      final clusterMemories = await _getClusterMemories(cluster);
      final similarity = _calculateMemoryToClusterSimilarity(newMemory, clusterMemories);
      
      if (similarity > bestSimilarity && similarity > 0.6) {
        bestSimilarity = similarity;
        bestCluster = cluster;
      }
    }

    if (bestCluster != null) {
      // 将记忆添加到现有聚类
      final updatedCluster = bestCluster.copyWith(
        memoryIds: [...bestCluster.memoryIds, newMemory.id],
        updatedAt: DateTime.now(),
      );
      await _storageService.saveMemoryCluster(updatedCluster);
    } else {
      // 检查是否需要重新聚类
      final allMemories = await _storageService.getMemoriesByCharacter(characterId);
      if (allMemories.length % 10 == 0) { // 每10个新记忆重新聚类一次
        await performClustering(characterId: characterId);
      }
    }
  }

  // 获取聚类中的记忆
  Future<List<Memory>> _getClusterMemories(MemoryCluster cluster) async {
    final memories = <Memory>[];
    for (final memoryId in cluster.memoryIds) {
      final memory = await _storageService.getMemory(memoryId);
      if (memory != null) {
        memories.add(memory);
      }
    }
    return memories;
  }

  // 计算记忆与聚类的相似度
  double _calculateMemoryToClusterSimilarity(Memory memory, List<Memory> clusterMemories) {
    if (clusterMemories.isEmpty) return 0.0;

    double totalSimilarity = 0.0;
    for (final clusterMemory in clusterMemories) {
      totalSimilarity += _calculateMemorySimilarity(memory, clusterMemory);
    }

    return totalSimilarity / clusterMemories.length;
  }

  // 分析聚类趋势
  Future<Map<String, dynamic>> analyzeClusterTrends(String characterId) async {
    final clusters = await getMemoryClusters(characterId);
    
    if (clusters.isEmpty) {
      return {
        'totalClusters': 0,
        'averageClusterSize': 0.0,
        'dominantThemes': <String>[],
        'clusterGrowth': 0.0,
      };
    }

    // 计算聚类统计
    final totalMemories = clusters.fold<int>(0, (sum, cluster) => sum + cluster.memoryIds.length);
    final averageClusterSize = totalMemories / clusters.length;

    // 分析主导主题
    final themeCounts = <String, int>{};
    for (final cluster in clusters) {
      final theme = cluster.theme.split(' - ').first; // 取主要部分
      themeCounts[theme] = (themeCounts[theme] ?? 0) + 1;
    }

    final dominantThemes = themeCounts.entries
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // 计算聚类增长趋势
    final recentClusters = clusters.where((c) => 
        DateTime.now().difference(c.createdAt).inDays <= 30).length;
    final clusterGrowth = recentClusters / clusters.length;

    return {
      'totalClusters': clusters.length,
      'averageClusterSize': averageClusterSize,
      'dominantThemes': dominantThemes.take(5).map((e) => e.key).toList(),
      'clusterGrowth': clusterGrowth,
      'totalMemories': totalMemories,
      'averageCoherence': clusters.map((c) => c.coherenceScore).reduce((a, b) => a + b) / clusters.length,
    };
  }

  // 搜索相关聚类
  Future<List<MemoryCluster>> searchClusters({
    required String characterId,
    required String query,
  }) async {
    final clusters = await getMemoryClusters(characterId);
    final queryLower = query.toLowerCase();

    return clusters.where((cluster) {
      return cluster.theme.toLowerCase().contains(queryLower) ||
             cluster.description.toLowerCase().contains(queryLower) ||
             cluster.keywords.any((keyword) => keyword.toLowerCase().contains(queryLower));
    }).toList()
      ..sort((a, b) => b.importance.compareTo(a.importance));
  }
}

// Provider
final memoryClusteringServiceProvider = Provider<MemoryClusteringService>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return MemoryClusteringService(storageService);
});
