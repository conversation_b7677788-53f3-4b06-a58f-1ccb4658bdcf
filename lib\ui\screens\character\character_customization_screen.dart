import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../models/models.dart';
import '../../../modules/character_management/character_service.dart';

class CharacterCustomizationScreen extends ConsumerStatefulWidget {
  final String characterId;

  const CharacterCustomizationScreen({
    super.key,
    required this.characterId,
  });

  @override
  ConsumerState<CharacterCustomizationScreen> createState() => _CharacterCustomizationScreenState();
}

class _CharacterCustomizationScreenState extends ConsumerState<CharacterCustomizationScreen> {
  Character? _character;
  late PersonalityTraits _personality;
  late List<String> _interests;
  late List<String> _traits;
  late String _speakingStyle;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadCharacter();
  }

  Future<void> _loadCharacter() async {
    final characterService = ref.read(characterServiceProvider);
    final character = await characterService.getCharacter(widget.characterId);
    
    if (character != null && mounted) {
      setState(() {
        _character = character;
        _personality = character.personality;
        _interests = List.from(character.interests);
        _traits = List.from(character.traits);
        _speakingStyle = character.speakingStyle;
        _isLoading = false;
      });
    }
  }

  Future<void> _saveChanges() async {
    if (_character == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final characterService = ref.read(characterServiceProvider);
      
      await characterService.updateCharacterPersonality(widget.characterId, _personality);
      await characterService.updateCharacterInterests(widget.characterId, _interests);
      await characterService.updateCharacterTraits(widget.characterId, _traits);
      await characterService.updateSpeakingStyle(widget.characterId, _speakingStyle);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('角色设置已保存')),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading || _character == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('自定义 ${_character!.name}'),
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _saveChanges,
            child: _isSaving
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('保存'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 角色信息卡片
            _buildCharacterInfoCard(),
            const SizedBox(height: 24),
            
            // 性格特征调整
            _buildPersonalitySection(),
            const SizedBox(height: 24),
            
            // 说话风格
            _buildSpeakingStyleSection(),
            const SizedBox(height: 24),
            
            // 兴趣爱好
            _buildInterestsSection(),
            const SizedBox(height: 24),
            
            // 性格标签
            _buildTraitsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildCharacterInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: _getCharacterColor(_character!.id),
              child: Text(
                _character!.name[0],
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 24,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _character!.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${_character!.age}岁 · ${_character!.occupation}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _character!.description,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '性格特征',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        _buildPersonalitySlider('外向性', _personality.extroversion, (value) {
          setState(() {
            _personality = _personality.copyWith(extroversion: value);
          });
        }),
        _buildPersonalitySlider('友善性', _personality.agreeableness, (value) {
          setState(() {
            _personality = _personality.copyWith(agreeableness: value);
          });
        }),
        _buildPersonalitySlider('责任心', _personality.conscientiousness, (value) {
          setState(() {
            _personality = _personality.copyWith(conscientiousness: value);
          });
        }),
        _buildPersonalitySlider('情绪稳定性', 1.0 - _personality.neuroticism, (value) {
          setState(() {
            _personality = _personality.copyWith(neuroticism: 1.0 - value);
          });
        }),
        _buildPersonalitySlider('开放性', _personality.openness, (value) {
          setState(() {
            _personality = _personality.copyWith(openness: value);
          });
        }),
        _buildPersonalitySlider('幽默感', _personality.humor, (value) {
          setState(() {
            _personality = _personality.copyWith(humor: value);
          });
        }),
        _buildPersonalitySlider('共情能力', _personality.empathy, (value) {
          setState(() {
            _personality = _personality.copyWith(empathy: value);
          });
        }),
        _buildPersonalitySlider('创造力', _personality.creativity, (value) {
          setState(() {
            _personality = _personality.copyWith(creativity: value);
          });
        }),
      ],
    );
  }

  Widget _buildPersonalitySlider(String label, double value, ValueChanged<double> onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(label),
              Text('${(value * 100).round()}%'),
            ],
          ),
          Slider(
            value: value,
            onChanged: onChanged,
            divisions: 10,
          ),
        ],
      ),
    );
  }

  Widget _buildSpeakingStyleSection() {
    final styles = {
      'gentle': '温柔体贴',
      'energetic': '活力四射',
      'professional': '专业理性',
      'technical': '技术范儿',
      'casual': '随意自然',
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '说话风格',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: styles.entries.map((entry) {
            final isSelected = _speakingStyle == entry.key;
            return FilterChip(
              label: Text(entry.value),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _speakingStyle = entry.key;
                  });
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildInterestsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '兴趣爱好',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _addInterest,
            ),
          ],
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _interests.map((interest) {
            return Chip(
              label: Text(interest),
              deleteIcon: const Icon(Icons.close, size: 16),
              onDeleted: () {
                setState(() {
                  _interests.remove(interest);
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildTraitsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '性格标签',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _addTrait,
            ),
          ],
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _traits.map((trait) {
            return Chip(
              label: Text(trait),
              deleteIcon: const Icon(Icons.close, size: 16),
              onDeleted: () {
                setState(() {
                  _traits.remove(trait);
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  void _addInterest() {
    _showAddDialog('添加兴趣爱好', (value) {
      if (value.isNotEmpty && !_interests.contains(value)) {
        setState(() {
          _interests.add(value);
        });
      }
    });
  }

  void _addTrait() {
    _showAddDialog('添加性格标签', (value) {
      if (value.isNotEmpty && !_traits.contains(value)) {
        setState(() {
          _traits.add(value);
        });
      }
    });
  }

  void _showAddDialog(String title, ValueChanged<String> onAdd) {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: '请输入内容',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              onAdd(controller.text.trim());
              Navigator.pop(context);
            },
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  Color _getCharacterColor(String characterId) {
    switch (characterId) {
      case '1':
        return Colors.pink;
      case '2':
        return Colors.blue;
      case '3':
        return Colors.purple;
      case '4':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
