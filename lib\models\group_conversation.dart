import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'group_conversation.freezed.dart';
part 'group_conversation.g.dart';

@freezed
@HiveType(typeId: 19)
class GroupConversation with _$GroupConversation {
  const factory GroupConversation({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String title,
    @HiveField(3) required List<String> characterIds,
    @HiveField(4) required DateTime createdAt,
    @HiveField(5) required DateTime updatedAt,
    @HiveField(6) @Default([]) List<String> messageIds,
    @HiveField(7) @Default(0) int messageCount,
    @HiveField(8) String? lastMessageId,
    @HiveField(9) String? lastMessagePreview,
    @HiveField(10) @Default(false) bool isActive,
    @HiveField(11) @Default({}) Map<String, int> characterMessageCounts,
    @HiveField(12) @Default({}) Map<String, DateTime> lastCharacterActivity,
    @HiveField(13) GroupConversationType? type,
    @HiveField(14) Map<String, dynamic>? settings,
  }) = _GroupConversation;

  factory GroupConversation.fromJson(Map<String, dynamic> json) => _$GroupConversationFromJson(json);
}

@HiveType(typeId: 20)
enum GroupConversationType {
  @HiveField(0)
  casual,      // 随意聊天
  @HiveField(1)
  debate,      // 辩论讨论
  @HiveField(2)
  roleplay,    // 角色扮演
  @HiveField(3)
  support,     // 情感支持
  @HiveField(4)
  learning,    // 学习交流
}

@freezed
@HiveType(typeId: 21)
class GroupMessage with _$GroupMessage {
  const factory GroupMessage({
    @HiveField(0) required String id,
    @HiveField(1) required String groupConversationId,
    @HiveField(2) required String content,
    @HiveField(3) required MessageType type,
    @HiveField(4) required MessageSender sender,
    @HiveField(5) required DateTime timestamp,
    @HiveField(6) @Default(MessageStatus.sent) MessageStatus status,
    @HiveField(7) String? characterId,
    @HiveField(8) String? replyToMessageId,
    @HiveField(9) @Default([]) List<String> mentionedCharacterIds,
    @HiveField(10) @Default(false) bool isSystemMessage,
    @HiveField(11) Map<String, dynamic>? metadata,
  }) = _GroupMessage;

  factory GroupMessage.fromJson(Map<String, dynamic> json) => _$GroupMessageFromJson(json);
}

@freezed
@HiveType(typeId: 22)
class CharacterRelationship with _$CharacterRelationship {
  const factory CharacterRelationship({
    @HiveField(0) required String id,
    @HiveField(1) required String character1Id,
    @HiveField(2) required String character2Id,
    @HiveField(3) required String userId,
    @HiveField(4) required RelationshipType type,
    @HiveField(5) required double strength,
    @HiveField(6) required DateTime establishedAt,
    @HiveField(7) required DateTime lastInteraction,
    @HiveField(8) @Default(0) int interactionCount,
    @HiveField(9) @Default([]) List<String> sharedMemoryIds,
    @HiveField(10) @Default({}) Map<String, double> emotionalDynamics,
    @HiveField(11) String? description,
    @HiveField(12) Map<String, dynamic>? attributes,
  }) = _CharacterRelationship;

  factory CharacterRelationship.fromJson(Map<String, dynamic> json) => _$CharacterRelationshipFromJson(json);
}

@HiveType(typeId: 23)
enum RelationshipType {
  @HiveField(0)
  neutral,     // 中性
  @HiveField(1)
  friendly,    // 友好
  @HiveField(2)
  competitive, // 竞争
  @HiveField(3)
  romantic,    // 浪漫
  @HiveField(4)
  mentor,      // 师生
  @HiveField(5)
  rival,       // 对手
  @HiveField(6)
  family,      // 家人
}

@freezed
@HiveType(typeId: 24)
class GroupInteractionEvent with _$GroupInteractionEvent {
  const factory GroupInteractionEvent({
    @HiveField(0) required String id,
    @HiveField(1) required String groupConversationId,
    @HiveField(2) required String userId,
    @HiveField(3) required InteractionEventType type,
    @HiveField(4) required DateTime timestamp,
    @HiveField(5) required List<String> involvedCharacterIds,
    @HiveField(6) required String description,
    @HiveField(7) @Default({}) Map<String, dynamic> eventData,
    @HiveField(8) @Default(1.0) double impact,
    @HiveField(9) String? triggerMessageId,
  }) = _GroupInteractionEvent;

  factory GroupInteractionEvent.fromJson(Map<String, dynamic> json) => _$GroupInteractionEventFromJson(json);
}

@HiveType(typeId: 25)
enum InteractionEventType {
  @HiveField(0)
  characterJoined,    // 角色加入
  @HiveField(1)
  characterLeft,      // 角色离开
  @HiveField(2)
  topicChanged,       // 话题改变
  @HiveField(3)
  conflictStarted,    // 冲突开始
  @HiveField(4)
  conflictResolved,   // 冲突解决
  @HiveField(5)
  allianceFormed,     // 联盟形成
  @HiveField(6)
  emotionalMoment,    // 情感时刻
  @HiveField(7)
  groupActivity,      // 群体活动
}

@freezed
@HiveType(typeId: 26)
class ConversationDynamics with _$ConversationDynamics {
  const factory ConversationDynamics({
    @HiveField(0) required String groupConversationId,
    @HiveField(1) required String userId,
    @HiveField(2) required DateTime analysisTime,
    @HiveField(3) required Map<String, double> characterParticipation,
    @HiveField(4) required Map<String, double> characterInfluence,
    @HiveField(5) required Map<String, List<String>> characterInteractions,
    @HiveField(6) required double groupCohesion,
    @HiveField(7) required double conversationEnergy,
    @HiveField(8) required List<String> dominantTopics,
    @HiveField(9) required Map<String, double> emotionalTone,
    @HiveField(10) String? currentMood,
    @HiveField(11) Map<String, dynamic>? insights,
  }) = _ConversationDynamics;

  factory ConversationDynamics.fromJson(Map<String, dynamic> json) => _$ConversationDynamicsFromJson(json);
}
