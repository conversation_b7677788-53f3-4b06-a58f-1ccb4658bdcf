import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'group_conversation.freezed.dart';
part 'group_conversation.g.dart';

@freezed
@HiveType(typeId: 142)
class GroupConversation with _$GroupConversation {
  const factory GroupConversation({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required String description,
    @HiveField(3) required List<String> participantIds,
    @HiveField(4) required String creatorId,
    @HiveField(5) required GroupType type,
    @HiveField(6) @Default(GroupStatus.active) GroupStatus status,
    @HiveField(7) required DateTime createdAt,
    @HiveField(8) DateTime? lastActivity,
    @HiveField(9) @Default([]) List<String> messageIds,
    @HiveField(10) @Default({}) Map<String, GroupRole> memberRoles,
    @HiveField(11) @Default({}) Map<String, DateTime> joinTimes,
    @HiveField(12) @Default({}) Map<String, DateTime> lastSeenTimes,
    @HiveField(13) @Default(GroupSettings()) GroupSettings settings,
    @HiveField(14) @Default([]) List<String> tags,
    @HiveField(15) String? avatarUrl,
    @HiveField(16) @Default({}) Map<String, dynamic> metadata,
    @HiveField(17) @Default(0) int messageCount,
    @HiveField(18) @Default([]) List<GroupEvent> events,
  }) = _GroupConversation;

  factory GroupConversation.fromJson(Map<String, dynamic> json) => _$GroupConversationFromJson(json);
}

@HiveType(typeId: 143)
enum GroupType {
  @HiveField(0)
  casual,           // 休闲聊天
  @HiveField(1)
  roleplay,         // 角色扮演
  @HiveField(2)
  debate,           // 辩论讨论
  @HiveField(3)
  support,          // 支持群组
  @HiveField(4)
  learning,         // 学习交流
  @HiveField(5)
  gaming,           // 游戏互动
  @HiveField(6)
  creative,         // 创意协作
  @HiveField(7)
  therapy,          // 心理治疗
  @HiveField(8)
  social,           // 社交聚会
  @HiveField(9)
  professional,     // 专业讨论
}

@HiveType(typeId: 144)
enum GroupStatus {
  @HiveField(0)
  active,           // 活跃
  @HiveField(1)
  paused,           // 暂停
  @HiveField(2)
  archived,         // 已归档
  @HiveField(3)
  private,          // 私有
  @HiveField(4)
  restricted,       // 受限
}

@HiveType(typeId: 145)
enum GroupRole {
  @HiveField(0)
  owner,            // 群主
  @HiveField(1)
  admin,            // 管理员
  @HiveField(2)
  moderator,        // 版主
  @HiveField(3)
  member,           // 成员
  @HiveField(4)
  observer,         // 观察者
  @HiveField(5)
  guest,            // 访客
}

@freezed
@HiveType(typeId: 146)
class GroupSettings with _$GroupSettings {
  const factory GroupSettings({
    @HiveField(0) @Default(true) bool allowNewMembers,
    @HiveField(1) @Default(false) bool requireApproval,
    @HiveField(2) @Default(true) bool allowMemberInvites,
    @HiveField(3) @Default(10) int maxMembers,
    @HiveField(4) @Default(true) bool enableNotifications,
    @HiveField(5) @Default(false) bool muteAll,
    @HiveField(6) @Default([]) List<String> bannedWords,
    @HiveField(7) @Default(MessageFilter()) MessageFilter messageFilter,
    @HiveField(8) @Default({}) Map<String, bool> permissions,
    @HiveField(9) @Default(false) bool autoModeration,
    @HiveField(10) @Default(ConversationTheme.default_) ConversationTheme theme,
  }) = _GroupSettings;

  factory GroupSettings.fromJson(Map<String, dynamic> json) => _$GroupSettingsFromJson(json);
}

@freezed
@HiveType(typeId: 147)
class MessageFilter with _$MessageFilter {
  const factory MessageFilter({
    @HiveField(0) @Default(true) bool allowText,
    @HiveField(1) @Default(true) bool allowEmoji,
    @HiveField(2) @Default(false) bool allowImages,
    @HiveField(3) @Default(false) bool allowFiles,
    @HiveField(4) @Default(false) bool allowLinks,
    @HiveField(5) @Default(true) bool enableSpamFilter,
    @HiveField(6) @Default(100) int maxMessageLength,
    @HiveField(7) @Default(5) int maxMessagesPerMinute,
  }) = _MessageFilter;

  factory MessageFilter.fromJson(Map<String, dynamic> json) => _$MessageFilterFromJson(json);
}

@HiveType(typeId: 148)
enum ConversationTheme {
  @HiveField(0)
  default_,         // 默认主题
  @HiveField(1)
  dark,             // 深色主题
  @HiveField(2)
  colorful,         // 彩色主题
  @HiveField(3)
  minimal,          // 简约主题
  @HiveField(4)
  fantasy,          // 奇幻主题
  @HiveField(5)
  scifi,            // 科幻主题
  @HiveField(6)
  nature,           // 自然主题
}

@freezed
@HiveType(typeId: 149)
class GroupEvent with _$GroupEvent {
  const factory GroupEvent({
    @HiveField(0) required String id,
    @HiveField(1) required String groupId,
    @HiveField(2) required GroupEventType type,
    @HiveField(3) required DateTime timestamp,
    @HiveField(4) String? actorId,
    @HiveField(5) String? targetId,
    @HiveField(6) @Default({}) Map<String, dynamic> data,
    @HiveField(7) String? description,
    @HiveField(8) @Default(false) bool isSystemEvent,
  }) = _GroupEvent;

  factory GroupEvent.fromJson(Map<String, dynamic> json) => _$GroupEventFromJson(json);
}

@HiveType(typeId: 150)
enum GroupEventType {
  @HiveField(0)
  memberJoined,     // 成员加入
  @HiveField(1)
  memberLeft,       // 成员离开
  @HiveField(2)
  memberKicked,     // 成员被踢
  @HiveField(3)
  memberBanned,     // 成员被禁
  @HiveField(4)
  roleChanged,      // 角色变更
  @HiveField(5)
  settingsChanged,  // 设置变更
  @HiveField(6)
  nameChanged,      // 群名变更
  @HiveField(7)
  avatarChanged,    // 头像变更
  @HiveField(8)
  messageDeleted,   // 消息删除
  @HiveField(9)
  conversationStarted, // 对话开始
  @HiveField(10)
  conversationEnded,   // 对话结束
  @HiveField(11)
  topicChanged,     // 话题变更
  @HiveField(12)
  moodShift,        // 氛围转变
}

@freezed
@HiveType(typeId: 151)
class ConversationScenario with _$ConversationScenario {
  const factory ConversationScenario({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required String description,
    @HiveField(3) required ScenarioType type,
    @HiveField(4) required List<String> requiredCharacters,
    @HiveField(5) @Default([]) List<String> optionalCharacters,
    @HiveField(6) required List<ScenarioStep> steps,
    @HiveField(7) @Default({}) Map<String, dynamic> initialContext,
    @HiveField(8) @Default([]) List<ScenarioTrigger> triggers,
    @HiveField(9) @Default([]) List<String> tags,
    @HiveField(10) @Default(1.0) double difficulty,
    @HiveField(11) @Default(30) int estimatedDuration,
    @HiveField(12) @Default({}) Map<String, dynamic> rewards,
    @HiveField(13) @Default([]) List<String> prerequisites,
    @HiveField(14) required DateTime createdAt,
    @HiveField(15) @Default(0) int usageCount,
    @HiveField(16) @Default(0.0) double averageRating,
    @HiveField(17) @Default(true) bool isActive,
  }) = _ConversationScenario;

  factory ConversationScenario.fromJson(Map<String, dynamic> json) => _$ConversationScenarioFromJson(json);
}

@HiveType(typeId: 152)
enum ScenarioType {
  @HiveField(0)
  icebreaker,       // 破冰对话
  @HiveField(1)
  conflict,         // 冲突解决
  @HiveField(2)
  celebration,      // 庆祝活动
  @HiveField(3)
  problemSolving,   // 问题解决
  @HiveField(4)
  storytelling,     // 故事讲述
  @HiveField(5)
  debate,           // 辩论讨论
  @HiveField(6)
  support,          // 情感支持
  @HiveField(7)
  learning,         // 学习交流
  @HiveField(8)
  creative,         // 创意协作
  @HiveField(9)
  social,           // 社交互动
  @HiveField(10)
  therapeutic,      // 治疗性对话
  @HiveField(11)
  competitive,      // 竞争性活动
}

@freezed
@HiveType(typeId: 153)
class ScenarioStep with _$ScenarioStep {
  const factory ScenarioStep({
    @HiveField(0) required int order,
    @HiveField(1) required StepType type,
    @HiveField(2) String? characterId,
    @HiveField(3) required String content,
    @HiveField(4) @Default({}) Map<String, dynamic> parameters,
    @HiveField(5) @Default([]) List<String> conditions,
    @HiveField(6) @Default([]) List<StepBranch> branches,
    @HiveField(7) @Default(0) int minDelay,
    @HiveField(8) @Default(0) int maxDelay,
    @HiveField(9) @Default(1.0) double probability,
    @HiveField(10) @Default(false) bool isOptional,
    @HiveField(11) String? description,
  }) = _ScenarioStep;

  factory ScenarioStep.fromJson(Map<String, dynamic> json) => _$ScenarioStepFromJson(json);
}

@HiveType(typeId: 154)
enum StepType {
  @HiveField(0)
  introduction,     // 介绍
  @HiveField(1)
  question,         // 提问
  @HiveField(2)
  response,         // 回应
  @HiveField(3)
  action,           // 行动
  @HiveField(4)
  emotion,          // 情感表达
  @HiveField(5)
  pause,            // 暂停
  @HiveField(6)
  transition,       // 过渡
  @HiveField(7)
  conclusion,       // 结论
  @HiveField(8)
  choice,           // 选择
  @HiveField(9)
  reaction,         // 反应
  @HiveField(10)
  narration,        // 旁白
}

@freezed
@HiveType(typeId: 155)
class StepBranch with _$StepBranch {
  const factory StepBranch({
    @HiveField(0) required String id,
    @HiveField(1) required String condition,
    @HiveField(2) required int targetStep,
    @HiveField(3) @Default(1.0) double probability,
    @HiveField(4) String? description,
  }) = _StepBranch;

  factory StepBranch.fromJson(Map<String, dynamic> json) => _$StepBranchFromJson(json);
}

@freezed
@HiveType(typeId: 156)
class ScenarioTrigger with _$ScenarioTrigger {
  const factory ScenarioTrigger({
    @HiveField(0) required String id,
    @HiveField(1) required TriggerType type,
    @HiveField(2) required Map<String, dynamic> conditions,
    @HiveField(3) @Default(1.0) double probability,
    @HiveField(4) String? description,
  }) = _ScenarioTrigger;

  factory ScenarioTrigger.fromJson(Map<String, dynamic> json) => _$ScenarioTriggerFromJson(json);
}

@HiveType(typeId: 157)
enum TriggerType {
  @HiveField(0)
  timeBasedTrigger,     // 时间触发
  @HiveField(1)
  eventBasedTrigger,    // 事件触发
  @HiveField(2)
  emotionBasedTrigger,  // 情感触发
  @HiveField(3)
  contextBasedTrigger,  // 上下文触发
  @HiveField(4)
  userActionTrigger,    // 用户行为触发
  @HiveField(5)
  randomTrigger,        // 随机触发
}

@freezed
@HiveType(typeId: 158)
class ScenarioExecution with _$ScenarioExecution {
  const factory ScenarioExecution({
    @HiveField(0) required String id,
    @HiveField(1) required String scenarioId,
    @HiveField(2) required String groupId,
    @HiveField(3) required DateTime startTime,
    @HiveField(4) DateTime? endTime,
    @HiveField(5) @Default(ExecutionStatus.running) ExecutionStatus status,
    @HiveField(6) @Default(0) int currentStep,
    @HiveField(7) @Default({}) Map<String, dynamic> context,
    @HiveField(8) @Default([]) List<ExecutionEvent> events,
    @HiveField(9) @Default({}) Map<String, dynamic> results,
    @HiveField(10) double? userRating,
    @HiveField(11) String? feedback,
    @HiveField(12) @Default({}) Map<String, int> participantContributions,
  }) = _ScenarioExecution;

  factory ScenarioExecution.fromJson(Map<String, dynamic> json) => _$ScenarioExecutionFromJson(json);
}

@HiveType(typeId: 159)
enum ExecutionStatus {
  @HiveField(0)
  pending,          // 等待中
  @HiveField(1)
  running,          // 执行中
  @HiveField(2)
  paused,           // 暂停
  @HiveField(3)
  completed,        // 完成
  @HiveField(4)
  failed,           // 失败
  @HiveField(5)
  cancelled,        // 取消
}

@freezed
@HiveType(typeId: 160)
class ExecutionEvent with _$ExecutionEvent {
  const factory ExecutionEvent({
    @HiveField(0) required String id,
    @HiveField(1) required String executionId,
    @HiveField(2) required ExecutionEventType type,
    @HiveField(3) required DateTime timestamp,
    @HiveField(4) @Default({}) Map<String, dynamic> data,
    @HiveField(5) String? description,
  }) = _ExecutionEvent;

  factory ExecutionEvent.fromJson(Map<String, dynamic> json) => _$ExecutionEventFromJson(json);
}

@HiveType(typeId: 161)
enum ExecutionEventType {
  @HiveField(0)
  stepStarted,      // 步骤开始
  @HiveField(1)
  stepCompleted,    // 步骤完成
  @HiveField(2)
  stepSkipped,      // 步骤跳过
  @HiveField(3)
  branchTaken,      // 分支选择
  @HiveField(4)
  participantJoined, // 参与者加入
  @HiveField(5)
  participantLeft,   // 参与者离开
  @HiveField(6)
  contextChanged,    // 上下文变化
  @HiveField(7)
  errorOccurred,     // 错误发生
}

@freezed
@HiveType(typeId: 162)
class GroupAnalytics with _$GroupAnalytics {
  const factory GroupAnalytics({
    @HiveField(0) required String groupId,
    @HiveField(1) required DateTime date,
    @HiveField(2) @Default(0) int totalMessages,
    @HiveField(3) @Default(0) int activeMembers,
    @HiveField(4) @Default(0.0) double averageResponseTime,
    @HiveField(5) @Default({}) Map<String, int> memberContributions,
    @HiveField(6) @Default({}) Map<String, double> emotionalTones,
    @HiveField(7) @Default([]) List<String> popularTopics,
    @HiveField(8) @Default(0.0) double engagementScore,
    @HiveField(9) @Default(0.0) double satisfactionScore,
    @HiveField(10) @Default({}) Map<String, dynamic> insights,
  }) = _GroupAnalytics;

  factory GroupAnalytics.fromJson(Map<String, dynamic> json) => _$GroupAnalyticsFromJson(json);
}

@freezed
@HiveType(typeId: 163)
class ConversationModerator with _$ConversationModerator {
  const factory ConversationModerator({
    @HiveField(0) required String id,
    @HiveField(1) required String groupId,
    @HiveField(2) required ModeratorType type,
    @HiveField(3) @Default(true) bool isActive,
    @HiveField(4) @Default({}) Map<String, dynamic> settings,
    @HiveField(5) @Default([]) List<ModerationRule> rules,
    @HiveField(6) @Default(0) int actionsCount,
    @HiveField(7) DateTime? lastAction,
  }) = _ConversationModerator;

  factory ConversationModerator.fromJson(Map<String, dynamic> json) => _$ConversationModeratorFromJson(json);
}

@HiveType(typeId: 164)
enum ModeratorType {
  @HiveField(0)
  aiModerator,      // AI版主
  @HiveField(1)
  humanModerator,   // 人类版主
  @HiveField(2)
  hybridModerator,  // 混合版主
}

@freezed
@HiveType(typeId: 165)
class ModerationRule with _$ModerationRule {
  const factory ModerationRule({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required RuleType type,
    @HiveField(3) required List<String> conditions,
    @HiveField(4) required ModerationAction action,
    @HiveField(5) @Default(1.0) double severity,
    @HiveField(6) @Default(true) bool isActive,
    @HiveField(7) String? description,
  }) = _ModerationRule;

  factory ModerationRule.fromJson(Map<String, dynamic> json) => _$ModerationRuleFromJson(json);
}

@HiveType(typeId: 166)
enum RuleType {
  @HiveField(0)
  contentFilter,    // 内容过滤
  @HiveField(1)
  behaviorMonitor,  // 行为监控
  @HiveField(2)
  spamDetection,    // 垃圾信息检测
  @HiveField(3)
  toxicityFilter,   // 毒性内容过滤
  @HiveField(4)
  topicGuide,       // 话题引导
}

@HiveType(typeId: 167)
enum ModerationAction {
  @HiveField(0)
  warn,             // 警告
  @HiveField(1)
  mute,             // 禁言
  @HiveField(2)
  kick,             // 踢出
  @HiveField(3)
  ban,              // 封禁
  @HiveField(4)
  deleteMessage,    // 删除消息
  @HiveField(5)
  redirectTopic,    // 重定向话题
  @HiveField(6)
  suggestBreak,     // 建议休息
}

@freezed
@HiveType(typeId: 19)
class GroupConversation with _$GroupConversation {
  const factory GroupConversation({
    @HiveField(0) required String id,
    @HiveField(1) required String userId,
    @HiveField(2) required String title,
    @HiveField(3) required List<String> characterIds,
    @HiveField(4) required DateTime createdAt,
    @HiveField(5) required DateTime updatedAt,
    @HiveField(6) @Default([]) List<String> messageIds,
    @HiveField(7) @Default(0) int messageCount,
    @HiveField(8) String? lastMessageId,
    @HiveField(9) String? lastMessagePreview,
    @HiveField(10) @Default(false) bool isActive,
    @HiveField(11) @Default({}) Map<String, int> characterMessageCounts,
    @HiveField(12) @Default({}) Map<String, DateTime> lastCharacterActivity,
    @HiveField(13) GroupConversationType? type,
    @HiveField(14) Map<String, dynamic>? settings,
  }) = _GroupConversation;

  factory GroupConversation.fromJson(Map<String, dynamic> json) => _$GroupConversationFromJson(json);
}

@HiveType(typeId: 20)
enum GroupConversationType {
  @HiveField(0)
  casual,      // 随意聊天
  @HiveField(1)
  debate,      // 辩论讨论
  @HiveField(2)
  roleplay,    // 角色扮演
  @HiveField(3)
  support,     // 情感支持
  @HiveField(4)
  learning,    // 学习交流
}

@freezed
@HiveType(typeId: 21)
class GroupMessage with _$GroupMessage {
  const factory GroupMessage({
    @HiveField(0) required String id,
    @HiveField(1) required String groupConversationId,
    @HiveField(2) required String content,
    @HiveField(3) required MessageType type,
    @HiveField(4) required MessageSender sender,
    @HiveField(5) required DateTime timestamp,
    @HiveField(6) @Default(MessageStatus.sent) MessageStatus status,
    @HiveField(7) String? characterId,
    @HiveField(8) String? replyToMessageId,
    @HiveField(9) @Default([]) List<String> mentionedCharacterIds,
    @HiveField(10) @Default(false) bool isSystemMessage,
    @HiveField(11) Map<String, dynamic>? metadata,
  }) = _GroupMessage;

  factory GroupMessage.fromJson(Map<String, dynamic> json) => _$GroupMessageFromJson(json);
}

@freezed
@HiveType(typeId: 22)
class CharacterRelationship with _$CharacterRelationship {
  const factory CharacterRelationship({
    @HiveField(0) required String id,
    @HiveField(1) required String character1Id,
    @HiveField(2) required String character2Id,
    @HiveField(3) required String userId,
    @HiveField(4) required RelationshipType type,
    @HiveField(5) required double strength,
    @HiveField(6) required DateTime establishedAt,
    @HiveField(7) required DateTime lastInteraction,
    @HiveField(8) @Default(0) int interactionCount,
    @HiveField(9) @Default([]) List<String> sharedMemoryIds,
    @HiveField(10) @Default({}) Map<String, double> emotionalDynamics,
    @HiveField(11) String? description,
    @HiveField(12) Map<String, dynamic>? attributes,
  }) = _CharacterRelationship;

  factory CharacterRelationship.fromJson(Map<String, dynamic> json) => _$CharacterRelationshipFromJson(json);
}

@HiveType(typeId: 23)
enum RelationshipType {
  @HiveField(0)
  neutral,     // 中性
  @HiveField(1)
  friendly,    // 友好
  @HiveField(2)
  competitive, // 竞争
  @HiveField(3)
  romantic,    // 浪漫
  @HiveField(4)
  mentor,      // 师生
  @HiveField(5)
  rival,       // 对手
  @HiveField(6)
  family,      // 家人
}

@freezed
@HiveType(typeId: 24)
class GroupInteractionEvent with _$GroupInteractionEvent {
  const factory GroupInteractionEvent({
    @HiveField(0) required String id,
    @HiveField(1) required String groupConversationId,
    @HiveField(2) required String userId,
    @HiveField(3) required InteractionEventType type,
    @HiveField(4) required DateTime timestamp,
    @HiveField(5) required List<String> involvedCharacterIds,
    @HiveField(6) required String description,
    @HiveField(7) @Default({}) Map<String, dynamic> eventData,
    @HiveField(8) @Default(1.0) double impact,
    @HiveField(9) String? triggerMessageId,
  }) = _GroupInteractionEvent;

  factory GroupInteractionEvent.fromJson(Map<String, dynamic> json) => _$GroupInteractionEventFromJson(json);
}

@HiveType(typeId: 25)
enum InteractionEventType {
  @HiveField(0)
  characterJoined,    // 角色加入
  @HiveField(1)
  characterLeft,      // 角色离开
  @HiveField(2)
  topicChanged,       // 话题改变
  @HiveField(3)
  conflictStarted,    // 冲突开始
  @HiveField(4)
  conflictResolved,   // 冲突解决
  @HiveField(5)
  allianceFormed,     // 联盟形成
  @HiveField(6)
  emotionalMoment,    // 情感时刻
  @HiveField(7)
  groupActivity,      // 群体活动
}

@freezed
@HiveType(typeId: 26)
class ConversationDynamics with _$ConversationDynamics {
  const factory ConversationDynamics({
    @HiveField(0) required String groupConversationId,
    @HiveField(1) required String userId,
    @HiveField(2) required DateTime analysisTime,
    @HiveField(3) required Map<String, double> characterParticipation,
    @HiveField(4) required Map<String, double> characterInfluence,
    @HiveField(5) required Map<String, List<String>> characterInteractions,
    @HiveField(6) required double groupCohesion,
    @HiveField(7) required double conversationEnergy,
    @HiveField(8) required List<String> dominantTopics,
    @HiveField(9) required Map<String, double> emotionalTone,
    @HiveField(10) String? currentMood,
    @HiveField(11) Map<String, dynamic>? insights,
  }) = _ConversationDynamics;

  factory ConversationDynamics.fromJson(Map<String, dynamic> json) => _$ConversationDynamicsFromJson(json);
}
