import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'memory.freezed.dart';
part 'memory.g.dart';

@freezed
@HiveType(typeId: 9)
class Memory with _$Memory {
  const factory Memory({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required String userId,
    @HiveField(3) required MemoryType type,
    @HiveField(4) required String content,
    @HiveField(5) required String summary,
    @HiveField(6) required DateTime timestamp,
    @HiveField(7) @Default(1.0) double importance,
    @HiveField(8) @Default([]) List<String> tags,
    @HiveField(9) @Default([]) List<String> relatedMemoryIds,
    @HiveField(10) String? conversationId,
    @HiveField(11) String? messageId,
    @HiveField(12) @Default(0) int accessCount,
    @HiveField(13) DateTime? lastAccessed,
    @HiveField(14) Map<String, dynamic>? metadata,
  }) = _Memory;

  factory Memory.fromJson(Map<String, dynamic> json) => _$MemoryFromJson(json);
}

@HiveType(typeId: 10)
enum MemoryType {
  @HiveField(0)
  conversation, // 对话记忆
  @HiveField(1)
  personal, // 个人信息
  @HiveField(2)
  preference, // 偏好记忆
  @HiveField(3)
  emotion, // 情感记忆
  @HiveField(4)
  event, // 事件记忆
  @HiveField(5)
  relationship, // 关系记忆
  @HiveField(6)
  habit, // 习惯记忆
  @HiveField(7)
  goal, // 目标记忆
}

@freezed
@HiveType(typeId: 11)
class MemoryCluster with _$MemoryCluster {
  const factory MemoryCluster({
    @HiveField(0) required String id,
    @HiveField(1) required String characterId,
    @HiveField(2) required String userId,
    @HiveField(3) required String topic,
    @HiveField(4) required List<String> memoryIds,
    @HiveField(5) required DateTime createdAt,
    @HiveField(6) required DateTime updatedAt,
    @HiveField(7) @Default(1.0) double relevanceScore,
    @HiveField(8) @Default([]) List<String> keywords,
    @HiveField(9) String? summary,
  }) = _MemoryCluster;

  factory MemoryCluster.fromJson(Map<String, dynamic> json) => _$MemoryClusterFromJson(json);
}

@freezed
@HiveType(typeId: 12)
class UserProfile with _$UserProfile {
  const factory UserProfile({
    @HiveField(0) required String userId,
    @HiveField(1) required String name,
    @HiveField(2) String? age,
    @HiveField(3) String? occupation,
    @HiveField(4) String? location,
    @HiveField(5) @Default([]) List<String> interests,
    @HiveField(6) @Default([]) List<String> preferences,
    @HiveField(7) @Default({}) Map<String, String> personalInfo,
    @HiveField(8) DateTime? lastUpdated,
    @HiveField(9) @Default(0) int interactionCount,
  }) = _UserProfile;

  factory UserProfile.fromJson(Map<String, dynamic> json) => _$UserProfileFromJson(json);
}
